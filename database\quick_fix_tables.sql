-- Quick fix for missing tables - run this in Supabase SQL Editor
-- This creates the tables without foreign key constraints for immediate functionality

-- User Settings Table (simplified for demo)
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    theme TEXT NOT NULL DEFAULT 'system',
    language TEXT NOT NULL DEFAULT 'en',
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    notifications JSONB NOT NULL DEFAULT '{
        "email": true,
        "push": false,
        "marketing": true
    }',
    privacy JSONB NOT NULL DEFAULT '{
        "analytics": true,
        "data_collection": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications Table (simplified for demo)
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info',
    read B<PERSON><PERSON><PERSON>N NOT NULL DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')
);

-- Saved Keywords Table (simplified for demo)
CREATE TABLE IF NOT EXISTS saved_keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    keyword TEXT NOT NULL,
    volume TEXT,
    difficulty INTEGER,
    cpc TEXT,
    trend TEXT,
    competition TEXT,
    intent TEXT,
    serp_features JSONB DEFAULT '[]',
    related_keywords JSONB DEFAULT '[]',
    questions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add basic indexes
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_saved_keywords_user_id ON saved_keywords(user_id);

-- Enable RLS and create permissive policies for demo
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_keywords ENABLE ROW LEVEL SECURITY;

-- Allow all operations for demo (replace with proper user-based policies in production)
CREATE POLICY "Allow all operations on user_settings" ON user_settings FOR ALL USING (true);
CREATE POLICY "Allow all operations on notifications" ON notifications FOR ALL USING (true);
CREATE POLICY "Allow all operations on saved_keywords" ON saved_keywords FOR ALL USING (true);

-- Insert some sample data for testing
INSERT INTO notifications (title, message, type) VALUES 
('Welcome!', 'Welcome to MarketCrawler Pro', 'info'),
('Feature Update', 'New keyword research features are now available', 'success')
ON CONFLICT DO NOTHING;
