import { apiService, SupabaseService, ApiResponse } from './apiService';

export interface KeywordData {
  id: string;
  keyword: string;
  volume: string;
  difficulty: number;
  cpc: string;
  competition: 'Low' | 'Medium' | 'High';
  intent: 'Commercial' | 'Informational' | 'Navigational' | 'Transactional';
  trend: 'up' | 'down' | 'stable';
  related_keywords: string[];
  questions: string[];
  serp_features: string[];
  created_at: string;
  updated_at: string;
  saved?: boolean;
}

export interface KeywordSearchParams {
  seed_keyword: string;
  location?: string;
  language?: string;
  search_engine?: 'google' | 'bing' | 'yahoo';
  include_questions?: boolean;
  include_related?: boolean;
  min_volume?: number;
  max_difficulty?: number;
}

export interface TrendingKeyword {
  keyword: string;
  growth_rate: number;
  volume: string;
  category: string;
  region: string;
  time_period: string;
}

class KeywordService {
  async searchKeywords(params: KeywordSearchParams): Promise<ApiResponse<KeywordData[]>> {
    try {
      // Validate input
      if (!params.seed_keyword || params.seed_keyword.trim().length === 0) {
        return {
          success: false,
          error: 'Seed keyword is required'
        };
      }

      // In a real implementation, this would call external APIs like:
      // - Google Keyword Planner API
      // - SEMrush API
      // - Ahrefs API
      // - Moz API

      // For demo purposes, generate mock data
      const mockKeywords = await this.generateMockKeywords(params.seed_keyword);

      // Save to database
      const savedKeywords: KeywordData[] = [];
      for (const keyword of mockKeywords) {
        const result = await SupabaseService.insert<KeywordData>('keywords', keyword);
        if (result.success && result.data) {
          savedKeywords.push(result.data);
        }
      }

      return {
        success: true,
        data: savedKeywords
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Keyword search failed'
      };
    }
  }

  async getKeywords(filters?: {
    search?: string;
    intent?: string;
    competition?: string;
    saved_only?: boolean;
    limit?: number;
  }): Promise<ApiResponse<KeywordData[]>> {
    try {
      const queryFilters: Record<string, any> = {};

      if (filters?.intent) {
        queryFilters.intent = filters.intent;
      }

      if (filters?.competition) {
        queryFilters.competition = filters.competition;
      }

      if (filters?.saved_only) {
        queryFilters.saved = true;
      }

      let result = await SupabaseService.query<KeywordData>('keywords', {
        filters: queryFilters,
        orderBy: 'created_at',
        orderDirection: 'desc',
        limit: filters?.limit || 100
      });

      if (result.success && result.data && filters?.search) {
        // Filter by search term
        result.data = result.data.filter(keyword =>
          keyword.keyword.toLowerCase().includes(filters.search!.toLowerCase())
        );
      }

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch keywords'
      };
    }
  }

  async saveKeyword(keywordId: string): Promise<ApiResponse<KeywordData>> {
    return SupabaseService.update<KeywordData>('keywords', keywordId, {
      saved: true,
      updated_at: new Date().toISOString()
    });
  }

  async unsaveKeyword(keywordId: string): Promise<ApiResponse<KeywordData>> {
    return SupabaseService.update<KeywordData>('keywords', keywordId, {
      saved: false,
      updated_at: new Date().toISOString()
    });
  }

  async deleteKeyword(keywordId: string): Promise<ApiResponse<void>> {
    return SupabaseService.delete('keywords', keywordId);
  }

  async getTrendingKeywords(params?: {
    category?: string;
    region?: string;
    time_period?: string;
  }): Promise<ApiResponse<TrendingKeyword[]>> {
    try {
      // In a real implementation, this would fetch from Google Trends API
      // or other trending keyword services

      const mockTrending: TrendingKeyword[] = [
        {
          keyword: 'AI automation',
          growth_rate: 156,
          volume: '45K',
          category: 'Technology',
          region: 'Global',
          time_period: 'Last 30 days'
        },
        {
          keyword: 'sustainable fashion',
          growth_rate: 89,
          volume: '23K',
          category: 'Fashion',
          region: 'US',
          time_period: 'Last 30 days'
        },
        {
          keyword: 'remote work tools',
          growth_rate: 67,
          volume: '67K',
          category: 'Business',
          region: 'Global',
          time_period: 'Last 30 days'
        }
      ];

      return {
        success: true,
        data: mockTrending
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch trending keywords'
      };
    }
  }

  async getKeywordSuggestions(seed: string): Promise<ApiResponse<string[]>> {
    try {
      // Generate suggestions based on seed keyword
      const suggestions = [
        `${seed} tools`,
        `${seed} software`,
        `${seed} guide`,
        `${seed} tutorial`,
        `${seed} tips`,
        `${seed} best practices`,
        `${seed} comparison`,
        `${seed} review`,
        `${seed} pricing`,
        `${seed} alternatives`,
        `how to ${seed}`,
        `what is ${seed}`,
        `${seed} vs`,
        `${seed} benefits`,
        `${seed} features`
      ];

      return {
        success: true,
        data: suggestions
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate suggestions'
      };
    }
  }

  async exportKeywords(keywordIds: string[], format: 'csv' | 'json' | 'xlsx' = 'csv'): Promise<ApiResponse<{ downloadUrl: string }>> {
    try {
      const keywords: KeywordData[] = [];
      
      for (const id of keywordIds) {
        const result = await SupabaseService.query<KeywordData>('keywords', {
          filters: { id }
        });
        
        if (result.success && result.data && result.data.length > 0) {
          keywords.push(result.data[0]);
        }
      }

      // In a real implementation, this would generate the file and upload to storage
      const mockDownloadUrl = `/exports/keywords-${Date.now()}.${format}`;

      return {
        success: true,
        data: { downloadUrl: mockDownloadUrl }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Export failed'
      };
    }
  }

  async getKeywordStats(): Promise<ApiResponse<{
    total: number;
    saved: number;
    by_intent: Record<string, number>;
    by_competition: Record<string, number>;
    avg_difficulty: number;
  }>> {
    try {
      const result = await SupabaseService.query<KeywordData>('keywords', {});
      
      if (!result.success || !result.data) {
        return { success: false, error: 'Failed to fetch keyword stats' };
      }

      const keywords = result.data;
      const stats = {
        total: keywords.length,
        saved: keywords.filter(k => k.saved).length,
        by_intent: {} as Record<string, number>,
        by_competition: {} as Record<string, number>,
        avg_difficulty: 0
      };

      // Calculate stats
      keywords.forEach(keyword => {
        stats.by_intent[keyword.intent] = (stats.by_intent[keyword.intent] || 0) + 1;
        stats.by_competition[keyword.competition] = (stats.by_competition[keyword.competition] || 0) + 1;
      });

      if (keywords.length > 0) {
        stats.avg_difficulty = keywords.reduce((sum, k) => sum + k.difficulty, 0) / keywords.length;
      }

      return { success: true, data: stats };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get keyword stats'
      };
    }
  }

  private async generateMockKeywords(seed: string): Promise<Partial<KeywordData>[]> {
    const variations = [
      seed,
      `${seed} tools`,
      `${seed} software`,
      `${seed} guide`,
      `${seed} tutorial`,
      `best ${seed}`,
      `${seed} tips`,
      `${seed} review`,
      `${seed} comparison`,
      `${seed} pricing`
    ];

    const intents: KeywordData['intent'][] = ['Commercial', 'Informational', 'Navigational', 'Transactional'];
    const competitions: KeywordData['competition'][] = ['Low', 'Medium', 'High'];
    const trends: KeywordData['trend'][] = ['up', 'down', 'stable'];

    return variations.map((keyword, index) => ({
      keyword,
      volume: `${Math.floor(Math.random() * 50000) + 1000}`,
      difficulty: Math.floor(Math.random() * 100),
      cpc: `$${(Math.random() * 10).toFixed(2)}`,
      competition: competitions[Math.floor(Math.random() * competitions.length)],
      intent: intents[Math.floor(Math.random() * intents.length)],
      trend: trends[Math.floor(Math.random() * trends.length)],
      related_keywords: [`related ${keyword}`, `${keyword} alternative`],
      questions: [`What is ${keyword}?`, `How to use ${keyword}?`],
      serp_features: ['Featured Snippet', 'People Also Ask'],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      saved: false
    }));
  }
}

export const keywordService = new KeywordService();
export default keywordService;
