# Database Setup Guide

This guide explains how to set up the Supabase database for the Scraping Analysis Marketing application.

## Prerequisites

1. **Supabase Account**: Create a free account at [supabase.com](https://supabase.com)
2. **Project Setup**: Create a new Supabase project
3. **Environment Variables**: Update your `.env` file with the correct Supabase credentials

## Database Schema Overview

The application uses the following main tables:

### Core Tables
- **`scraping_jobs`** - Tracks web scraping jobs and their status
- **`scraped_data`** - Stores the actual scraped content and metadata
- **`seo_analysis`** - SEO analysis results for scraped pages
- **`keywords`** - Extracted keywords with frequency and context
- **`leads`** - Contact information extracted from scraped content
- **`content_ideas`** - Generated content ideas based on scraped data
- **`social_posts`** - Generated social media posts

### Key Features
- **UUID Primary Keys** for all tables
- **Foreign Key Relationships** to maintain data integrity
- **JSONB Columns** for flexible metadata storage
- **Full-Text Search** indexes for content searching
- **Row Level Security (RLS)** enabled for all tables
- **Automatic Triggers** for word count and content hashing
- **Performance Indexes** for common queries

## Setup Instructions

### Step 1: Access Supabase SQL Editor

1. Go to your Supabase project dashboard
2. Navigate to the **SQL Editor** in the left sidebar
3. Create a new query

### Step 2: Run the Schema Script

1. Copy the contents of `database/schema.sql`
2. Paste it into the SQL Editor
3. Click **Run** to execute the script

The script will:
- Create all necessary tables with proper relationships
- Set up indexes for optimal performance
- Create triggers for automatic data processing
- Enable Row Level Security with permissive policies
- Add sample data for testing

### Step 3: Verify the Setup

After running the schema script, verify that all tables were created:

```sql
-- Check if all tables exist
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

You should see these tables:
- `content_ideas`
- `keywords`
- `leads`
- `scraped_data`
- `scraping_jobs`
- `seo_analysis`
- `social_posts`

### Step 4: Test the Connection

Run a simple query to test the connection:

```sql
-- Test query
SELECT COUNT(*) as total_jobs FROM scraping_jobs;
```

## Environment Configuration

Make sure your `.env` file contains the correct Supabase credentials:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
VITE_SUPABASE_FUNCTIONS_URL=https://your-project-id.supabase.co/functions/v1
```

You can find these values in your Supabase project settings:
1. Go to **Settings** → **API**
2. Copy the **Project URL** and **anon/public key**

## Row Level Security (RLS)

The current setup uses permissive RLS policies that allow all operations. This is suitable for development and demo purposes.

**For production**, you should implement proper user-based policies:

```sql
-- Example: User-specific policies (implement when adding authentication)
CREATE POLICY "Users can only see their own jobs" ON scraping_jobs
    FOR ALL USING (auth.uid() = user_id);
```

## Data Relationships

```
scraping_jobs (1) → (many) scraped_data
    ↓
scraped_data (1) → (many) seo_analysis
scraped_data (1) → (many) keywords  
scraped_data (1) → (many) leads
scraped_data (1) → (many) content_ideas
scraped_data (1) → (many) social_posts
```

## Performance Considerations

The schema includes several performance optimizations:

1. **Indexes** on frequently queried columns
2. **Full-text search** indexes for content searching
3. **JSONB** for flexible metadata storage
4. **Triggers** for automatic data processing
5. **Constraints** for data validation

## Maintenance

### Cleanup Old Data

The schema includes a cleanup function for old failed jobs:

```sql
-- Run periodically to clean up old failed jobs
SELECT cleanup_old_jobs();
```

### Monitor Performance

Use these queries to monitor database performance:

```sql
-- Check table sizes
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats
WHERE schemaname = 'public';

-- Check index usage
SELECT 
    indexrelname,
    idx_tup_read,
    idx_tup_fetch
FROM pg_stat_user_indexes;
```

## Troubleshooting

### Common Issues

1. **Permission Errors**: Make sure RLS policies are properly configured
2. **Connection Issues**: Verify your Supabase credentials in `.env`
3. **Schema Errors**: Check that all tables were created successfully
4. **Performance Issues**: Monitor query performance and add indexes as needed

### Useful Queries

```sql
-- Check recent scraping jobs
SELECT * FROM scraping_jobs ORDER BY created_at DESC LIMIT 10;

-- Check scraped data with job info
SELECT sd.*, sj.status 
FROM scraped_data sd 
JOIN scraping_jobs sj ON sd.job_id = sj.id 
ORDER BY sd.scraped_at DESC LIMIT 10;

-- Check SEO analysis results
SELECT sa.overall_score, sd.title, sd.url
FROM seo_analysis sa
JOIN scraped_data sd ON sa.scraped_data_id = sd.id
ORDER BY sa.overall_score DESC;
```

## Next Steps

After setting up the database:

1. **Test the Application**: Run the application and try scraping a website
2. **Verify Data Flow**: Check that data is being stored correctly in all tables
3. **Monitor Performance**: Watch for any performance issues as you add more data
4. **Implement Authentication**: Add user authentication and proper RLS policies for production
5. **Set up Backups**: Configure automatic backups in Supabase

## Support

If you encounter issues:

1. Check the Supabase logs in your project dashboard
2. Verify your environment variables are correct
3. Test the database connection using the Supabase SQL Editor
4. Review the application logs for any database-related errors

For more information, refer to the [Supabase Documentation](https://supabase.com/docs).
