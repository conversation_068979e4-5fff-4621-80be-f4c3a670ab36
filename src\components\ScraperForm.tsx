import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Play, Plus, Trash2, Globe, CheckCircle, AlertCircle, Shield, AlertTriangle } from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { config } from '@/lib/config';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { ValidationError, NetworkError } from '@/lib/errors';
import { webScraper } from '@/lib/scraping';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useURLValidation, useRateLimit, useSecurityMonitoring } from '@/hooks/useSecurity';
import { inputSanitizer } from '@/lib/security';

const ScraperForm: React.FC = () => {
  const [urls, setUrls] = useState<string[]>(['']);
  const [isRunning, setIsRunning] = useState<boolean>(false);
  const [progress, setProgress] = useState<number>(0);
  const [currentUrl, setCurrentUrl] = useState<string>('');
  const [completedUrls, setCompletedUrls] = useState<string[]>([]);
  const [failedUrls, setFailedUrls] = useState<string[]>([]);
  const [securityWarnings, setSecurityWarnings] = useState<string[]>([]);
  const { showError, showSuccess } = useErrorHandler();

  // Security hooks
  const { validateURLs } = useURLValidation();
  const { checkLimit, remainingRequests, isLimited } = useRateLimit('scraping', 10, 60000); // 10 requests per minute
  const { logSecurityEvent, securityScore } = useSecurityMonitoring();

  const addUrl = () => {
    if (urls.length >= 10) {
      showError(new ValidationError('Too many URLs', 'Maximum 10 URLs allowed per batch'));
      logSecurityEvent('URL limit exceeded', { count: urls.length }, 'medium');
      return;
    }
    setUrls([...urls, '']);
  };

  const removeUrl = (index: number) => setUrls(urls.filter((_, i) => i !== index));

  const updateUrl = (index: number, value: string) => {
    // Sanitize input
    const sanitizedValue = inputSanitizer.sanitizeText(value);
    const newUrls = [...urls];
    newUrls[index] = sanitizedValue;
    setUrls(newUrls);

    // Clear security warnings when URL is updated
    setSecurityWarnings([]);
  };

  const handleScrape = async () => {
    // Check rate limiting
    if (!checkLimit()) {
      showError(new ValidationError('Rate limit exceeded', `Please wait. ${remainingRequests} requests remaining.`));
      logSecurityEvent('Rate limit exceeded during scraping', { remainingRequests }, 'medium');
      return;
    }

    const inputUrls = urls.filter(url => url.trim());

    if (inputUrls.length === 0) {
      showError(new ValidationError('No URLs provided', 'Please enter at least one URL'));
      return;
    }

    // Enhanced URL validation with security checks
    const { validURLs, invalidURLs, results } = validateURLs(inputUrls);

    if (invalidURLs.length > 0) {
      const warnings = results
        .filter(r => !r.isValid)
        .map(r => `${r.url}: ${r.error}`)

      setSecurityWarnings(warnings);
      showError(new ValidationError(
        `${invalidURLs.length} invalid or dangerous URLs detected`,
        'Please check the security warnings below'
      ));
      logSecurityEvent('Invalid URLs blocked', { invalidURLs }, 'medium');
      return;
    }

    if (validURLs.length === 0) {
      showError(new ValidationError('No valid URLs', 'All provided URLs failed security validation'));
      return;
    }

    // Clear previous security warnings
    setSecurityWarnings([]);

    setIsRunning(true);
    setProgress(0);
    setCompletedUrls([]);
    setFailedUrls([]);
    let successCount = 0;
    let failureCount = 0;

    // Log security event for scraping start
    logSecurityEvent('Scraping session started', {
      urlCount: validURLs.length,
      securityScore
    }, 'low');

    try {
      for (let i = 0; i < validURLs.length; i++) {
        const url = validURLs[i];
        setCurrentUrl(url);
        setProgress((i / validURLs.length) * 100);

        try {
          // Create job record
          const { data: job, error: jobError } = await supabase
            .from('scraping_jobs')
            .insert({ url, status: 'running' })
            .select()
            .single();

          if (jobError) {
            throw new Error(`Failed to create job: ${jobError.message}`);
          }

          // Use local scraper instead of external function
          const scrapingResult = await webScraper.scrapeUrl(url, {
            timeout: 15000,
            maxContentLength: 100000,
            extractImages: true,
            extractLinks: true,
            extractHeadings: true,
          });

          if (scrapingResult.error) {
            throw new Error(scrapingResult.error);
          }

          // Update job status and save scraped data
          const { error: updateError } = await supabase
            .from('scraping_jobs')
            .update({
              status: 'completed',
              results: scrapingResult,
              completed_at: new Date().toISOString()
            })
            .eq('id', job.id);

          if (updateError) {
            throw new Error(`Failed to update job: ${updateError.message}`);
          }

          const { error: insertError } = await supabase
            .from('scraped_data')
            .insert({
              job_id: job.id,
              url: scrapingResult.url,
              title: scrapingResult.title,
              content: scrapingResult.content,
              metadata: {
                headings: scrapingResult.headings || [],
                links: scrapingResult.links || [],
                wordCount: scrapingResult.wordCount || 0,
                images: scrapingResult.images || [],
                description: scrapingResult.description,
                ...scrapingResult.metadata
              },
              scraped_at: new Date().toISOString()
            });

          if (insertError) {
            throw new Error(`Failed to save scraped data: ${insertError.message}`);
          }

          successCount++;
          setCompletedUrls(prev => [...prev, url]);
        } catch (error) {
          failureCount++;
          setFailedUrls(prev => [...prev, url]);

          // Update job status to failed
          try {
            await supabase
              .from('scraping_jobs')
              .update({
                status: 'failed',
                results: { error: error instanceof Error ? error.message : 'Unknown error' },
                completed_at: new Date().toISOString()
              })
              .eq('url', url);
          } catch {
            // Ignore errors when updating failed status
          }
        }
      }

      setProgress(100);
      setCurrentUrl('');

      if (successCount > 0) {
        showSuccess(
          `Scraping completed: ${successCount} successful${failureCount > 0 ? `, ${failureCount} failed` : ''}`,
          'Check the data table below for results'
        );
      } else if (failureCount > 0) {
        showError(new Error('All scraping attempts failed'), 'Please check your URLs and try again');
      }
    } catch (error) {
      showError(error, 'Scraping operation');
    } finally {
      setIsRunning(false);
      setProgress(0);
      setCurrentUrl('');
    }
  };

  return (
    <Card className="border-0 shadow-xl bg-gradient-to-br from-white to-gray-50">
      <CardHeader className="bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-t-lg">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Play className="h-5 w-5" />
            <span>Web Scraper</span>
          </div>
          <div className="flex items-center space-x-2">
            <Shield className="h-4 w-4" />
            <Badge variant={securityScore >= 80 ? 'default' : securityScore >= 60 ? 'secondary' : 'destructive'}>
              Security: {securityScore}%
            </Badge>
            {isLimited && (
              <Badge variant="destructive">
                Rate Limited
              </Badge>
            )}
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="p-6 space-y-6">
        {/* Security Warnings */}
        {securityWarnings.length > 0 && (
          <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="font-medium text-red-800">Security Warnings</span>
            </div>
            <ul className="text-sm text-red-700 space-y-1">
              {securityWarnings.map((warning, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-red-500">•</span>
                  <span>{warning}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Rate Limit Info */}
        {remainingRequests <= 3 && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <span className="text-sm text-yellow-800">
                Rate limit warning: {remainingRequests} requests remaining
              </span>
            </div>
          </div>
        )}

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label className="text-lg font-semibold">Target URLs</Label>
            <Badge variant="outline" className="text-xs">
              {urls.filter(url => url.trim()).length}/10 URLs
            </Badge>
          </div>
          {urls.map((url, index) => (
            <div key={index} className="flex space-x-2">
              <Input
                placeholder="https://example.com"
                value={url}
                onChange={(e) => updateUrl(index, e.target.value)}
                className="flex-1"
              />
              {urls.length > 1 && (
                <Button variant="outline" size="sm" onClick={() => removeUrl(index)}>
                  <Trash2 className="h-4 w-4" />
                </Button>
              )}
            </div>
          ))}
          <Button variant="outline" onClick={addUrl} className="w-full">
            <Plus className="h-4 w-4 mr-2" />Add URL
          </Button>
        </div>
        {/* Progress Section */}
        {isRunning && (
          <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Scraping Progress</span>
              <span className="text-sm text-muted-foreground">
                {Math.round(progress)}%
              </span>
            </div>
            <Progress value={progress} className="w-full" />

            {currentUrl && (
              <div className="flex items-center space-x-2 text-sm">
                <Globe className="h-4 w-4 animate-pulse text-primary" />
                <span className="text-muted-foreground">Currently scraping:</span>
                <span className="font-medium truncate">{currentUrl}</span>
              </div>
            )}

            {/* Status indicators */}
            <div className="flex space-x-4 text-sm">
              {completedUrls.length > 0 && (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span>{completedUrls.length} completed</span>
                </div>
              )}
              {failedUrls.length > 0 && (
                <div className="flex items-center space-x-1 text-red-600">
                  <AlertCircle className="h-4 w-4" />
                  <span>{failedUrls.length} failed</span>
                </div>
              )}
            </div>
          </div>
        )}

        <Button
          onClick={handleScrape}
          disabled={isRunning}
          className="w-full bg-gradient-to-r from-purple-500 to-blue-500"
        >
          {isRunning ? (
            <>
              <Globe className="h-4 w-4 mr-2 animate-spin" />
              Scraping... ({Math.round(progress)}%)
            </>
          ) : (
            <>
              <Play className="h-4 w-4 mr-2" />
              Start Scraping
            </>
          )}
        </Button>
      </CardContent>
    </Card>
  );
};

export default ScraperForm;