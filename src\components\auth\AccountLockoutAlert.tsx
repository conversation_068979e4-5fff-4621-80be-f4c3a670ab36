import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Shield, Clock, AlertTriangle, Mail } from 'lucide-react';
import { accountSecurity, AccountLockout } from '@/lib/accountSecurity';

interface AccountLockoutAlertProps {
  email: string;
  onContactSupport?: () => void;
}

const AccountLockoutAlert: React.FC<AccountLockoutAlertProps> = ({ 
  email, 
  onContactSupport 
}) => {
  const [lockoutInfo, setLockoutInfo] = useState<AccountLockout | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchLockoutInfo = async () => {
      try {
        const info = await accountSecurity.getAccountLockoutInfo(email);
        setLockoutInfo(info);
      } catch (error) {
        console.error('Error fetching lockout info:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchLockoutInfo();
  }, [email]);

  useEffect(() => {
    if (!lockoutInfo) return;

    const updateTimeRemaining = () => {
      const now = new Date();
      const lockedUntil = new Date(lockoutInfo.locked_until);
      const diff = lockedUntil.getTime() - now.getTime();

      if (diff <= 0) {
        setTimeRemaining('Account should be unlocked now');
        return;
      }

      const minutes = Math.floor(diff / (1000 * 60));
      const seconds = Math.floor((diff % (1000 * 60)) / 1000);

      if (minutes > 0) {
        setTimeRemaining(`${minutes} minute${minutes !== 1 ? 's' : ''} and ${seconds} second${seconds !== 1 ? 's' : ''}`);
      } else {
        setTimeRemaining(`${seconds} second${seconds !== 1 ? 's' : ''}`);
      }
    };

    updateTimeRemaining();
    const interval = setInterval(updateTimeRemaining, 1000);

    return () => clearInterval(interval);
  }, [lockoutInfo]);

  if (loading) {
    return (
      <Alert className="border-yellow-200 bg-yellow-50">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-yellow-700">
          Checking account status...
        </AlertDescription>
      </Alert>
    );
  }

  if (!lockoutInfo) {
    return null;
  }

  const lockedAt = new Date(lockoutInfo.locked_at).toLocaleString();
  const lockedUntil = new Date(lockoutInfo.locked_until).toLocaleString();

  return (
    <Card className="w-full max-w-md border-red-200 bg-red-50">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
          <Shield className="h-6 w-6 text-red-600" />
        </div>
        <CardTitle className="text-xl text-red-800">Account Temporarily Locked</CardTitle>
        <CardDescription className="text-red-600">
          Your account has been locked for security reasons
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert className="border-red-200 bg-red-100">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="text-red-700">
            <strong>Reason:</strong> {lockoutInfo.reason}
          </AlertDescription>
        </Alert>

        <div className="space-y-3 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Failed attempts:</span>
            <span className="font-medium text-red-600">{lockoutInfo.attempt_count}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Locked at:</span>
            <span className="font-medium">{lockedAt}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-gray-600">Unlocks at:</span>
            <span className="font-medium">{lockedUntil}</span>
          </div>
        </div>

        {timeRemaining && (
          <Alert className="border-blue-200 bg-blue-50">
            <Clock className="h-4 w-4" />
            <AlertDescription className="text-blue-700">
              <strong>Time remaining:</strong> {timeRemaining}
            </AlertDescription>
          </Alert>
        )}

        <div className="space-y-3 pt-4 border-t">
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-2">What you can do:</p>
            <ul className="space-y-1 list-disc list-inside">
              <li>Wait for the lockout period to expire</li>
              <li>Make sure you're using the correct password</li>
              <li>Check if Caps Lock is enabled</li>
              <li>Contact support if you believe this is an error</li>
            </ul>
          </div>

          {onContactSupport && (
            <Button
              variant="outline"
              onClick={onContactSupport}
              className="w-full"
            >
              <Mail className="mr-2 h-4 w-4" />
              Contact Support
            </Button>
          )}

          <div className="text-xs text-gray-500 text-center">
            <p>For security reasons, we temporarily lock accounts after multiple failed login attempts.</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AccountLockoutAlert;
