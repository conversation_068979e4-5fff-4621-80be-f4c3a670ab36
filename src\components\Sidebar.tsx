import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  Globe,
  Search,
  BarChart3,
  Mail,
  Share2,
  Settings,
  Database,
  TrendingUp,
  Users,
  MessageSquare
} from 'lucide-react';

export type MenuItemKey = 'web-scraper' | 'seo-tools' | 'analytics' | 'keyword-research' | 'email-marketing' | 'social-media' | 'lead-generation' | 'content-ideas' | 'data-export' | 'settings';

interface SidebarProps {
  isOpen: boolean;
  isMobile: boolean;
  activeMenuItem: MenuItemKey;
  onMenuItemClick: (menuItem: MenuItemKey) => void;
}

const Sidebar: React.FC<SidebarProps> = ({ isOpen, isMobile, activeMenuItem, onMenuItemClick }) => {
  const menuItems = [
    { icon: Globe, label: 'Web Scraper', key: 'web-scraper' as <PERSON>u<PERSON><PERSON><PERSON><PERSON> },
    { icon: Search, label: 'SEO Tools', key: 'seo-tools' as Menu<PERSON><PERSON><PERSON><PERSON> },
    { icon: BarChart3, label: 'Analytics', key: 'analytics' as MenuItemKey },
    { icon: TrendingUp, label: 'Keyword Research', key: 'keyword-research' as MenuItemKey },
    { icon: Mail, label: 'Email Marketing', key: 'email-marketing' as MenuItemKey },
    { icon: Share2, label: 'Social Media', key: 'social-media' as MenuItemKey },
    { icon: Users, label: 'Lead Generation', key: 'lead-generation' as MenuItemKey },
    { icon: MessageSquare, label: 'Content Ideas', key: 'content-ideas' as MenuItemKey },
    { icon: Database, label: 'Data Export', key: 'data-export' as MenuItemKey },
    { icon: Settings, label: 'Settings', key: 'settings' as MenuItemKey }
  ];

  return (
    <aside className={cn(
      "bg-white shadow-lg transition-all duration-300 border-r border-gray-200",
      isMobile ? "fixed inset-y-0 left-0 z-50" : "relative",
      isOpen ? "w-64" : "w-16"
    )}>
      <div className="p-4 space-y-2">
        {menuItems.map((item, index) => {
          const isActive = activeMenuItem === item.key;
          return (
            <Button
              key={index}
              variant={isActive ? "default" : "ghost"}
              className={cn(
                "w-full justify-start transition-all cursor-pointer",
                !isOpen && "px-2",
                isActive && "bg-gradient-to-r from-purple-500 to-blue-500 text-white"
              )}
              onClick={() => onMenuItemClick(item.key)}
            >
              <item.icon className="h-5 w-5" />
              {isOpen && <span className="ml-3">{item.label}</span>}
            </Button>
          );
        })}
      </div>
    </aside>
  );
};

export default Sidebar;