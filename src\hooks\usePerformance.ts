import { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { debounce, throttle, performanceMonitor } from '@/lib/performance'

/**
 * Hook for debounced values
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

/**
 * Hook for throttled callbacks
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const throttledCallback = useMemo(
    () => throttle(callback, delay),
    [callback, delay]
  )

  return throttledCallback as T
}

/**
 * Hook for debounced callbacks
 */
export function useDebounceCallback<T extends (...args: any[]) => any>(
  callback: T,
  delay: number,
  immediate = false
): T {
  const debouncedCallback = useMemo(
    () => debounce(callback, delay, immediate),
    [callback, delay, immediate]
  )

  return debouncedCallback as T
}

/**
 * Hook for performance timing
 */
export function usePerformanceTiming(label: string) {
  const endTimingRef = useRef<(() => void) | null>(null)

  const startTiming = useCallback(() => {
    endTimingRef.current = performanceMonitor.startTiming(label)
  }, [label])

  const endTiming = useCallback(() => {
    if (endTimingRef.current) {
      endTimingRef.current()
      endTimingRef.current = null
    }
  }, [])

  useEffect(() => {
    return () => {
      if (endTimingRef.current) {
        endTimingRef.current()
      }
    }
  }, [])

  return { startTiming, endTiming }
}

/**
 * Hook for intersection observer (lazy loading)
 */
export function useIntersectionObserver(
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)
  const elementRef = useRef<HTMLElement | null>(null)

  useEffect(() => {
    const element = elementRef.current
    if (!element) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        const isElementIntersecting = entry.isIntersecting
        setIsIntersecting(isElementIntersecting)
        
        if (isElementIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        ...options,
      }
    )

    observer.observe(element)

    return () => {
      observer.unobserve(element)
    }
  }, [hasIntersected, options])

  return {
    elementRef,
    isIntersecting,
    hasIntersected,
  }
}

/**
 * Hook for virtual scrolling
 */
export function useVirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[]
  itemHeight: number
  containerHeight: number
  overscan?: number
}) {
  const [scrollTop, setScrollTop] = useState(0)

  const visibleRange = useMemo(() => {
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const startIndex = Math.floor(scrollTop / itemHeight)
    const endIndex = Math.min(
      startIndex + visibleCount + overscan,
      items.length
    )

    return {
      startIndex: Math.max(0, startIndex - overscan),
      endIndex,
      visibleItems: items.slice(
        Math.max(0, startIndex - overscan),
        endIndex
      ),
    }
  }, [scrollTop, itemHeight, containerHeight, items.length, overscan, items])

  const totalHeight = items.length * itemHeight
  const offsetY = visibleRange.startIndex * itemHeight

  const handleScroll = useCallback((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop)
  }, [])

  return {
    visibleRange,
    totalHeight,
    offsetY,
    handleScroll,
  }
}

/**
 * Hook for image lazy loading
 */
export function useImageLazyLoad(src: string, placeholder?: string) {
  const [imageSrc, setImageSrc] = useState(placeholder || '')
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const { elementRef, hasIntersected } = useIntersectionObserver()

  useEffect(() => {
    if (!hasIntersected || !src) return

    const img = new Image()
    
    img.onload = () => {
      setImageSrc(src)
      setIsLoaded(true)
      setIsError(false)
    }
    
    img.onerror = () => {
      setIsError(true)
      setIsLoaded(false)
    }
    
    img.src = src
  }, [hasIntersected, src])

  return {
    elementRef,
    imageSrc,
    isLoaded,
    isError,
    hasIntersected,
  }
}

/**
 * Hook for memory usage monitoring
 */
export function useMemoryMonitor() {
  const [memoryUsage, setMemoryUsage] = useState<{
    usedJSHeapSize: number
    totalJSHeapSize: number
    jsHeapSizeLimit: number
    usedPercentage: number
  } | null>(null)

  useEffect(() => {
    const updateMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory
        setMemoryUsage({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
          usedPercentage: (memory.usedJSHeapSize / memory.jsHeapSizeLimit) * 100,
        })
      }
    }

    updateMemoryUsage()
    const interval = setInterval(updateMemoryUsage, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  return memoryUsage
}

/**
 * Hook for component render counting (development only)
 */
export function useRenderCount(componentName: string) {
  const renderCount = useRef(0)
  
  useEffect(() => {
    renderCount.current += 1
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔄 ${componentName} rendered ${renderCount.current} times`)
    }
  })

  return renderCount.current
}

/**
 * Hook for preventing unnecessary re-renders
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T
): T {
  const callbackRef = useRef(callback)
  
  useEffect(() => {
    callbackRef.current = callback
  })

  return useCallback((...args: any[]) => {
    return callbackRef.current(...args)
  }, []) as T
}

/**
 * Hook for memoized expensive calculations
 */
export function useExpensiveCalculation<T, D extends readonly unknown[]>(
  calculate: () => T,
  deps: D
): T {
  return useMemo(() => {
    const { startTiming, endTiming } = performanceMonitor.startTiming('expensive-calculation')
    const result = calculate()
    endTiming()
    return result
  }, deps)
}

/**
 * Hook for component mounting/unmounting timing
 */
export function useComponentLifecycle(componentName: string) {
  const { startTiming, endTiming } = usePerformanceTiming(`${componentName}-mount`)

  useEffect(() => {
    startTiming()
    
    return () => {
      endTiming()
    }
  }, [startTiming, endTiming])
}

/**
 * Hook for network status monitoring
 */
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(navigator.onLine)
  const [networkInfo, setNetworkInfo] = useState<{
    effectiveType?: string
    downlink?: number
    rtt?: number
    saveData?: boolean
  } | null>(null)

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Get network information if available
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      setNetworkInfo({
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData,
      })

      const handleConnectionChange = () => {
        setNetworkInfo({
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt,
          saveData: connection.saveData,
        })
      }

      connection.addEventListener('change', handleConnectionChange)

      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
        connection.removeEventListener('change', handleConnectionChange)
      }
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return { isOnline, networkInfo }
}
