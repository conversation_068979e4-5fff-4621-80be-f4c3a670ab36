// <PERSON><PERSON><PERSON> to run the SEO tables SQL script
// This will create the missing SEO tables that are causing 404 errors in SEOTools component

import { readFileSync } from 'fs';
import { createClient } from '@supabase/supabase-js';

// Read config from the same source as the app
const config = {
  supabase: {
    url: process.env.VITE_SUPABASE_URL || 'https://rclikclltlyzyojjttqv.supabase.co',
    anonKey: process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NjE1NzQsImV4cCI6MjA1MDUzNzU3NH0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw'
  }
};

const supabase = createClient(config.supabase.url, config.supabase.anonKey);

async function runSEOTablesSQL() {
  try {
    console.log('🚀 Starting SEO tables creation...');
    
    // Read the SQL file
    const sqlContent = readFileSync('./database/create_seo_tables.sql', 'utf8');
    
    // Split the SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && stmt !== 'COMMIT');
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.includes('CREATE EXTENSION') || 
          statement.includes('CREATE TABLE') || 
          statement.includes('INSERT INTO') ||
          statement.includes('CREATE INDEX') ||
          statement.includes('ALTER TABLE') ||
          statement.includes('CREATE POLICY') ||
          statement.includes('DO $$')) {
        
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
        console.log(`📄 Statement preview: ${statement.substring(0, 100)}...`);
        
        try {
          // For Supabase, we'll use direct SQL execution
          const { data, error } = await supabase.rpc('exec_sql', { 
            sql_query: statement + ';' 
          });
          
          if (error) {
            console.log(`⚠️ Statement ${i + 1} failed (might be expected):`, error.message);
            // Continue with other statements even if one fails
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully`);
          }
        } catch (err) {
          console.log(`⚠️ Statement ${i + 1} exception:`, err.message);
          // Continue with other statements
        }
      }
    }
    
    console.log('\n🧪 Testing the created SEO tables...');
    
    // Test if tables were created successfully
    const tables = ['seo_analyses', 'keyword_data', 'competitor_analyses', 'backlinks'];
    
    for (const table of tables) {
      try {
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: Table exists and accessible (${count || 0} rows)`);
        }
      } catch (err) {
        console.log(`💥 ${table}: ${err.message}`);
      }
    }
    
    console.log('\n🎉 SEO tables setup completed!');
    console.log('You can now refresh your SEOTools component and the 404 errors should be resolved.');
    
    // Test a sample query from each table to verify data
    console.log('\n📊 Testing sample data...');
    
    try {
      const { data: seoData } = await supabase
        .from('seo_analyses')
        .select('url, overall_score')
        .limit(2);
      
      if (seoData && seoData.length > 0) {
        console.log('✅ Sample SEO analyses:', seoData);
      }
    } catch (err) {
      console.log('⚠️ Could not fetch sample SEO data:', err.message);
    }
    
    try {
      const { data: keywordData } = await supabase
        .from('keyword_data')
        .select('keyword, volume, difficulty')
        .limit(2);
      
      if (keywordData && keywordData.length > 0) {
        console.log('✅ Sample keyword data:', keywordData);
      }
    } catch (err) {
      console.log('⚠️ Could not fetch sample keyword data:', err.message);
    }
    
  } catch (error) {
    console.error('💥 Failed to run SEO tables SQL script:', error);
    console.log('\n📋 Manual steps:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the content from database/create_seo_tables.sql');
    console.log('4. Run the SQL script manually');
    console.log('\n🔗 Supabase Dashboard: https://supabase.com/dashboard/projects');
  }
}

// Run the script
runSEOTablesSQL();
