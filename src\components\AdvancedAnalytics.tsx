import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Progress } from '@/components/ui/progress'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  ScatterChart,
  Scatter,
  Area,
  AreaChart
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Target, 
  Users, 
  Globe, 
  Search,
  Download,
  Filter,
  Calendar,
  BarChart3,
  PieChart as PieChartIcon,
  Activity
} from 'lucide-react'
import { useQuery } from '@tanstack/react-query'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { dataExporter } from '@/lib/export'

interface AnalyticsData {
  overview: {
    totalPages: number
    avgSeoScore: number
    totalKeywords: number
    totalLeads: number
    conversionRate: number
    growthRate: number
  }
  trends: Array<{
    date: string
    pages: number
    seoScore: number
    keywords: number
    leads: number
  }>
  seoDistribution: Array<{
    scoreRange: string
    count: number
    percentage: number
  }>
  topKeywords: Array<{
    keyword: string
    frequency: number
    density: number
    trend: 'up' | 'down' | 'stable'
  }>
  leadSources: Array<{
    source: string
    count: number
    conversionRate: number
  }>
  contentPerformance: Array<{
    url: string
    title: string
    seoScore: number
    keywords: number
    leads: number
    wordCount: number
  }>
}

const AdvancedAnalytics: React.FC = () => {
  const { user, canPerformAction } = useAuth()
  const [dateRange, setDateRange] = useState('30d')
  const [selectedMetric, setSelectedMetric] = useState('seoScore')
  const [chartType, setChartType] = useState<'line' | 'bar' | 'area'>('line')

  // Check if user has access to advanced analytics
  if (!canPerformAction('advanced_analytics')) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <div className="space-y-4">
            <Activity className="h-12 w-12 mx-auto text-muted-foreground" />
            <h3 className="text-lg font-semibold">Advanced Analytics</h3>
            <p className="text-muted-foreground">
              Upgrade to Premium to access advanced analytics features including trend analysis, 
              performance insights, and detailed reporting.
            </p>
            <Button>Upgrade to Premium</Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  // Fetch analytics data
  const { data: analyticsData, isLoading, error } = useQuery({
    queryKey: ['advanced-analytics', dateRange],
    queryFn: async (): Promise<AnalyticsData> => {
      const endDate = new Date()
      const startDate = new Date()
      
      switch (dateRange) {
        case '7d':
          startDate.setDate(endDate.getDate() - 7)
          break
        case '30d':
          startDate.setDate(endDate.getDate() - 30)
          break
        case '90d':
          startDate.setDate(endDate.getDate() - 90)
          break
        case '1y':
          startDate.setFullYear(endDate.getFullYear() - 1)
          break
      }

      // Fetch overview data
      const { data: scrapedData } = await supabase
        .from('scraped_data')
        .select(`
          *,
          seo_analysis(*),
          keywords(*),
          leads(*)
        `)
        .gte('scraped_at', startDate.toISOString())
        .lte('scraped_at', endDate.toISOString())

      if (!scrapedData) throw new Error('Failed to fetch analytics data')

      // Process data for analytics
      const overview = {
        totalPages: scrapedData.length,
        avgSeoScore: scrapedData.reduce((sum, item) => 
          sum + (item.seo_analysis?.[0]?.overall_score || 0), 0) / scrapedData.length || 0,
        totalKeywords: scrapedData.reduce((sum, item) => 
          sum + (item.keywords?.length || 0), 0),
        totalLeads: scrapedData.reduce((sum, item) => 
          sum + (item.leads?.length || 0), 0),
        conversionRate: 0, // Calculate based on your business logic
        growthRate: 0 // Calculate based on previous period comparison
      }

      // Generate trends data (simplified)
      const trends = Array.from({ length: 30 }, (_, i) => {
        const date = new Date(startDate)
        date.setDate(date.getDate() + i)
        
        const dayData = scrapedData.filter(item => {
          const itemDate = new Date(item.scraped_at)
          return itemDate.toDateString() === date.toDateString()
        })

        return {
          date: date.toISOString().split('T')[0],
          pages: dayData.length,
          seoScore: dayData.reduce((sum, item) => 
            sum + (item.seo_analysis?.[0]?.overall_score || 0), 0) / dayData.length || 0,
          keywords: dayData.reduce((sum, item) => 
            sum + (item.keywords?.length || 0), 0),
          leads: dayData.reduce((sum, item) => 
            sum + (item.leads?.length || 0), 0)
        }
      })

      // SEO score distribution
      const seoDistribution = [
        { scoreRange: '0-20', count: 0, percentage: 0 },
        { scoreRange: '21-40', count: 0, percentage: 0 },
        { scoreRange: '41-60', count: 0, percentage: 0 },
        { scoreRange: '61-80', count: 0, percentage: 0 },
        { scoreRange: '81-100', count: 0, percentage: 0 }
      ]

      scrapedData.forEach(item => {
        const score = item.seo_analysis?.[0]?.overall_score || 0
        if (score <= 20) seoDistribution[0].count++
        else if (score <= 40) seoDistribution[1].count++
        else if (score <= 60) seoDistribution[2].count++
        else if (score <= 80) seoDistribution[3].count++
        else seoDistribution[4].count++
      })

      seoDistribution.forEach(item => {
        item.percentage = (item.count / scrapedData.length) * 100
      })

      // Top keywords (simplified)
      const allKeywords = scrapedData.flatMap(item => item.keywords || [])
      const keywordMap = new Map()
      
      allKeywords.forEach(keyword => {
        const existing = keywordMap.get(keyword.keyword) || { 
          frequency: 0, 
          density: 0, 
          count: 0 
        }
        keywordMap.set(keyword.keyword, {
          frequency: existing.frequency + keyword.frequency,
          density: existing.density + keyword.density,
          count: existing.count + 1
        })
      })

      const topKeywords = Array.from(keywordMap.entries())
        .map(([keyword, data]) => ({
          keyword,
          frequency: data.frequency,
          density: data.density / data.count,
          trend: 'stable' as const // Would need historical data for real trends
        }))
        .sort((a, b) => b.frequency - a.frequency)
        .slice(0, 10)

      // Lead sources (simplified)
      const leadSources = [
        { source: 'Organic Search', count: Math.floor(overview.totalLeads * 0.4), conversionRate: 2.5 },
        { source: 'Direct Traffic', count: Math.floor(overview.totalLeads * 0.3), conversionRate: 3.2 },
        { source: 'Social Media', count: Math.floor(overview.totalLeads * 0.2), conversionRate: 1.8 },
        { source: 'Referrals', count: Math.floor(overview.totalLeads * 0.1), conversionRate: 4.1 }
      ]

      // Content performance
      const contentPerformance = scrapedData
        .map(item => ({
          url: item.url,
          title: item.title || 'Untitled',
          seoScore: item.seo_analysis?.[0]?.overall_score || 0,
          keywords: item.keywords?.length || 0,
          leads: item.leads?.length || 0,
          wordCount: item.word_count || 0
        }))
        .sort((a, b) => b.seoScore - a.seoScore)
        .slice(0, 10)

      return {
        overview,
        trends,
        seoDistribution,
        topKeywords,
        leadSources,
        contentPerformance
      }
    },
    enabled: !!user
  })

  const handleExport = async () => {
    if (!analyticsData) return

    try {
      await dataExporter.exportComprehensiveReport(
        {
          scrapedData: [],
          seoAnalysis: [],
          keywords: [],
          leads: [],
          contentIdeas: [],
          socialPosts: []
        },
        {
          format: 'json',
          filename: `analytics_report_${dateRange}_${Date.now()}.json`,
          includeMetadata: true
        }
      )
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const chartColors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#00ff00']

  if (isLoading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 4 }).map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
                <div className="h-32 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error || !analyticsData) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-destructive">Failed to load analytics data</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Advanced Analytics</h2>
          <p className="text-muted-foreground">
            Comprehensive insights and performance metrics
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button variant="outline" onClick={handleExport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Pages</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalPages}</p>
              </div>
              <Globe className="h-8 w-8 text-blue-500" />
            </div>
            <div className="mt-2 flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500">+12%</span>
              <span className="text-muted-foreground ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg SEO Score</p>
                <p className="text-2xl font-bold">{analyticsData.overview.avgSeoScore.toFixed(1)}</p>
              </div>
              <Target className="h-8 w-8 text-green-500" />
            </div>
            <div className="mt-2">
              <Progress value={analyticsData.overview.avgSeoScore} className="h-2" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Keywords Found</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalKeywords}</p>
              </div>
              <Search className="h-8 w-8 text-purple-500" />
            </div>
            <div className="mt-2 flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500">+8%</span>
              <span className="text-muted-foreground ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Leads Generated</p>
                <p className="text-2xl font-bold">{analyticsData.overview.totalLeads}</p>
              </div>
              <Users className="h-8 w-8 text-orange-500" />
            </div>
            <div className="mt-2 flex items-center text-sm">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <span className="text-green-500">+15%</span>
              <span className="text-muted-foreground ml-1">vs last period</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="trends" className="space-y-4">
        <TabsList>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="distribution">SEO Distribution</TabsTrigger>
          <TabsTrigger value="keywords">Top Keywords</TabsTrigger>
          <TabsTrigger value="performance">Content Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <CardTitle>Performance Trends</CardTitle>
              <div className="flex items-center space-x-2">
                <Select value={selectedMetric} onValueChange={setSelectedMetric}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="seoScore">SEO Score</SelectItem>
                    <SelectItem value="pages">Pages</SelectItem>
                    <SelectItem value="keywords">Keywords</SelectItem>
                    <SelectItem value="leads">Leads</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={chartType} onValueChange={(value: any) => setChartType(value)}>
                  <SelectTrigger className="w-24">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="line">Line</SelectItem>
                    <SelectItem value="bar">Bar</SelectItem>
                    <SelectItem value="area">Area</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                {chartType === 'line' && (
                  <LineChart data={analyticsData.trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey={selectedMetric} 
                      stroke="#8884d8" 
                      strokeWidth={2}
                    />
                  </LineChart>
                )}
                {chartType === 'bar' && (
                  <BarChart data={analyticsData.trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey={selectedMetric} fill="#8884d8" />
                  </BarChart>
                )}
                {chartType === 'area' && (
                  <AreaChart data={analyticsData.trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area 
                      type="monotone" 
                      dataKey={selectedMetric} 
                      stroke="#8884d8" 
                      fill="#8884d8" 
                      fillOpacity={0.3}
                    />
                  </AreaChart>
                )}
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="distribution" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>SEO Score Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={analyticsData.seoDistribution}
                      cx="50%"
                      cy="50%"
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                      label={({ scoreRange, percentage }) => 
                        `${scoreRange}: ${percentage.toFixed(1)}%`
                      }
                    >
                      {analyticsData.seoDistribution.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lead Sources</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={analyticsData.leadSources}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="source" />
                    <YAxis />
                    <Tooltip />
                    <Bar dataKey="count" fill="#82ca9d" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="keywords" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Top Performing Keywords</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.topKeywords.map((keyword, index) => (
                  <div key={keyword.keyword} className="flex items-center justify-between p-3 border rounded">
                    <div className="flex items-center space-x-3">
                      <Badge variant="outline">#{index + 1}</Badge>
                      <div>
                        <p className="font-medium">{keyword.keyword}</p>
                        <p className="text-sm text-muted-foreground">
                          Density: {keyword.density.toFixed(2)}%
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="font-bold">{keyword.frequency}</span>
                      {keyword.trend === 'up' && <TrendingUp className="h-4 w-4 text-green-500" />}
                      {keyword.trend === 'down' && <TrendingDown className="h-4 w-4 text-red-500" />}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Content Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.contentPerformance.map((content, index) => (
                  <div key={content.url} className="p-4 border rounded space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium truncate">{content.title}</h4>
                      <Badge variant={content.seoScore >= 80 ? 'default' : content.seoScore >= 60 ? 'secondary' : 'destructive'}>
                        SEO: {content.seoScore}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground truncate">{content.url}</p>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Keywords:</span>
                        <span className="ml-1 font-medium">{content.keywords}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Leads:</span>
                        <span className="ml-1 font-medium">{content.leads}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Words:</span>
                        <span className="ml-1 font-medium">{content.wordCount}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AdvancedAnalytics
