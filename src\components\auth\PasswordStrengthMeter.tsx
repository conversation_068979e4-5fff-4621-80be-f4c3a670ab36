import React from 'react';
import { Progress } from '@/components/ui/progress';
import { CheckCircle, XCircle } from 'lucide-react';

interface PasswordStrength {
  score: number;
  feedback: string[];
  color: string;
  label: string;
}

interface PasswordStrengthMeterProps {
  password: string;
  showFeedback?: boolean;
  className?: string;
}

export const calculatePasswordStrength = (password: string): PasswordStrength => {
  let score = 0;
  const feedback: string[] = [];

  // Length check
  if (password.length >= 12) {
    score += 25;
  } else if (password.length >= 8) {
    score += 15;
  } else {
    feedback.push('At least 8 characters (12+ recommended)');
  }

  // Lowercase letters
  if (/[a-z]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Include lowercase letters (a-z)');
  }

  // Uppercase letters
  if (/[A-Z]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Include uppercase letters (A-Z)');
  }

  // Numbers
  if (/\d/.test(password)) {
    score += 15;
  } else {
    feedback.push('Include numbers (0-9)');
  }

  // Special characters
  if (/[^a-zA-Z0-9]/.test(password)) {
    score += 15;
  } else {
    feedback.push('Include special characters (!@#$%^&*)');
  }

  // Bonus points for variety and length
  if (password.length >= 12 && /[a-z]/.test(password) && /[A-Z]/.test(password) && /\d/.test(password) && /[^a-zA-Z0-9]/.test(password)) {
    score += 15; // Bonus for having all character types with good length
  }

  // Common patterns penalty
  if (/(.)\1{2,}/.test(password)) {
    score -= 10; // Repeated characters
    feedback.push('Avoid repeated characters');
  }

  if (/123|abc|qwe|password|admin/i.test(password)) {
    score -= 15; // Common patterns
    feedback.push('Avoid common patterns or words');
  }

  // Ensure score is between 0 and 100
  score = Math.max(0, Math.min(100, score));

  let color = 'bg-red-500';
  let label = 'Very Weak';

  if (score >= 90) {
    color = 'bg-green-600';
    label = 'Excellent';
  } else if (score >= 75) {
    color = 'bg-green-500';
    label = 'Strong';
  } else if (score >= 60) {
    color = 'bg-yellow-500';
    label = 'Good';
  } else if (score >= 40) {
    color = 'bg-orange-500';
    label = 'Fair';
  } else if (score >= 20) {
    color = 'bg-red-400';
    label = 'Weak';
  }

  return { score, feedback, color, label };
};

const PasswordStrengthMeter: React.FC<PasswordStrengthMeterProps> = ({
  password,
  showFeedback = true,
  className = ''
}) => {
  if (!password) return null;

  const strength = calculatePasswordStrength(password);

  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center justify-between text-sm">
        <span className="text-gray-600">Password strength:</span>
        <span className={`font-medium ${
          strength.score >= 75 ? 'text-green-600' :
          strength.score >= 60 ? 'text-yellow-600' :
          strength.score >= 40 ? 'text-orange-600' : 'text-red-600'
        }`}>
          {strength.label}
        </span>
      </div>
      
      <div className="relative">
        <Progress value={strength.score} className="h-2" />
        <div 
          className={`absolute top-0 left-0 h-2 rounded-full transition-all duration-300 ${strength.color}`}
          style={{ width: `${strength.score}%` }}
        />
      </div>

      {showFeedback && strength.feedback.length > 0 && (
        <div className="space-y-1">
          <p className="text-xs text-gray-600 font-medium">To improve your password:</p>
          <ul className="text-xs text-gray-600 space-y-1">
            {strength.feedback.map((item, index) => (
              <li key={index} className="flex items-start">
                <XCircle className="h-3 w-3 text-red-500 mr-2 mt-0.5 flex-shrink-0" />
                <span>{item}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {showFeedback && strength.score >= 75 && (
        <div className="flex items-center text-xs text-green-600">
          <CheckCircle className="h-3 w-3 mr-1" />
          <span>Your password meets security requirements</span>
        </div>
      )}
    </div>
  );
};

export default PasswordStrengthMeter;
