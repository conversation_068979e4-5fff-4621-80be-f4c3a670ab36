<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create SEO Tables - Database Setup</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .sql-preview {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 15px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛠️ Create SEO Tables for SEOTools Component</h1>
        
        <div class="info status">
            <strong>Purpose:</strong> This tool will create the missing SEO-related database tables that your SEOTools component needs:
            <ul>
                <li><code>seo_analyses</code> - Stores SEO analysis results</li>
                <li><code>keyword_data</code> - Stores keyword research data</li>
                <li><code>competitor_analyses</code> - Stores competitor analysis results</li>
                <li><code>backlinks</code> - Stores backlink data</li>
            </ul>
        </div>

        <div id="status-container"></div>

        <div>
            <button onclick="createSEOTables()" id="createBtn">🚀 Create SEO Tables</button>
            <button onclick="testTables()" id="testBtn">🧪 Test Tables</button>
            <button onclick="clearLog()" id="clearBtn">🗑️ Clear Log</button>
        </div>

        <h3>📋 Execution Log</h3>
        <div id="log" class="log">Ready to create SEO tables...\n</div>

        <h3>📄 SQL Preview (First few statements)</h3>
        <div class="sql-preview">
-- SEO Analyses Table (plural - what SEOTools component expects)
CREATE TABLE IF NOT EXISTS seo_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    overall_score INTEGER NOT NULL DEFAULT 0,
    page_speed INTEGER DEFAULT 0,
    mobile_friendly INTEGER DEFAULT 0,
    security INTEGER DEFAULT 0,
    accessibility INTEGER DEFAULT 0,
    seo_score INTEGER DEFAULT 0,
    issues JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    meta_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Keyword Data Table
CREATE TABLE IF NOT EXISTS keyword_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword TEXT NOT NULL,
    volume TEXT,
    difficulty TEXT CHECK (difficulty IN ('Low', 'Medium', 'High')),
    cpc TEXT,
    competition DECIMAL(3,2) DEFAULT 0.0,
    trend TEXT CHECK (trend IN ('up', 'down', 'stable')),
    related_keywords JSONB DEFAULT '[]',
    questions JSONB DEFAULT '[]',
    serp_features JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ... and more tables
        </div>
    </div>

    <script type="module">
        // Import Supabase
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Initialize Supabase client
        const supabaseUrl = 'https://rclikclltlyzyojjttqv.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NjE1NzQsImV4cCI6MjA1MDUzNzU3NH0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw';
        
        const supabase = createClient(supabaseUrl, supabaseKey);

        // Make functions available globally
        window.createSEOTables = createSEOTables;
        window.testTables = testTables;
        window.clearLog = clearLog;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
            
            // Also show status
            showStatus(message, type);
        }

        function showStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            const statusDiv = document.createElement('div');
            statusDiv.className = `status ${type}`;
            statusDiv.textContent = message;
            container.appendChild(statusDiv);
            
            // Remove after 5 seconds
            setTimeout(() => {
                if (statusDiv.parentNode) {
                    statusDiv.parentNode.removeChild(statusDiv);
                }
            }, 5000);
        }

        function clearLog() {
            document.getElementById('log').textContent = 'Log cleared...\n';
            document.getElementById('status-container').innerHTML = '';
        }

        async function createSEOTables() {
            const createBtn = document.getElementById('createBtn');
            createBtn.disabled = true;
            createBtn.textContent = '⏳ Creating Tables...';

            try {
                log('🚀 Starting SEO tables creation...', 'info');

                // Define the SQL statements for creating SEO tables
                const sqlStatements = [
                    // Enable UUID extension
                    `CREATE EXTENSION IF NOT EXISTS "uuid-ossp"`,
                    
                    // SEO Analyses Table
                    `CREATE TABLE IF NOT EXISTS seo_analyses (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        url TEXT NOT NULL,
                        overall_score INTEGER NOT NULL DEFAULT 0,
                        page_speed INTEGER DEFAULT 0,
                        mobile_friendly INTEGER DEFAULT 0,
                        security INTEGER DEFAULT 0,
                        accessibility INTEGER DEFAULT 0,
                        seo_score INTEGER DEFAULT 0,
                        issues JSONB DEFAULT '[]',
                        recommendations JSONB DEFAULT '[]',
                        meta_data JSONB DEFAULT '{}',
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        CONSTRAINT valid_seo_url CHECK (url ~ '^https?://'),
                        CONSTRAINT valid_overall_score CHECK (overall_score >= 0 AND overall_score <= 100)
                    )`,

                    // Keyword Data Table
                    `CREATE TABLE IF NOT EXISTS keyword_data (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        keyword TEXT NOT NULL,
                        volume TEXT,
                        difficulty TEXT CHECK (difficulty IN ('Low', 'Medium', 'High')),
                        cpc TEXT,
                        competition DECIMAL(3,2) DEFAULT 0.0,
                        trend TEXT CHECK (trend IN ('up', 'down', 'stable')),
                        related_keywords JSONB DEFAULT '[]',
                        questions JSONB DEFAULT '[]',
                        serp_features JSONB DEFAULT '[]',
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    )`,

                    // Competitor Analyses Table
                    `CREATE TABLE IF NOT EXISTS competitor_analyses (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        competitor_url TEXT NOT NULL,
                        domain_authority INTEGER DEFAULT 0,
                        organic_keywords INTEGER DEFAULT 0,
                        organic_traffic TEXT,
                        backlinks INTEGER DEFAULT 0,
                        top_keywords JSONB DEFAULT '[]',
                        content_gaps JSONB DEFAULT '[]',
                        strengths JSONB DEFAULT '[]',
                        weaknesses JSONB DEFAULT '[]',
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        CONSTRAINT valid_competitor_url CHECK (competitor_url ~ '^https?://')
                    )`,

                    // Backlinks Table
                    `CREATE TABLE IF NOT EXISTS backlinks (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        source_url TEXT NOT NULL,
                        target_url TEXT NOT NULL,
                        anchor_text TEXT,
                        link_type TEXT DEFAULT 'dofollow' CHECK (link_type IN ('dofollow', 'nofollow')),
                        domain_authority INTEGER DEFAULT 0,
                        page_authority INTEGER DEFAULT 0,
                        spam_score INTEGER DEFAULT 0,
                        first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        CONSTRAINT valid_source_url CHECK (source_url ~ '^https?://'),
                        CONSTRAINT valid_target_url CHECK (target_url ~ '^https?://'),
                        UNIQUE(source_url, target_url)
                    )`
                ];

                log(`📝 Found ${sqlStatements.length} SQL statements to execute`, 'info');

                // Execute each statement
                for (let i = 0; i < sqlStatements.length; i++) {
                    const statement = sqlStatements[i];
                    log(`⚡ Executing statement ${i + 1}/${sqlStatements.length}...`);

                    try {
                        const { data, error } = await supabase.rpc('exec_sql', { 
                            sql_query: statement 
                        });

                        if (error) {
                            log(`⚠️ Statement ${i + 1} failed: ${error.message}`, 'warning');
                        } else {
                            log(`✅ Statement ${i + 1} executed successfully`, 'success');
                        }
                    } catch (err) {
                        log(`⚠️ Statement ${i + 1} exception: ${err.message}`, 'warning');
                    }
                }

                log('🎉 SEO tables creation completed!', 'success');
                showStatus('✅ SEO tables created successfully!', 'success');

            } catch (error) {
                log(`💥 Failed to create SEO tables: ${error.message}`, 'error');
                showStatus('❌ Failed to create SEO tables. Check the log for details.', 'error');
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = '🚀 Create SEO Tables';
            }
        }

        async function testTables() {
            const testBtn = document.getElementById('testBtn');
            testBtn.disabled = true;
            testBtn.textContent = '⏳ Testing...';

            try {
                log('🧪 Testing SEO tables...', 'info');

                const tables = ['seo_analyses', 'keyword_data', 'competitor_analyses', 'backlinks'];
                let allTablesExist = true;

                for (const table of tables) {
                    try {
                        const { data, error, count } = await supabase
                            .from(table)
                            .select('*', { count: 'exact', head: true });

                        if (error) {
                            log(`❌ ${table}: ${error.message}`, 'error');
                            allTablesExist = false;
                        } else {
                            log(`✅ ${table}: Table exists and accessible (${count || 0} rows)`, 'success');
                        }
                    } catch (err) {
                        log(`💥 ${table}: ${err.message}`, 'error');
                        allTablesExist = false;
                    }
                }

                if (allTablesExist) {
                    log('🎉 All SEO tables are working correctly!', 'success');
                    showStatus('✅ All SEO tables are working!', 'success');
                } else {
                    log('⚠️ Some tables have issues. You may need to create them manually.', 'warning');
                    showStatus('⚠️ Some tables have issues', 'warning');
                }

            } catch (error) {
                log(`💥 Failed to test tables: ${error.message}`, 'error');
                showStatus('❌ Failed to test tables', 'error');
            } finally {
                testBtn.disabled = false;
                testBtn.textContent = '🧪 Test Tables';
            }
        }

        // Auto-test on page load
        window.addEventListener('load', () => {
            setTimeout(testTables, 1000);
        });
    </script>
</body>
</html>
