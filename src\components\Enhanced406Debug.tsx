import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

interface TestResult {
  status: 'success' | 'error' | 'loading';
  data?: any;
  error?: any;
  message?: string;
}

const Enhanced406Debug: React.FC = () => {
  const [authStatus, setAuthStatus] = useState<string>('Checking...');
  const [userInfo, setUserInfo] = useState<any>(null);
  const [settingsResult, setSettingsResult] = useState<TestResult>({ status: 'loading' });
  const [profileResult, setProfileResult] = useState<TestResult>({ status: 'loading' });
  const [tableTests, setTableTests] = useState<Record<string, TestResult>>({});
  const [connectionTest, setConnectionTest] = useState<TestResult>({ status: 'loading' });
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    runAllTests();
  }, []);

  const runAllTests = async () => {
    setLoading(true);
    
    // Test 1: Basic connection
    await testConnection();
    
    // Test 2: Authentication
    await checkAuth();
    
    setLoading(false);
  };

  const testConnection = async () => {
    try {
      setConnectionTest({ status: 'loading', message: 'Testing connection...' });
      
      // Simple connection test
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        setConnectionTest({ 
          status: 'error', 
          error, 
          message: `Connection failed: ${error.message}` 
        });
      } else {
        setConnectionTest({ 
          status: 'success', 
          data, 
          message: 'Connection successful' 
        });
      }
    } catch (err: any) {
      setConnectionTest({ 
        status: 'error', 
        error: err, 
        message: `Connection exception: ${err.message}` 
      });
    }
  };

  const checkAuth = async () => {
    try {
      // Check authentication
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError) {
        setAuthStatus(`Auth Error: ${authError.message}`);
        return;
      }

      if (!user) {
        setAuthStatus('Not authenticated - Please sign in first');
        return;
      }

      setAuthStatus('Authenticated');
      setUserInfo(user);

      // Now run user-specific tests
      await checkUserProfile(user.id);
      await checkUserSettings(user.id);
      await testAllTables(user.id);

    } catch (err: any) {
      console.error('Auth check error:', err);
      setAuthStatus(`Exception: ${err.message}`);
    }
  };

  const checkUserProfile = async (userId: string) => {
    try {
      setProfileResult({ status: 'loading', message: 'Checking user profile...' });
      
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId);

      if (error) {
        setProfileResult({ 
          status: 'error', 
          error, 
          message: `Profile error: ${error.message}` 
        });
      } else {
        setProfileResult({ 
          status: 'success', 
          data, 
          message: `Profile found: ${data.length} records` 
        });
      }
    } catch (err: any) {
      setProfileResult({ 
        status: 'error', 
        error: err, 
        message: `Profile exception: ${err.message}` 
      });
    }
  };

  const checkUserSettings = async (userId: string) => {
    try {
      setSettingsResult({ status: 'loading', message: 'Checking user settings...' });
      
      // This is the exact query that's failing with 406
      const { data, error } = await supabase
        .from('user_settings')
        .select('*')
        .eq('user_id', userId);

      if (error) {
        setSettingsResult({ 
          status: 'error', 
          error, 
          message: `Settings error: ${error.message} (Code: ${error.code})` 
        });
      } else {
        setSettingsResult({ 
          status: 'success', 
          data, 
          message: `Settings found: ${data.length} records` 
        });
      }
    } catch (err: any) {
      setSettingsResult({ 
        status: 'error', 
        error: err, 
        message: `Settings exception: ${err.message}` 
      });
    }
  };

  const testAllTables = async (userId: string) => {
    const tables = ['user_settings', 'notification_settings', 'privacy_settings', 'security_settings'];
    const results: Record<string, TestResult> = {};
    
    for (const table of tables) {
      try {
        results[table] = { status: 'loading', message: `Testing ${table}...` };
        setTableTests({ ...results });
        
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .eq('user_id', userId);
        
        if (error) {
          results[table] = { 
            status: 'error', 
            error, 
            message: `${table}: ${error.message} (${error.code})` 
          };
        } else {
          results[table] = { 
            status: 'success', 
            data, 
            message: `${table}: ${data.length} records` 
          };
        }
      } catch (err: any) {
        results[table] = { 
          status: 'error', 
          error: err, 
          message: `${table}: Exception - ${err.message}` 
        };
      }
    }
    
    setTableTests(results);
  };

  const createTestSettings = async () => {
    if (!userInfo) {
      alert('Please authenticate first');
      return;
    }

    try {
      const { data, error } = await supabase
        .from('user_settings')
        .upsert([{
          user_id: userInfo.id,
          theme: 'system',
          language: 'en',
          timezone: 'America/New_York',
          notifications: {
            email: true,
            push: false,
            marketing: true
          },
          privacy: {
            analytics: true,
            data_collection: true
          }
        }])
        .select();

      if (error) {
        alert(`Create settings failed: ${error.message}`);
      } else {
        alert('Settings created successfully!');
        await checkUserSettings(userInfo.id);
      }
    } catch (err: any) {
      alert(`Create settings exception: ${err.message}`);
    }
  };

  const signInAnonymously = async () => {
    try {
      const { data, error } = await supabase.auth.signInAnonymously();
      
      if (error) {
        alert(`Anonymous sign-in failed: ${error.message}`);
      } else {
        alert('Signed in anonymously!');
        await runAllTests();
      }
    } catch (err: any) {
      alert(`Sign-in exception: ${err.message}`);
    }
  };

  const signOut = async () => {
    try {
      await supabase.auth.signOut();
      setAuthStatus('Not authenticated');
      setUserInfo(null);
      setSettingsResult({ status: 'loading' });
      setProfileResult({ status: 'loading' });
      setTableTests({});
    } catch (err: any) {
      alert(`Sign-out exception: ${err.message}`);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-50';
      case 'error': return 'text-red-600 bg-red-50';
      case 'loading': return 'text-blue-600 bg-blue-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">🔧 Enhanced 406 Error Debug Tool</h1>
      
      {/* Connection Status */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">🌐 Connection Status</h2>
        <div className={`p-2 rounded ${getStatusColor(connectionTest.status)}`}>
          {connectionTest.message}
        </div>
      </div>

      {/* Authentication */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">🔐 Authentication</h2>
        <div className="mb-2">
          <strong>Status:</strong> {authStatus}
        </div>
        {userInfo && (
          <div className="mb-2">
            <strong>User ID:</strong> {userInfo.id}
          </div>
        )}
        <div className="space-x-2">
          <button 
            onClick={signInAnonymously}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Sign In Anonymously
          </button>
          <button 
            onClick={signOut}
            className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
          >
            Sign Out
          </button>
          <button 
            onClick={runAllTests}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
          >
            Run All Tests
          </button>
        </div>
      </div>

      {/* User Profile Test */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">👤 User Profile Test</h2>
        <div className={`p-2 rounded ${getStatusColor(profileResult.status)}`}>
          {profileResult.message}
        </div>
        {profileResult.error && (
          <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
            {JSON.stringify(profileResult.error, null, 2)}
          </pre>
        )}
      </div>

      {/* User Settings Test (The main 406 issue) */}
      <div className="mb-6 p-4 border rounded-lg border-red-300">
        <h2 className="text-lg font-semibold mb-2">⚙️ User Settings Test (406 Issue)</h2>
        <div className={`p-2 rounded ${getStatusColor(settingsResult.status)}`}>
          {settingsResult.message}
        </div>
        {settingsResult.error && (
          <pre className="mt-2 p-2 bg-gray-100 rounded text-sm overflow-auto">
            {JSON.stringify(settingsResult.error, null, 2)}
          </pre>
        )}
        <button 
          onClick={createTestSettings}
          className="mt-2 px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
          disabled={!userInfo}
        >
          Create Test Settings
        </button>
      </div>

      {/* All Tables Test */}
      <div className="mb-6 p-4 border rounded-lg">
        <h2 className="text-lg font-semibold mb-2">📊 All Settings Tables Test</h2>
        {Object.entries(tableTests).map(([table, result]) => (
          <div key={table} className="mb-2">
            <div className={`p-2 rounded ${getStatusColor(result.status)}`}>
              <strong>{table}:</strong> {result.message}
            </div>
            {result.error && (
              <pre className="mt-1 p-2 bg-gray-100 rounded text-xs overflow-auto">
                {JSON.stringify(result.error, null, 2)}
              </pre>
            )}
          </div>
        ))}
      </div>

      {loading && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center">
          <div className="bg-white p-4 rounded-lg">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
            <p className="mt-2">Running tests...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Enhanced406Debug;
