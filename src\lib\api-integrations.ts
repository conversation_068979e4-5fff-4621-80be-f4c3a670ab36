// API integrations for external services

import { securityAudit } from './security'

export interface APICredentials {
  apiKey: string
  apiSecret?: string
  endpoint?: string
}

export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  rateLimitRemaining?: number
  rateLimitReset?: number
}

export interface WebhookConfig {
  url: string
  events: string[]
  secret?: string
  active: boolean
}

/**
 * Base API client class
 */
abstract class BaseAPIClient {
  protected credentials: APICredentials
  protected baseURL: string
  protected rateLimitRemaining = 100
  protected rateLimitReset = Date.now() + 3600000 // 1 hour

  constructor(credentials: APICredentials, baseURL: string) {
    this.credentials = credentials
    this.baseURL = baseURL
  }

  protected async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`
      const headers = {
        'Content-Type': 'application/json',
        'User-Agent': 'MarketCrawler-Pro/1.0',
        ...this.getAuthHeaders(),
        ...options.headers
      }

      securityAudit.log('API request initiated', { 
        endpoint, 
        method: options.method || 'GET' 
      }, 'low')

      const response = await fetch(url, {
        ...options,
        headers
      })

      // Update rate limit info
      this.updateRateLimitInfo(response)

      if (!response.ok) {
        const errorText = await response.text()
        securityAudit.log('API request failed', { 
          endpoint, 
          status: response.status,
          error: errorText 
        }, 'medium')
        
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`,
          rateLimitRemaining: this.rateLimitRemaining,
          rateLimitReset: this.rateLimitReset
        }
      }

      const data = await response.json()
      
      securityAudit.log('API request successful', { 
        endpoint, 
        status: response.status 
      }, 'low')

      return {
        success: true,
        data,
        rateLimitRemaining: this.rateLimitRemaining,
        rateLimitReset: this.rateLimitReset
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('API request error', { 
        endpoint, 
        error: errorMessage 
      }, 'high')

      return {
        success: false,
        error: errorMessage,
        rateLimitRemaining: this.rateLimitRemaining,
        rateLimitReset: this.rateLimitReset
      }
    }
  }

  protected abstract getAuthHeaders(): Record<string, string>

  private updateRateLimitInfo(response: Response): void {
    const remaining = response.headers.get('X-RateLimit-Remaining')
    const reset = response.headers.get('X-RateLimit-Reset')

    if (remaining) {
      this.rateLimitRemaining = parseInt(remaining, 10)
    }

    if (reset) {
      this.rateLimitReset = parseInt(reset, 10) * 1000 // Convert to milliseconds
    }
  }

  public getRateLimitInfo() {
    return {
      remaining: this.rateLimitRemaining,
      reset: this.rateLimitReset,
      resetDate: new Date(this.rateLimitReset)
    }
  }
}

/**
 * Slack API integration
 */
export class SlackAPIClient extends BaseAPIClient {
  constructor(credentials: APICredentials) {
    super(credentials, 'https://slack.com/api/')
  }

  protected getAuthHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.credentials.apiKey}`
    }
  }

  async sendMessage(channel: string, text: string, attachments?: any[]): Promise<APIResponse> {
    return this.makeRequest('chat.postMessage', {
      method: 'POST',
      body: JSON.stringify({
        channel,
        text,
        attachments
      })
    })
  }

  async uploadFile(channels: string, file: File, title?: string): Promise<APIResponse> {
    const formData = new FormData()
    formData.append('file', file)
    formData.append('channels', channels)
    if (title) formData.append('title', title)

    return this.makeRequest('files.upload', {
      method: 'POST',
      body: formData,
      headers: {} // Let browser set Content-Type for FormData
    })
  }
}

/**
 * Discord API integration
 */
export class DiscordAPIClient extends BaseAPIClient {
  constructor(credentials: APICredentials) {
    super(credentials, 'https://discord.com/api/v10/')
  }

  protected getAuthHeaders(): Record<string, string> {
    return {
      'Authorization': `Bot ${this.credentials.apiKey}`
    }
  }

  async sendMessage(channelId: string, content: string, embeds?: any[]): Promise<APIResponse> {
    return this.makeRequest(`channels/${channelId}/messages`, {
      method: 'POST',
      body: JSON.stringify({
        content,
        embeds
      })
    })
  }
}

/**
 * Zapier webhook integration
 */
export class ZapierWebhook {
  private webhookUrl: string

  constructor(webhookUrl: string) {
    this.webhookUrl = webhookUrl
  }

  async trigger(data: any): Promise<APIResponse> {
    try {
      securityAudit.log('Zapier webhook triggered', { 
        url: this.webhookUrl.substring(0, 50) + '...' 
      }, 'low')

      const response = await fetch(this.webhookUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      })

      if (!response.ok) {
        const errorText = await response.text()
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`
        }
      }

      return {
        success: true,
        data: await response.text()
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('Zapier webhook error', { error: errorMessage }, 'medium')
      
      return {
        success: false,
        error: errorMessage
      }
    }
  }
}

/**
 * Email service integration (using a generic email API)
 */
export class EmailAPIClient extends BaseAPIClient {
  constructor(credentials: APICredentials, provider: 'sendgrid' | 'mailgun' | 'ses' = 'sendgrid') {
    const baseURLs = {
      sendgrid: 'https://api.sendgrid.com/v3/',
      mailgun: 'https://api.mailgun.net/v3/',
      ses: 'https://email.us-east-1.amazonaws.com/'
    }
    super(credentials, baseURLs[provider])
  }

  protected getAuthHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.credentials.apiKey}`
    }
  }

  async sendEmail(
    to: string | string[],
    subject: string,
    content: string,
    isHTML = false
  ): Promise<APIResponse> {
    const emailData = {
      personalizations: [{
        to: Array.isArray(to) ? to.map(email => ({ email })) : [{ email: to }]
      }],
      from: { email: '<EMAIL>', name: 'MarketCrawler Pro' },
      subject,
      content: [{
        type: isHTML ? 'text/html' : 'text/plain',
        value: content
      }]
    }

    return this.makeRequest('mail/send', {
      method: 'POST',
      body: JSON.stringify(emailData)
    })
  }
}

/**
 * Google Sheets API integration
 */
export class GoogleSheetsAPIClient extends BaseAPIClient {
  constructor(credentials: APICredentials) {
    super(credentials, 'https://sheets.googleapis.com/v4/')
  }

  protected getAuthHeaders(): Record<string, string> {
    return {
      'Authorization': `Bearer ${this.credentials.apiKey}`
    }
  }

  async appendData(
    spreadsheetId: string,
    range: string,
    values: any[][]
  ): Promise<APIResponse> {
    return this.makeRequest(
      `spreadsheets/${spreadsheetId}/values/${range}:append?valueInputOption=RAW`,
      {
        method: 'POST',
        body: JSON.stringify({
          values
        })
      }
    )
  }

  async createSpreadsheet(title: string): Promise<APIResponse> {
    return this.makeRequest('spreadsheets', {
      method: 'POST',
      body: JSON.stringify({
        properties: {
          title
        }
      })
    })
  }
}

/**
 * Integration manager
 */
export class IntegrationManager {
  private static instance: IntegrationManager
  private integrations: Map<string, any> = new Map()

  static getInstance(): IntegrationManager {
    if (!IntegrationManager.instance) {
      IntegrationManager.instance = new IntegrationManager()
    }
    return IntegrationManager.instance
  }

  registerIntegration(name: string, client: any): void {
    this.integrations.set(name, client)
    securityAudit.log('Integration registered', { name }, 'low')
  }

  getIntegration<T>(name: string): T | null {
    return this.integrations.get(name) || null
  }

  async testIntegration(name: string): Promise<boolean> {
    const integration = this.integrations.get(name)
    if (!integration) return false

    try {
      // Test the integration based on its type
      if (integration instanceof SlackAPIClient) {
        const result = await integration.makeRequest('auth.test')
        return result.success
      }
      
      if (integration instanceof DiscordAPIClient) {
        const result = await integration.makeRequest('users/@me')
        return result.success
      }

      if (integration instanceof GoogleSheetsAPIClient) {
        const result = await integration.makeRequest('spreadsheets')
        return result.success
      }

      return true
    } catch (error) {
      securityAudit.log('Integration test failed', { name, error }, 'medium')
      return false
    }
  }

  listIntegrations(): string[] {
    return Array.from(this.integrations.keys())
  }

  removeIntegration(name: string): boolean {
    const removed = this.integrations.delete(name)
    if (removed) {
      securityAudit.log('Integration removed', { name }, 'low')
    }
    return removed
  }
}

/**
 * Webhook manager for handling incoming webhooks
 */
export class WebhookManager {
  private static instance: WebhookManager
  private webhooks: Map<string, WebhookConfig> = new Map()
  private handlers: Map<string, (data: any) => Promise<void>> = new Map()

  static getInstance(): WebhookManager {
    if (!WebhookManager.instance) {
      WebhookManager.instance = new WebhookManager()
    }
    return WebhookManager.instance
  }

  registerWebhook(id: string, config: WebhookConfig): void {
    this.webhooks.set(id, config)
    securityAudit.log('Webhook registered', { id, url: config.url }, 'low')
  }

  registerHandler(event: string, handler: (data: any) => Promise<void>): void {
    this.handlers.set(event, handler)
  }

  async handleWebhook(id: string, event: string, data: any): Promise<boolean> {
    const webhook = this.webhooks.get(id)
    if (!webhook || !webhook.active) {
      securityAudit.log('Webhook not found or inactive', { id, event }, 'medium')
      return false
    }

    if (!webhook.events.includes(event)) {
      securityAudit.log('Event not configured for webhook', { id, event }, 'low')
      return false
    }

    const handler = this.handlers.get(event)
    if (!handler) {
      securityAudit.log('No handler for webhook event', { id, event }, 'medium')
      return false
    }

    try {
      await handler(data)
      securityAudit.log('Webhook handled successfully', { id, event }, 'low')
      return true
    } catch (error) {
      securityAudit.log('Webhook handler error', { 
        id, 
        event, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }, 'high')
      return false
    }
  }

  listWebhooks(): Array<{ id: string; config: WebhookConfig }> {
    return Array.from(this.webhooks.entries()).map(([id, config]) => ({ id, config }))
  }
}

// Export singleton instances
export const integrationManager = IntegrationManager.getInstance()
export const webhookManager = WebhookManager.getInstance()

// Utility functions
export function createSlackIntegration(apiKey: string): SlackAPIClient {
  return new SlackAPIClient({ apiKey })
}

export function createDiscordIntegration(botToken: string): DiscordAPIClient {
  return new DiscordAPIClient({ apiKey: botToken })
}

export function createEmailIntegration(
  apiKey: string, 
  provider: 'sendgrid' | 'mailgun' | 'ses' = 'sendgrid'
): EmailAPIClient {
  return new EmailAPIClient({ apiKey }, provider)
}

export function createGoogleSheetsIntegration(accessToken: string): GoogleSheetsAPIClient {
  return new GoogleSheetsAPIClient({ apiKey: accessToken })
}

export function createZapierWebhook(webhookUrl: string): ZapierWebhook {
  return new ZapierWebhook(webhookUrl)
}
