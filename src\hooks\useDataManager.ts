import { useState, useCallback, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { useError<PERSON>and<PERSON> } from './useErrorHandler';

export interface DataManagerOptions<T> {
  tableName: string;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  limit?: number;
  filters?: Record<string, any>;
  realtime?: boolean;
}

export interface DataManagerState<T> {
  data: T[];
  loading: boolean;
  error: string | null;
  total: number;
}

export const useDataManager = <T extends { id: string }>(
  options: DataManagerOptions<T>
) => {
  const { tableName, orderBy = 'created_at', orderDirection = 'desc', limit, filters, realtime = false } = options;
  const { showError, showSuccess } = useErrorHandler();

  const [state, setState] = useState<DataManagerState<T>>({
    data: [],
    loading: false,
    error: null,
    total: 0
  });

  const loadData = useCallback(async () => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      let query = supabase
        .from(tableName)
        .select('*', { count: 'exact' })
        .order(orderBy, { ascending: orderDirection === 'asc' });

      if (limit) {
        query = query.limit(limit);
      }

      if (filters) {
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value);
          }
        });
      }

      const { data, error, count } = await query;

      if (error) throw error;

      setState(prev => ({
        ...prev,
        data: data || [],
        total: count || 0,
        loading: false
      }));

      return data || [];
    } catch (error) {
      const errorMessage = showError(error, `Load ${tableName}`, { showToast: false });
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false
      }));
      throw error;
    }
  }, [tableName, orderBy, orderDirection, limit, filters, showError]);

  const createItem = useCallback(async (item: Omit<T, 'id'>) => {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .insert([item])
        .select()
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        data: [data, ...prev.data],
        total: prev.total + 1
      }));

      showSuccess(`${tableName} created successfully`);
      return data;
    } catch (error) {
      showError(error, `Create ${tableName}`);
      throw error;
    }
  }, [tableName, showError, showSuccess]);

  const updateItem = useCallback(async (id: string, updates: Partial<T>) => {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .update(updates)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      setState(prev => ({
        ...prev,
        data: prev.data.map(item => item.id === id ? { ...item, ...data } : item)
      }));

      showSuccess(`${tableName} updated successfully`);
      return data;
    } catch (error) {
      showError(error, `Update ${tableName}`);
      throw error;
    }
  }, [tableName, showError, showSuccess]);

  const deleteItem = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from(tableName)
        .delete()
        .eq('id', id);

      if (error) throw error;

      setState(prev => ({
        ...prev,
        data: prev.data.filter(item => item.id !== id),
        total: prev.total - 1
      }));

      showSuccess(`${tableName} deleted successfully`);
    } catch (error) {
      showError(error, `Delete ${tableName}`);
      throw error;
    }
  }, [tableName, showError, showSuccess]);

  const refreshData = useCallback(() => {
    return loadData();
  }, [loadData]);

  // Load data on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Set up realtime subscription if enabled
  useEffect(() => {
    if (!realtime) return;

    const subscription = supabase
      .channel(`${tableName}_changes`)
      .on('postgres_changes', 
        { event: '*', schema: 'public', table: tableName },
        (payload) => {
          if (payload.eventType === 'INSERT') {
            setState(prev => ({
              ...prev,
              data: [payload.new as T, ...prev.data],
              total: prev.total + 1
            }));
          } else if (payload.eventType === 'UPDATE') {
            setState(prev => ({
              ...prev,
              data: prev.data.map(item => 
                item.id === payload.new.id ? payload.new as T : item
              )
            }));
          } else if (payload.eventType === 'DELETE') {
            setState(prev => ({
              ...prev,
              data: prev.data.filter(item => item.id !== payload.old.id),
              total: prev.total - 1
            }));
          }
        }
      )
      .subscribe();

    return () => {
      subscription.unsubscribe();
    };
  }, [tableName, realtime]);

  return {
    ...state,
    loadData,
    createItem,
    updateItem,
    deleteItem,
    refreshData
  };
};
