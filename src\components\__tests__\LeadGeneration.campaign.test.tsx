import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { vi } from 'vitest';
import LeadGeneration from '../LeadGeneration';

// Mock the supabase client
vi.mock('@/lib/supabase', () => ({
  supabase: {
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        order: vi.fn(() => ({
          then: vi.fn()
        }))
      })),
      insert: vi.fn(() => ({
        select: vi.fn(() => ({
          single: vi.fn(() => Promise.resolve({
            data: {
              id: 'test-id',
              name: 'Test Campaign',
              description: 'Test Description',
              status: 'Draft',
              leads: 0,
              qualified: 0,
              converted: 0,
              conversion_rate: 0,
              target_criteria: {},
              created_at: new Date().toISOString()
            },
            error: null
          }))
        }))
      }))
    }))
  }
}));

// Mock the hooks
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

vi.mock('@/hooks/useErrorHandler', () => ({
  useErrorHandler: () => ({
    showError: vi.fn(),
    showSuccess: vi.fn()
  })
}));

describe('LeadGeneration Campaign Functionality', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('should render New Campaign button', async () => {
    render(<LeadGeneration />);
    
    // Click on the Campaigns tab
    const campaignsTab = screen.getByRole('tab', { name: /campaigns/i });
    fireEvent.click(campaignsTab);
    
    // Check if New Campaign button exists
    const newCampaignButton = screen.getByRole('button', { name: /new campaign/i });
    expect(newCampaignButton).toBeInTheDocument();
  });

  test('should open campaign creation dialog when New Campaign button is clicked', async () => {
    render(<LeadGeneration />);
    
    // Click on the Campaigns tab
    const campaignsTab = screen.getByRole('tab', { name: /campaigns/i });
    fireEvent.click(campaignsTab);
    
    // Click the New Campaign button
    const newCampaignButton = screen.getByRole('button', { name: /new campaign/i });
    fireEvent.click(newCampaignButton);
    
    // Check if dialog opened
    await waitFor(() => {
      expect(screen.getByText('Create New Campaign')).toBeInTheDocument();
      expect(screen.getByText('Set up a new lead generation campaign with targeting criteria')).toBeInTheDocument();
    });
  });

  test('should have required form fields in campaign creation dialog', async () => {
    render(<LeadGeneration />);
    
    // Navigate to campaigns and open dialog
    const campaignsTab = screen.getByRole('tab', { name: /campaigns/i });
    fireEvent.click(campaignsTab);
    
    const newCampaignButton = screen.getByRole('button', { name: /new campaign/i });
    fireEvent.click(newCampaignButton);
    
    // Check for required fields
    await waitFor(() => {
      expect(screen.getByLabelText(/campaign name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /create campaign/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /cancel/i })).toBeInTheDocument();
    });
  });

  test('should have optional targeting criteria fields', async () => {
    render(<LeadGeneration />);
    
    // Navigate to campaigns and open dialog
    const campaignsTab = screen.getByRole('tab', { name: /campaigns/i });
    fireEvent.click(campaignsTab);
    
    const newCampaignButton = screen.getByRole('button', { name: /new campaign/i });
    fireEvent.click(newCampaignButton);
    
    // Check for targeting criteria fields
    await waitFor(() => {
      expect(screen.getByLabelText(/industries/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/job titles/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/company sizes/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/locations/i)).toBeInTheDocument();
    });
  });

  test('should allow filling out campaign form', async () => {
    render(<LeadGeneration />);
    
    // Navigate to campaigns and open dialog
    const campaignsTab = screen.getByRole('tab', { name: /campaigns/i });
    fireEvent.click(campaignsTab);
    
    const newCampaignButton = screen.getByRole('button', { name: /new campaign/i });
    fireEvent.click(newCampaignButton);
    
    await waitFor(() => {
      const nameInput = screen.getByLabelText(/campaign name/i);
      const descriptionInput = screen.getByLabelText(/description/i);
      
      // Fill out the form
      fireEvent.change(nameInput, { target: { value: 'Test Campaign' } });
      fireEvent.change(descriptionInput, { target: { value: 'Test Description' } });
      
      expect(nameInput).toHaveValue('Test Campaign');
      expect(descriptionInput).toHaveValue('Test Description');
    });
  });
});
