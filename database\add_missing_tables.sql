-- Migration to add missing tables for notifications and user settings
-- Run this in your Supabase SQL Editor

-- User Settings Table
-- Stores user preferences and application settings
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    theme TEXT NOT NULL DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    language TEXT NOT NULL DEFAULT 'en',
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    notifications JSONB NOT NULL DEFAULT '{
        "email": true,
        "push": false,
        "marketing": true
    }',
    privacy JSONB NOT NULL DEFAULT '{
        "analytics": true,
        "data_collection": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one settings record per user
    UNIQUE(user_id)
);

-- Notifications Table
-- Stores user notifications
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    read BOOLEAN NOT NULL DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')
);

-- Saved Keywords Table
-- Stores user-saved keywords from keyword research
CREATE TABLE IF NOT EXISTS saved_keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    keyword TEXT NOT NULL,
    volume TEXT,
    difficulty INTEGER,
    cpc TEXT,
    trend TEXT CHECK (trend IN ('up', 'down', 'stable')),
    competition TEXT CHECK (competition IN ('High', 'Medium', 'Low')),
    intent TEXT CHECK (intent IN ('Commercial', 'Informational', 'Navigational', 'Transactional')),
    serp_features JSONB DEFAULT '[]',
    related_keywords JSONB DEFAULT '[]',
    questions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate keywords per user
    UNIQUE(user_id, keyword)
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON notifications(expires_at);

CREATE INDEX IF NOT EXISTS idx_saved_keywords_user_id ON saved_keywords(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_keywords_keyword ON saved_keywords(keyword);
CREATE INDEX IF NOT EXISTS idx_saved_keywords_created_at ON saved_keywords(created_at DESC);

-- Enable Row Level Security
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_keywords ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can manage own settings" ON user_settings FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own notifications" ON notifications FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own saved keywords" ON saved_keywords FOR ALL USING (auth.uid() = user_id);

-- For demo purposes, also allow anonymous access (remove in production)
CREATE POLICY "Allow anonymous access to user_settings" ON user_settings FOR ALL USING (true);
CREATE POLICY "Allow anonymous access to notifications" ON notifications FOR ALL USING (true);
CREATE POLICY "Allow anonymous access to saved_keywords" ON saved_keywords FOR ALL USING (true);
