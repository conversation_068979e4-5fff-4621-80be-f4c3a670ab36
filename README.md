# MarketCrawler Pro - Web Intelligence Suite

A comprehensive web scraping and marketing analysis platform built with React, TypeScript, and Supabase. Transform web data into powerful marketing insights and automated campaigns.

## 🚀 Features

### Core Functionality
- **Advanced Web Scraping**: Multi-strategy scraping with CORS handling and fallback methods
- **SEO Analysis Engine**: Comprehensive SEO scoring with detailed recommendations
- **Marketing Tools Suite**: Keyword extraction, lead generation, content ideas, and social media posts
- **Real-time Analytics**: Interactive dashboards with data visualization
- **Security-First Design**: Input validation, rate limiting, and XSS protection

### Technical Highlights
- **Modern React Stack**: React 18, TypeScript, Vite, and ShadCN/UI
- **Performance Optimized**: Code splitting, lazy loading, and service worker caching
- **Production Ready**: Comprehensive testing, error handling, and monitoring
- **Scalable Architecture**: Modular design with proper separation of concerns

## 📋 Table of Contents

- [Quick Start](#quick-start)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Documentation](#documentation)
- [Contributing](#contributing)
- [License](#license)

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- Supabase account and project
- Git

### 1. Clone and Install

```bash
git clone <repository-url>
cd scraping-analysis-marketing
npm install
```

### 2. Environment Setup

```bash
cp .env.example .env
# Edit .env with your Supabase credentials
```

### 3. Database Setup

```bash
# Run the database schema in your Supabase SQL editor
# See database/README.md for detailed instructions
```

### 4. Start Development

```bash
npm run dev
```

Visit `http://localhost:5173` to see the application.

## 📦 Installation

### System Requirements

- **Node.js**: 18.0.0 or higher
- **npm**: 9.0.0 or higher
- **Browser**: Modern browser with ES2020 support
- **Memory**: 4GB RAM minimum for development

### Development Installation

```bash
# Clone the repository
git clone <repository-url>
cd scraping-analysis-marketing

# Install dependencies
npm install

# Install development tools (optional)
npm install -g @playwright/test
npm install -g lighthouse
```

### Production Installation

```bash
# Install production dependencies only
npm ci --only=production

# Build the application
npm run build

# Preview the build
npm run preview
```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# Supabase Configuration
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key-here
VITE_SUPABASE_FUNCTIONS_URL=https://your-project.supabase.co/functions/v1

# Application Configuration
VITE_APP_NAME=Scraping Analysis Marketing
VITE_APP_VERSION=1.0.0

# Development Configuration
VITE_DEV_MODE=true
VITE_LOG_LEVEL=debug
```

### Supabase Setup

1. Create a new Supabase project at [supabase.com](https://supabase.com)
2. Copy your project URL and anon key from Settings → API
3. Run the database schema from `database/schema.sql`
4. Configure Row Level Security policies as needed

### Build Configuration

The application uses Vite with optimized build settings:

- **Code Splitting**: Automatic vendor and component chunking
- **Tree Shaking**: Unused code elimination
- **Compression**: Gzip and Brotli compression
- **Source Maps**: Available in development mode
- **Bundle Analysis**: Use `npm run build && npx vite-bundle-analyzer dist`

## 🎯 Usage

### Web Scraping

1. **Navigate to Web Scraper tab**
2. **Enter target URLs** (up to 10 per batch)
3. **Click "Start Scraping"** to begin extraction
4. **Monitor progress** with real-time updates
5. **View results** in the Data Results tab

### SEO Analysis

1. **Go to Analytics tab**
2. **Click "Run SEO Analysis"**
3. **Review comprehensive scores** for:
   - Title optimization
   - Content analysis
   - Technical SEO
   - Keyword density
4. **Follow recommendations** for improvements

### Marketing Tools

1. **Access Marketing Tools tab**
2. **Choose from 4 tools**:
   - **SEO Keywords**: Extract and analyze keywords
   - **Lead Generation**: Find contact information
   - **Content Ideas**: Generate content suggestions
   - **Social Posts**: Create social media content
3. **Export results** as CSV or copy to clipboard

### Data Management

- **View all scraped data** in the Data Results tab
- **Filter and search** through results
- **Sort by various criteria** (date, status, word count)
- **Export data** for external analysis

## 🛠️ Development

### Development Server

```bash
# Start development server
npm run dev

# Start with specific port
npm run dev -- --port 3000

# Start with host binding
npm run dev -- --host 0.0.0.0
```

### Code Quality

```bash
# Lint code
npm run lint

# Fix linting issues
npm run lint -- --fix

# Type checking
npx tsc --noEmit
```

### Project Structure

```
src/
├── components/          # React components
│   ├── ui/             # ShadCN/UI components
│   └── __tests__/      # Component tests
├── hooks/              # Custom React hooks
├── lib/                # Utility libraries
│   ├── scraping.ts     # Web scraping engine
│   ├── security.ts     # Security utilities
│   └── performance.ts  # Performance utilities
├── pages/              # Page components
├── types/              # TypeScript type definitions
└── contexts/           # React contexts
```

### Adding New Features

1. **Create feature branch**: `git checkout -b feature/new-feature`
2. **Add components**: Follow existing patterns and naming
3. **Add tests**: Write unit and integration tests
4. **Update types**: Add TypeScript definitions
5. **Document changes**: Update relevant documentation
6. **Submit PR**: Include tests and documentation

## 🧪 Testing

### Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui
```

### Test Types

- **Unit Tests**: Component and utility function tests
- **Integration Tests**: API and database integration tests
- **E2E Tests**: Full user workflow tests
- **Performance Tests**: Bundle size and runtime performance
- **Security Tests**: Input validation and XSS protection

### Writing Tests

```typescript
// Component test example
import { render, screen } from '@/test/utils'
import MyComponent from '../MyComponent'

test('renders correctly', () => {
  render(<MyComponent />)
  expect(screen.getByText('Expected Text')).toBeInTheDocument()
})
```

### Test Coverage

Current coverage targets:
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

## 🚀 Deployment

### Build for Production

```bash
# Create production build
npm run build

# Preview production build locally
npm run preview

# Analyze bundle size
npx vite-bundle-analyzer dist
```

### Deployment Options

#### Vercel (Recommended)

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel

# Set environment variables in Vercel dashboard
```

#### Netlify

```bash
# Install Netlify CLI
npm i -g netlify-cli

# Build and deploy
npm run build
netlify deploy --prod --dir=dist
```

#### Docker

```dockerfile
# Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### Static Hosting

The built application in `dist/` can be deployed to any static hosting service:
- GitHub Pages
- AWS S3 + CloudFront
- Google Cloud Storage
- Azure Static Web Apps

### Environment Configuration

Set these environment variables in your deployment platform:

```env
VITE_SUPABASE_URL=your-production-supabase-url
VITE_SUPABASE_ANON_KEY=your-production-anon-key
VITE_SUPABASE_FUNCTIONS_URL=your-production-functions-url
VITE_APP_NAME=MarketCrawler Pro
VITE_APP_VERSION=1.0.0
VITE_DEV_MODE=false
VITE_LOG_LEVEL=error
```

### Performance Optimization

- **Enable compression** (Gzip/Brotli) on your server
- **Configure caching headers** for static assets
- **Use CDN** for global content delivery
- **Enable HTTP/2** for better performance
- **Monitor Core Web Vitals** with tools like Lighthouse

## 📚 Documentation

### Available Documentation

- **[Database Setup](database/README.md)**: Database schema and setup guide
- **[Testing Guide](docs/TESTING.md)**: Comprehensive testing documentation
- **[Performance Guide](docs/PERFORMANCE.md)**: Performance optimization guide
- **[Security Guide](docs/SECURITY.md)**: Security best practices and implementation
- **[API Documentation](docs/API.md)**: API endpoints and usage (coming soon)

### Architecture Documentation

- **Component Architecture**: Modular React components with TypeScript
- **State Management**: React Context + React Query for server state
- **Database Design**: Supabase with PostgreSQL and Row Level Security
- **Security Model**: Input validation, rate limiting, and XSS protection
- **Performance Strategy**: Code splitting, caching, and optimization

## 🤝 Contributing

### Development Workflow

1. **Fork the repository**
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes**: Follow coding standards and add tests
4. **Run tests**: Ensure all tests pass
5. **Commit changes**: Use conventional commit messages
6. **Push to branch**: `git push origin feature/amazing-feature`
7. **Open Pull Request**: Include description and testing notes

### Coding Standards

- **TypeScript**: Strict mode enabled with comprehensive types
- **ESLint**: Configured with React and TypeScript rules
- **Prettier**: Code formatting (configured in package.json)
- **Conventional Commits**: Use conventional commit message format
- **Testing**: Write tests for new features and bug fixes

### Bug Reports

When reporting bugs, please include:
- **Environment details** (OS, browser, Node.js version)
- **Steps to reproduce** the issue
- **Expected vs actual behavior**
- **Screenshots or error messages** if applicable
- **Minimal reproduction case** if possible

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **[Supabase](https://supabase.com)** - Backend as a Service
- **[ShadCN/UI](https://ui.shadcn.com)** - UI Component Library
- **[Vite](https://vitejs.dev)** - Build Tool
- **[React](https://reactjs.org)** - UI Framework
- **[TypeScript](https://typescriptlang.org)** - Type Safety
- **[Tailwind CSS](https://tailwindcss.com)** - Styling Framework

## 📞 Support

- **Documentation**: Check the docs/ directory for detailed guides
- **Issues**: Report bugs and request features via GitHub Issues
- **Discussions**: Join community discussions in GitHub Discussions
- **Security**: Report security <NAME_EMAIL>

---

**Built with ❤️ by the MarketCrawler Pro team**
