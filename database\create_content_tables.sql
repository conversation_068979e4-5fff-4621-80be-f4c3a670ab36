-- Create missing tables for Content Ideas functionality
-- This script creates the tables needed by the ContentIdeas component

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (for clean recreation)
DROP TABLE IF EXISTS content_calendar CASCADE;
DROP TABLE IF EXISTS trending_topics CASCADE;
DROP TABLE IF EXISTS content_templates CASCADE;

-- Update the existing content_ideas table to match the component interface
-- First, let's check if it exists and update its structure
DO $$
BEGIN
    -- Drop and recreate content_ideas table with the correct structure
    DROP TABLE IF EXISTS content_ideas CASCADE;
    
    CREATE TABLE content_ideas (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        title TEXT NOT NULL,
        type TEXT NOT NULL CHECK (type IN ('Blog Post', 'Video', 'Podcast', 'Infographic', 'Social Media', 'Email', 'Webinar', 'Case Study')),
        difficulty TEXT NOT NULL CHECK (difficulty IN ('Easy', 'Medium', 'Hard')),
        estimated_time TEXT NOT NULL DEFAULT '30 min',
        keywords JSONB DEFAULT '[]',
        trending BOOLEAN DEFAULT false,
        engagement TEXT CHECK (engagement IN ('High', 'Medium', 'Low')) DEFAULT 'Medium',
        description TEXT,
        outline JSONB DEFAULT '[]',
        target_audience TEXT,
        content_pillars JSONB DEFAULT '[]',
        seo_score INTEGER DEFAULT 0 CHECK (seo_score >= 0 AND seo_score <= 100),
        status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Draft', 'In Progress', 'Published', 'Archived')),
        performance JSONB DEFAULT '{
            "views": 0,
            "likes": 0,
            "shares": 0,
            "comments": 0,
            "engagement_rate": 0
        }',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
    );
END $$;

-- Trending Topics Table
CREATE TABLE trending_topics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    topic TEXT NOT NULL,
    growth TEXT NOT NULL,
    volume TEXT NOT NULL,
    category TEXT NOT NULL,
    related_keywords JSONB DEFAULT '[]',
    content_opportunities INTEGER DEFAULT 0,
    competition_level TEXT CHECK (competition_level IN ('Low', 'Medium', 'High')) DEFAULT 'Medium',
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Templates Table
CREATE TABLE content_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type TEXT NOT NULL,
    description TEXT,
    structure JSONB DEFAULT '[]',
    usage_count INTEGER DEFAULT 0 CHECK (usage_count >= 0),
    category TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content Calendar Table
CREATE TABLE content_calendar (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    type TEXT NOT NULL,
    scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status TEXT NOT NULL DEFAULT 'Planned' CHECK (status IN ('Planned', 'In Progress', 'Ready', 'Published')),
    assigned_to TEXT,
    content_idea_id UUID REFERENCES content_ideas(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_content_ideas_type ON content_ideas(type);
CREATE INDEX idx_content_ideas_difficulty ON content_ideas(difficulty);
CREATE INDEX idx_content_ideas_status ON content_ideas(status);
CREATE INDEX idx_content_ideas_created_at ON content_ideas(created_at);
CREATE INDEX idx_trending_topics_category ON trending_topics(category);
CREATE INDEX idx_trending_topics_competition ON trending_topics(competition_level);
CREATE INDEX idx_content_templates_category ON content_templates(category);
CREATE INDEX idx_content_calendar_scheduled_date ON content_calendar(scheduled_date);
CREATE INDEX idx_content_calendar_status ON content_calendar(status);

-- Insert sample data for testing
INSERT INTO content_ideas (title, type, difficulty, estimated_time, keywords, trending, engagement, description, outline, target_audience, content_pillars, seo_score, status) VALUES
('The Ultimate Guide to Web Scraping Ethics', 'Blog Post', 'Medium', '45 min', '["web scraping", "ethics", "best practices"]', true, 'High', 'A comprehensive guide covering ethical considerations in web scraping', '["Introduction to Ethics", "Legal Considerations", "Best Practices", "Case Studies"]', 'Developers and Data Scientists', '["Education", "Best Practices"]', 85, 'Draft'),
('10 Web Scraping Tools Every Developer Should Know', 'Blog Post', 'Easy', '30 min', '["web scraping", "tools", "development"]', false, 'Medium', 'A curated list of essential web scraping tools', '["Introduction", "Tool Categories", "Detailed Reviews", "Comparison Chart"]', 'Developers', '["Tools", "Education"]', 78, 'Draft'),
('Building Your First Web Scraper: Video Tutorial', 'Video', 'Easy', '25 min', '["tutorial", "beginner", "web scraping"]', true, 'High', 'Step-by-step video guide for beginners', '["Setup", "Basic Concepts", "Hands-on Example", "Best Practices"]', 'Beginners', '["Education", "Tutorial"]', 82, 'In Progress');

INSERT INTO trending_topics (topic, growth, volume, category, related_keywords, content_opportunities, competition_level) VALUES
('AI-powered web scraping', '+156%', '12.5K', 'Technology', '["AI", "machine learning", "automation"]', 15, 'Medium'),
('No-code data extraction', '+89%', '8.2K', 'Tools', '["no-code", "visual scraping", "drag-drop"]', 12, 'Low'),
('Web scraping for e-commerce', '+67%', '15.3K', 'Business', '["e-commerce", "price monitoring", "competitor analysis"]', 18, 'High'),
('Python web scraping libraries', '+45%', '22.1K', 'Programming', '["Python", "BeautifulSoup", "Scrapy", "Selenium"]', 25, 'Medium');

INSERT INTO content_templates (name, type, description, structure, usage_count, category) VALUES
('How-to Guide Template', 'Blog Post', 'Template for creating step-by-step guides', '["Introduction", "Prerequisites", "Step-by-step Instructions", "Troubleshooting", "Conclusion"]', 5, 'Educational'),
('Tool Review Template', 'Blog Post', 'Template for reviewing software tools', '["Overview", "Features", "Pros and Cons", "Pricing", "Verdict"]', 3, 'Review'),
('Tutorial Video Template', 'Video', 'Structure for educational videos', '["Introduction", "Setup", "Demo", "Key Points", "Next Steps"]', 8, 'Educational'),
('Case Study Template', 'Case Study', 'Template for business case studies', '["Challenge", "Solution", "Implementation", "Results", "Lessons Learned"]', 2, 'Business');

INSERT INTO content_calendar (title, type, scheduled_date, status, content_idea_id) VALUES
('Web Scraping Ethics Guide', 'Blog Post', NOW() + INTERVAL '7 days', 'Planned', (SELECT id FROM content_ideas WHERE title = 'The Ultimate Guide to Web Scraping Ethics')),
('Developer Tools Roundup', 'Blog Post', NOW() + INTERVAL '14 days', 'Planned', (SELECT id FROM content_ideas WHERE title = '10 Web Scraping Tools Every Developer Should Know')),
('Beginner Tutorial Video', 'Video', NOW() + INTERVAL '21 days', 'In Progress', (SELECT id FROM content_ideas WHERE title = 'Building Your First Web Scraper: Video Tutorial'));

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE content_ideas ENABLE ROW LEVEL SECURITY;
ALTER TABLE trending_topics ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_calendar ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for now (you can make these more restrictive later)
CREATE POLICY "Allow all operations on content_ideas" ON content_ideas FOR ALL USING (true);
CREATE POLICY "Allow all operations on trending_topics" ON trending_topics FOR ALL USING (true);
CREATE POLICY "Allow all operations on content_templates" ON content_templates FOR ALL USING (true);
CREATE POLICY "Allow all operations on content_calendar" ON content_calendar FOR ALL USING (true);

-- Create triggers to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_content_ideas_updated_at BEFORE UPDATE ON content_ideas FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_templates_updated_at BEFORE UPDATE ON content_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_content_calendar_updated_at BEFORE UPDATE ON content_calendar FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Verify tables were created
SELECT 'Tables created successfully!' as status;
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('content_ideas', 'trending_topics', 'content_templates', 'content_calendar')
ORDER BY table_name;
