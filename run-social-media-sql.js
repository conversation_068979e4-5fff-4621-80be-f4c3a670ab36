// <PERSON><PERSON><PERSON> to run the social media tables SQL script
// This will create the missing tables that are causing 404 errors

import { readFileSync } from 'fs';
import { createClient } from '@supabase/supabase-js';

// Read config from the same source as the app
const config = {
  supabase: {
    url: process.env.VITE_SUPABASE_URL || 'https://rclikclltlyzyojjttqv.supabase.co',
    anonKey: process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQ5NjE1NzQsImV4cCI6MjA1MDUzNzU3NH0.Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8'
  }
};

const supabase = createClient(config.supabase.url, config.supabase.anonKey);

async function runSocialMediaSQL() {
  try {
    console.log('🚀 Starting social media tables creation...');
    
    // Read the SQL file
    const sqlContent = readFileSync('./database/create_social_media_tables.sql', 'utf8');
    
    // Split the SQL into individual statements
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--') && stmt !== 'COMMIT');
    
    console.log(`📝 Found ${statements.length} SQL statements to execute`);
    
    // Execute each statement
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      
      if (statement.includes('CREATE EXTENSION') || 
          statement.includes('CREATE TABLE') || 
          statement.includes('INSERT INTO') ||
          statement.includes('CREATE INDEX') ||
          statement.includes('DO $$')) {
        
        console.log(`⚡ Executing statement ${i + 1}/${statements.length}...`);
        
        try {
          // Use the RPC function to execute raw SQL
          const { data, error } = await supabase.rpc('exec_sql', { 
            sql_query: statement + ';' 
          });
          
          if (error) {
            console.log(`⚠️ Statement ${i + 1} failed (might be expected):`, error.message);
            // Continue with other statements even if one fails
          } else {
            console.log(`✅ Statement ${i + 1} executed successfully`);
          }
        } catch (err) {
          console.log(`⚠️ Statement ${i + 1} exception:`, err.message);
          // Continue with other statements
        }
      }
    }
    
    console.log('\n🧪 Testing the created tables...');
    
    // Test if tables were created successfully
    const tables = ['social_platforms', 'content_templates', 'social_posts'];
    
    for (const table of tables) {
      try {
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          console.log(`✅ ${table}: Table exists and accessible (${count || 0} rows)`);
        }
      } catch (err) {
        console.log(`💥 ${table}: ${err.message}`);
      }
    }
    
    console.log('\n🎉 Social media tables setup completed!');
    console.log('You can now refresh your application and the 404 errors should be resolved.');
    
  } catch (error) {
    console.error('💥 Failed to run SQL script:', error);
    console.log('\n📋 Manual steps:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the content from database/create_social_media_tables.sql');
    console.log('4. Run the SQL script manually');
  }
}

// Run the script
runSocialMediaSQL();
