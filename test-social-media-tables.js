// Test script to verify social media tables are working
// Copy and paste this into your browser console (F12) while on localhost:5173

const testSocialMediaTables = async () => {
  console.log('🔍 Testing social media table connections...');
  
  try {
    // Import Supabase client
    const { supabase } = await import('./src/lib/supabase.js');
    
    const tables = [
      'social_platforms',
      'content_templates', 
      'social_posts'
    ];
    
    const results = {};
    
    for (const table of tables) {
      try {
        console.log(`Testing ${table}...`);
        
        // Test basic connection with count query
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          results[table] = {
            status: 'ERROR',
            message: error.message,
            code: error.code
          };
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          results[table] = {
            status: 'SUCCESS',
            count: count || 0
          };
          console.log(`✅ ${table}: Connected successfully (${count || 0} rows)`);
        }
      } catch (err) {
        results[table] = {
          status: 'EXCEPTION',
          message: err.message
        };
        console.log(`💥 ${table}: Exception - ${err.message}`);
      }
    }
    
    console.log('\n📊 Summary:');
    console.table(results);
    
    // Test if we can read the default data
    console.log('\n🧪 Testing data operations...');
    
    try {
      // Test reading social platforms
      const { data: platforms, error: platformsError } = await supabase
        .from('social_platforms')
        .select('*')
        .order('name');
      
      if (platformsError) {
        console.log(`❌ Platforms read test failed: ${platformsError.message}`);
      } else {
        console.log(`✅ Platforms read test successful (${platforms?.length || 0} platforms)`);
        if (platforms && platforms.length > 0) {
          console.log('📋 Available platforms:', platforms.map(p => p.name).join(', '));
        }
      }
      
      // Test reading content templates
      const { data: templates, error: templatesError } = await supabase
        .from('content_templates')
        .select('*')
        .order('usage_count', { ascending: false });
      
      if (templatesError) {
        console.log(`❌ Templates read test failed: ${templatesError.message}`);
      } else {
        console.log(`✅ Templates read test successful (${templates?.length || 0} templates)`);
        if (templates && templates.length > 0) {
          console.log('📋 Available templates:', templates.map(t => t.name).join(', '));
        }
      }
      
      // Test reading social posts
      const { data: posts, error: postsError } = await supabase
        .from('social_posts')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5);
      
      if (postsError) {
        console.log(`❌ Posts read test failed: ${postsError.message}`);
      } else {
        console.log(`✅ Posts read test successful (${posts?.length || 0} posts)`);
      }
      
    } catch (err) {
      console.log(`💥 Data operations test failed: ${err.message}`);
    }
    
    return results;
    
  } catch (err) {
    console.error('💥 Failed to run social media test:', err);
    return { error: err.message };
  }
};

// Run the test
testSocialMediaTables().then(results => {
  console.log('\n🎉 Social media tables test completed!');
  if (Object.values(results).every(r => r.status === 'SUCCESS')) {
    console.log('🎊 All social media tables are working correctly!');
    console.log('🔄 You can now refresh your application and the 404 errors should be resolved.');
  } else {
    console.log('⚠️ Some issues found - check the results above');
    console.log('📋 Next steps:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the content from database/create_social_media_tables.sql');
    console.log('4. Run the SQL script manually');
  }
});
