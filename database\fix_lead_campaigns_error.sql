-- Fix for LeadGeneration component - Create only the missing lead_campaigns and lead_management tables
-- Run this in your Supabase SQL Editor to fix the 404 error

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Lead Campaigns Table
-- Stores lead generation campaigns with targeting criteria and performance metrics
CREATE TABLE IF NOT EXISTS lead_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Active', 'Paused', 'Completed', 'Draft')),
    leads INTEGER DEFAULT 0,
    qualified INTEGER DEFAULT 0,
    converted INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.0,
    target_criteria JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_leads_count CHECK (leads >= 0),
    CONSTRAINT valid_qualified_count CHECK (qualified >= 0 AND qualified <= leads),
    CONSTRAINT valid_converted_count CHECK (converted >= 0 AND converted <= qualified),
    CONSTRAINT valid_conversion_rate CHECK (conversion_rate >= 0.0 AND conversion_rate <= 100.0)
);

-- Lead Management Table
-- Stores leads for the LeadGeneration component (separate from scraped leads)
CREATE TABLE IF NOT EXISTS lead_management (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    company TEXT NOT NULL,
    title TEXT NOT NULL,
    location TEXT NOT NULL,
    score INTEGER DEFAULT 50,
    status TEXT NOT NULL DEFAULT 'Cold' CHECK (status IN ('Hot', 'Warm', 'Cold', 'Qualified', 'Converted', 'Lost')),
    source TEXT NOT NULL DEFAULT 'Manual',
    tags JSONB DEFAULT '[]',
    notes TEXT DEFAULT '',
    last_contact TIMESTAMP WITH TIME ZONE,
    next_follow_up TIMESTAMP WITH TIME ZONE,
    social_profiles JSONB DEFAULT '{}',
    company_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_email_format CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_score_range CHECK (score >= 0 AND score <= 100)
);

-- Enable Row Level Security
ALTER TABLE lead_campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE lead_management ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for demo (replace with proper user-based policies in production)
DO $$ 
BEGIN
    -- Handle lead_campaigns policies
    DROP POLICY IF EXISTS "Allow all operations on lead_campaigns" ON lead_campaigns;
    CREATE POLICY "Allow all operations on lead_campaigns" ON lead_campaigns FOR ALL USING (true);
    
    -- Handle lead_management policies  
    DROP POLICY IF EXISTS "Allow all operations on lead_management" ON lead_management;
    CREATE POLICY "Allow all operations on lead_management" ON lead_management FOR ALL USING (true);
EXCEPTION
    WHEN others THEN
        -- If policies don't exist, create them
        CREATE POLICY "Allow all operations on lead_campaigns" ON lead_campaigns FOR ALL USING (true);
        CREATE POLICY "Allow all operations on lead_management" ON lead_management FOR ALL USING (true);
END $$;

-- Insert sample data for lead_campaigns
INSERT INTO lead_campaigns (name, description, status, leads, qualified, converted, conversion_rate, target_criteria) VALUES
('SaaS Decision Makers', 'Target decision makers in SaaS companies', 'Active', 1250, 89, 23, 25.8, '{"industries": ["Technology", "SaaS"], "job_titles": ["CEO", "CTO", "VP"], "company_sizes": ["100-500", "500+"]}'),
('E-commerce Managers', 'Marketing managers in e-commerce', 'Paused', 890, 67, 18, 26.9, '{"industries": ["E-commerce", "Retail"], "job_titles": ["Marketing Manager", "Marketing Director"]}'),
('Enterprise Sales', 'Enterprise sales professionals', 'Draft', 0, 0, 0, 0.0, '{"industries": ["Enterprise Software"], "job_titles": ["Sales Director", "VP Sales"]}')
ON CONFLICT DO NOTHING;

-- Insert sample data for lead_management
INSERT INTO lead_management (name, email, phone, company, title, location, score, status, source, tags, notes, social_profiles, company_info) VALUES
('Sarah Johnson', '<EMAIL>', '+****************', 'TechCorp Inc.', 'Marketing Director', 'San Francisco, CA', 85, 'Hot', 'LinkedIn', '["Enterprise", "SaaS"]', 'Interested in our enterprise solution', '{"linkedin": "https://linkedin.com/in/sarahjohnson", "website": "https://techcorp.com"}', '{"industry": "Technology", "size": "500-1000", "revenue": "$50M-100M"}'),
('Michael Chen', '<EMAIL>', '+****************', 'DataFlow Solutions', 'VP of Sales', 'New York, NY', 72, 'Warm', 'Website', '["Mid-market", "Analytics"]', 'Downloaded whitepaper, needs follow-up', '{}', '{"industry": "Data Analytics", "size": "100-500", "revenue": "$10M-50M"}'),
('Emily Rodriguez', '<EMAIL>', '+****************', 'Innovate Co', 'CTO', 'Austin, TX', 91, 'Hot', 'Referral', '["Enterprise", "AI"]', 'Referred by existing customer', '{"linkedin": "https://linkedin.com/in/emilyrodriguez"}', '{"industry": "AI/ML", "size": "200-500", "revenue": "$25M-50M"}')
ON CONFLICT DO NOTHING;

-- Verify tables were created successfully
SELECT 'Tables created successfully!' as status;

-- Check if the tables exist
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('lead_campaigns', 'lead_management')
ORDER BY table_name;

-- Show sample data
SELECT 'Lead Campaigns:' as info;
SELECT name, status, leads, qualified, converted FROM lead_campaigns LIMIT 3;

SELECT 'Lead Management:' as info;
SELECT name, company, title, status, score FROM lead_management LIMIT 3;
