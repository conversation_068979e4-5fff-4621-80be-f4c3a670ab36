// Database types
export interface ScrapingJob {
  id: string;
  url: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  created_at: string;
  completed_at?: string;
  results?: Record<string, unknown>;
}

export interface ScrapedData {
  id: string;
  job_id: string;
  url: string;
  title?: string;
  content?: string;
  metadata?: {
    headings?: string[];
    links?: string[];
    wordCount?: number;
    [key: string]: unknown;
  };
  scraped_at: string;
  scraping_jobs: ScrapingJob;
}

// Analytics types
export interface AnalyticsStats {
  totalJobs: number;
  completedJobs: number;
  totalData: number;
  avgWordCount: number;
}

// Marketing tools types
export interface KeywordData {
  keyword: string;
  frequency: number;
  context: string;
}

export interface LeadData {
  email?: string;
  name?: string;
  company?: string;
  url: string;
  title: string;
}

// SEO Analysis types
export interface SEOAnalysisResult {
  score: number;
  issues: string[];
  recommendations: string[];
  details: {
    titleAnalysis: SEOCategoryAnalysis;
    contentAnalysis: ContentAnalysis;
    technicalSEO: SEOCategoryAnalysis;
    keywordAnalysis: KeywordAnalysis;
  };
}

export interface SEOCategoryAnalysis {
  score: number;
  issues: string[];
  recommendations: string[];
}

export interface ContentAnalysis extends SEOCategoryAnalysis {
  wordCount: number;
  readabilityScore: number;
}

export interface KeywordAnalysis extends SEOCategoryAnalysis {
  topKeywords: Array<{
    keyword: string;
    frequency: number;
    density: number;
  }>;
}

export interface ScrapedDataForSEO {
  title?: string;
  content?: string;
  url: string;
  metadata?: {
    headings?: string[];
    links?: string[];
    wordCount?: number;
  };
}

// UI Component types
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

export interface FilterState {
  searchTerm: string;
  statusFilter: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

// API Response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface SupabaseResponse<T> {
  data: T | null;
  error: {
    message: string;
    details?: string;
    hint?: string;
  } | null;
}

// Configuration types
export interface AppConfig {
  supabase: {
    url: string;
    anonKey: string;
    functionsUrl: string;
  };
  app: {
    name: string;
    version: string;
  };
  dev: {
    mode: boolean;
    logLevel: string;
  };
}

// Error types
export interface AppError {
  message: string;
  code: string;
  statusCode: number;
  userMessage?: string;
}

// Form types
export interface ScrapingFormData {
  urls: string[];
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

// Event handler types
export type AsyncEventHandler = () => Promise<void>;
export type EventHandler = () => void;
export type ChangeHandler<T> = (value: T) => void;

// Utility types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Component prop types
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

export interface LoadingProps {
  loading: boolean;
  error?: string | null;
}

export interface DataTableProps extends LoadingProps {
  data: ScrapedData[];
  onRefresh: AsyncEventHandler;
}

export interface MarketingToolsProps extends LoadingProps {
  onExtractKeywords: AsyncEventHandler;
  onExtractLeads: AsyncEventHandler;
  onGenerateContent: AsyncEventHandler;
  onGenerateSocialPosts: AsyncEventHandler;
}

export interface AnalyticsDashboardProps extends LoadingProps {
  stats: AnalyticsStats;
  onRefreshStats: AsyncEventHandler;
  onRunSEOAnalysis: AsyncEventHandler;
}

// Status badge variants
export type StatusVariant = 'default' | 'secondary' | 'destructive' | 'outline';

// Sort configuration
export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

// Pagination
export interface PaginationConfig {
  page: number;
  pageSize: number;
  total: number;
}

// Theme types
export type Theme = 'light' | 'dark' | 'system';

// Toast types
export interface ToastConfig {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

// Environment variables
export interface EnvironmentVariables {
  VITE_SUPABASE_URL: string;
  VITE_SUPABASE_ANON_KEY: string;
  VITE_SUPABASE_FUNCTIONS_URL: string;
  VITE_APP_NAME?: string;
  VITE_APP_VERSION?: string;
  VITE_DEV_MODE?: string;
  VITE_LOG_LEVEL?: string;
}

// Export all types as a namespace for easier imports
export namespace Types {
  export type ScrapingJob = ScrapingJob;
  export type ScrapedData = ScrapedData;
  export type AnalyticsStats = AnalyticsStats;
  export type SEOAnalysisResult = SEOAnalysisResult;
  export type KeywordData = KeywordData;
  export type LeadData = LeadData;
  export type AppConfig = AppConfig;
  export type AppError = AppError;
  export type LoadingState = LoadingState;
  export type FilterState = FilterState;
}
