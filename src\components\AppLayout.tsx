import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useIsMobile } from '@/hooks/use-mobile';
import Header from './Header';
import Sidebar, { MenuItemKey } from './Sidebar';
import ScraperForm from './ScraperForm';
import DataTable from './DataTable';
import AnalyticsDashboard from './AnalyticsDashboard';
import MarketingTools from './MarketingTools';
import SEOTools from './SEOTools';
import KeywordResearch from './KeywordResearch';
import EmailMarketing from './EmailMarketing';
import SocialMedia from './SocialMedia';
import LeadGeneration from './LeadGeneration';
import ContentIdeas from './ContentIdeas';
import DataExport from './DataExport';
import Settings from './Settings';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

const AppLayout: React.FC = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const location = useLocation();
  const navigate = useNavigate();
  const isMobile = useIsMobile();

  // Determine active menu item from URL
  const getMenuItemFromPath = (pathname: string): MenuItemKey => {
    const path = pathname.slice(1); // Remove leading slash
    if (path === '') return 'web-scraper'; // Default to web-scraper for root path
    return path as MenuItemKey;
  };

  const [activeMenuItem, setActiveMenuItem] = useState<MenuItemKey>(
    getMenuItemFromPath(location.pathname)
  );

  // Update active menu item when URL changes
  useEffect(() => {
    const menuItem = getMenuItemFromPath(location.pathname);
    setActiveMenuItem(menuItem);
  }, [location.pathname]);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen);

  const handleMenuItemClick = (menuItem: MenuItemKey) => {
    setActiveMenuItem(menuItem);
    // Update URL without page reload
    const path = menuItem === 'web-scraper' ? '/' : `/${menuItem}`;
    navigate(path);
  };

  const renderMainContent = () => {
    switch (activeMenuItem) {
      case 'web-scraper':
        return (
          <Tabs defaultValue="scraper" className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 bg-white shadow-lg">
              <TabsTrigger value="scraper" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-blue-500 data-[state=active]:text-white">
                Web Scraper
              </TabsTrigger>
              <TabsTrigger value="analytics" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-blue-500 data-[state=active]:text-white">
                Analytics
              </TabsTrigger>
              <TabsTrigger value="data" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-blue-500 data-[state=active]:text-white">
                Data Results
              </TabsTrigger>
              <TabsTrigger value="tools" className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-500 data-[state=active]:to-blue-500 data-[state=active]:text-white">
                Marketing Tools
              </TabsTrigger>
            </TabsList>

            <TabsContent value="scraper" className="space-y-6">
              <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
                <div className="xl:col-span-1">
                  <ScraperForm />
                </div>
                <div className="xl:col-span-2">
                  <AnalyticsDashboard />
                </div>
              </div>
            </TabsContent>

            <TabsContent value="analytics">
              <AnalyticsDashboard />
            </TabsContent>

            <TabsContent value="data">
              <DataTable />
            </TabsContent>

            <TabsContent value="tools">
              <MarketingTools />
            </TabsContent>
          </Tabs>
        );
      case 'seo-tools':
        return <SEOTools />;
      case 'analytics':
        return <AnalyticsDashboard />;
      case 'keyword-research':
        return <KeywordResearch />;
      case 'email-marketing':
        return <EmailMarketing />;
      case 'social-media':
        return <SocialMedia />;
      case 'lead-generation':
        return <LeadGeneration />;
      case 'content-ideas':
        return <ContentIdeas />;
      case 'data-export':
        return <DataExport />;
      case 'settings':
        return <Settings />;
      default:
        return <AnalyticsDashboard />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      <Header onMenuClick={toggleSidebar} />

      <div className="flex">
        <Sidebar
          isOpen={sidebarOpen}
          isMobile={isMobile}
          activeMenuItem={activeMenuItem}
          onMenuItemClick={handleMenuItemClick}
        />

        <main className="flex-1 p-6 space-y-6 overflow-auto">
          <div className="max-w-7xl mx-auto">
            <div className="mb-8">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent mb-2">
                MarketCrawler Pro - Web Intelligence Suite
              </h2>
              <p className="text-gray-600">
                Transform web data into powerful marketing insights and automated campaigns
              </p>
            </div>

            {renderMainContent()}
          </div>
        </main>
      </div>

      {isMobile && sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
};

export default AppLayout;