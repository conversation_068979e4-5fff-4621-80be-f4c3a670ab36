-- CORRECTED 406 Error Fix Script
-- This script matches your existing table structure with first_name/last_name columns
-- Run this in your Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 1: Drop existing problematic policies if they exist
DROP POLICY IF EXISTS "Users can manage own settings" ON user_settings;
DROP POLICY IF EXISTS "Users can manage own notifications" ON notification_settings;
DROP POLICY IF EXISTS "Users can manage own privacy" ON privacy_settings;
DROP POLICY IF EXISTS "Users can manage own security" ON security_settings;

-- Step 2: Disable RLS temporarily to fix structure
ALTER TABLE IF EXISTS user_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS notification_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS privacy_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS security_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS user_profiles DISABLE ROW LEVEL SECURITY;

-- Step 3: Check and create user_profiles table with correct structure
-- This matches your existing table with first_name/last_name columns
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    company TEXT,
    bio TEXT,
    avatar_url TEXT,
    phone TEXT,
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    language TEXT NOT NULL DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 4: Create user_settings table with proper structure
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    theme TEXT NOT NULL DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    language TEXT NOT NULL DEFAULT 'en',
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    notifications JSONB NOT NULL DEFAULT '{
        "email": true,
        "push": false,
        "marketing": true
    }',
    privacy JSONB NOT NULL DEFAULT '{
        "analytics": true,
        "data_collection": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one settings record per user
    UNIQUE(user_id)
);

-- Step 5: Create additional settings tables
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    email BOOLEAN NOT NULL DEFAULT true,
    push BOOLEAN NOT NULL DEFAULT false,
    sms BOOLEAN NOT NULL DEFAULT false,
    marketing BOOLEAN NOT NULL DEFAULT true,
    security_alerts BOOLEAN NOT NULL DEFAULT true,
    export_complete BOOLEAN NOT NULL DEFAULT true,
    weekly_reports BOOLEAN NOT NULL DEFAULT false,
    system_updates BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

CREATE TABLE IF NOT EXISTS privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    dataCollection BOOLEAN NOT NULL DEFAULT true,
    analytics BOOLEAN NOT NULL DEFAULT true,
    thirdParty BOOLEAN NOT NULL DEFAULT false,
    cookieConsent BOOLEAN NOT NULL DEFAULT true,
    dataRetention INTEGER NOT NULL DEFAULT 365,
    shareUsageData BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

CREATE TABLE IF NOT EXISTS security_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    password_last_changed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    login_notifications BOOLEAN NOT NULL DEFAULT true,
    session_timeout INTEGER NOT NULL DEFAULT 60,
    ip_whitelist JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Step 6: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user_id ON privacy_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_security_settings_user_id ON security_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);

-- Step 7: Create a function to automatically create user profile and settings
-- This function handles the first_name/last_name structure correctly
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
DECLARE
    user_name TEXT;
    first_name_val TEXT;
    last_name_val TEXT;
BEGIN
    -- Extract name from metadata or email
    user_name := COALESCE(NEW.raw_user_meta_data->>'name', NEW.email);
    
    -- Split name into first and last name
    IF user_name LIKE '% %' THEN
        first_name_val := split_part(user_name, ' ', 1);
        last_name_val := split_part(user_name, ' ', 2);
    ELSE
        first_name_val := user_name;
        last_name_val := 'User';
    END IF;
    
    -- Insert user profile with correct structure (only if not exists)
    IF NOT EXISTS (SELECT 1 FROM public.user_profiles WHERE id = NEW.id) THEN
        INSERT INTO public.user_profiles (id, first_name, last_name, email)
        VALUES (
            NEW.id,
            first_name_val,
            last_name_val,
            NEW.email
        );
    END IF;

    -- Insert default settings (only if not exists)
    IF NOT EXISTS (SELECT 1 FROM public.user_settings WHERE user_id = NEW.id) THEN
        INSERT INTO public.user_settings (user_id)
        VALUES (NEW.id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM public.notification_settings WHERE user_id = NEW.id) THEN
        INSERT INTO public.notification_settings (user_id)
        VALUES (NEW.id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM public.privacy_settings WHERE user_id = NEW.id) THEN
        INSERT INTO public.privacy_settings (user_id)
        VALUES (NEW.id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM public.security_settings WHERE user_id = NEW.id) THEN
        INSERT INTO public.security_settings (user_id)
        VALUES (NEW.id);
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Step 8: Create trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Step 9: Enable RLS with permissive policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_settings ENABLE ROW LEVEL SECURITY;

-- Step 10: Create RLS policies
-- User profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles
    FOR SELECT USING (auth.uid()::text = id::text);
CREATE POLICY "Users can update own profile" ON user_profiles
    FOR UPDATE USING (auth.uid()::text = id::text);
CREATE POLICY "Users can insert own profile" ON user_profiles
    FOR INSERT WITH CHECK (auth.uid()::text = id::text);

-- User settings policies
CREATE POLICY "Users can manage own settings" ON user_settings
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Notification settings policies
CREATE POLICY "Users can manage own notifications" ON notification_settings
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Privacy settings policies
CREATE POLICY "Users can manage own privacy" ON privacy_settings
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Security settings policies
CREATE POLICY "Users can manage own security" ON security_settings
    FOR ALL USING (auth.uid()::text = user_id::text);

-- Step 11: Insert default settings for existing users with correct name handling
-- Handle existing users by creating profiles with proper first_name/last_name
-- Use WHERE NOT EXISTS to avoid conflicts instead of ON CONFLICT
INSERT INTO user_profiles (id, first_name, last_name, email)
SELECT
    au.id,
    CASE
        WHEN COALESCE(au.raw_user_meta_data->>'name', au.email) LIKE '% %' THEN
            split_part(COALESCE(au.raw_user_meta_data->>'name', au.email), ' ', 1)
        ELSE
            COALESCE(au.raw_user_meta_data->>'name', split_part(au.email, '@', 1))
    END as first_name,
    CASE
        WHEN COALESCE(au.raw_user_meta_data->>'name', au.email) LIKE '% %' THEN
            split_part(COALESCE(au.raw_user_meta_data->>'name', au.email), ' ', 2)
        ELSE
            'User'
    END as last_name,
    au.email
FROM auth.users au
WHERE NOT EXISTS (
    SELECT 1 FROM user_profiles up WHERE up.id = au.id
);

-- Insert default settings for all users using WHERE NOT EXISTS
INSERT INTO user_settings (user_id)
SELECT up.id
FROM user_profiles up
WHERE NOT EXISTS (
    SELECT 1 FROM user_settings us WHERE us.user_id = up.id
);

INSERT INTO notification_settings (user_id)
SELECT up.id
FROM user_profiles up
WHERE NOT EXISTS (
    SELECT 1 FROM notification_settings ns WHERE ns.user_id = up.id
);

INSERT INTO privacy_settings (user_id)
SELECT up.id
FROM user_profiles up
WHERE NOT EXISTS (
    SELECT 1 FROM privacy_settings ps WHERE ps.user_id = up.id
);

INSERT INTO security_settings (user_id)
SELECT up.id
FROM user_profiles up
WHERE NOT EXISTS (
    SELECT 1 FROM security_settings ss WHERE ss.user_id = up.id
);

-- Step 12: Verification queries
SELECT 
    'user_profiles' as table_name, 
    COUNT(*) as row_count 
FROM user_profiles
UNION ALL
SELECT 
    'user_settings' as table_name, 
    COUNT(*) as row_count 
FROM user_settings
UNION ALL
SELECT 
    'notification_settings' as table_name, 
    COUNT(*) as row_count 
FROM notification_settings
UNION ALL
SELECT 
    'privacy_settings' as table_name, 
    COUNT(*) as row_count 
FROM privacy_settings
UNION ALL
SELECT 
    'security_settings' as table_name, 
    COUNT(*) as row_count 
FROM security_settings;

-- Success message
DO $$
BEGIN
    RAISE NOTICE '✅ CORRECTED Database fix completed successfully!';
    RAISE NOTICE 'All tables created with proper structure matching your schema.';
    RAISE NOTICE 'Default settings inserted for existing users with proper names.';
    RAISE NOTICE 'The 406 errors should now be resolved.';
END $$;
