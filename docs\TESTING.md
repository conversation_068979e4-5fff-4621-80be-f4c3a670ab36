# Testing Guide

This document outlines the testing strategy and setup for the Scraping Analysis Marketing application.

## Testing Stack

- **Unit & Integration Tests**: [Vitest](https://vitest.dev/) + [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- **E2E Tests**: [Playwright](https://playwright.dev/)
- **Mocking**: [<PERSON><PERSON> (Mock Service Worker)](https://mswjs.io/)
- **Coverage**: Vitest Coverage (v8)

## Test Structure

```
src/
├── test/
│   ├── setup.ts           # Test setup and global mocks
│   ├── utils.tsx          # Test utilities and custom render
│   └── mocks/
│       └── server.ts      # MSW server setup
├── components/
│   └── __tests__/         # Component unit tests
├── lib/
│   └── __tests__/         # Library/utility unit tests
└── hooks/
    └── __tests__/         # Custom hooks tests

e2e/
└── *.spec.ts             # End-to-end tests
```

## Running Tests

### Unit & Integration Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with UI
npm run test:ui

# Run tests with coverage
npm run test:coverage
```

### E2E Tests

```bash
# Run E2E tests
npm run test:e2e

# Run E2E tests with UI
npm run test:e2e:ui

# Run E2E tests in headed mode
npx playwright test --headed
```

## Test Configuration

### Vitest Configuration

The `vitest.config.ts` file configures:
- **Environment**: jsdom for DOM testing
- **Setup Files**: Global test setup
- **Coverage**: v8 provider with thresholds
- **Path Aliases**: Same as the main app

### Coverage Thresholds

- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%
- **Statements**: 70%

### Playwright Configuration

The `playwright.config.ts` file configures:
- **Browsers**: Chrome, Firefox, Safari, Mobile
- **Base URL**: http://localhost:5173
- **Reporters**: HTML, JSON, JUnit
- **Screenshots**: On failure
- **Videos**: On failure

## Writing Tests

### Unit Tests

```typescript
import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@/test/utils'
import MyComponent from '../MyComponent'

describe('MyComponent', () => {
  it('renders correctly', () => {
    render(<MyComponent />)
    expect(screen.getByText('Expected Text')).toBeInTheDocument()
  })

  it('handles user interactions', async () => {
    const user = userEvent.setup()
    render(<MyComponent />)
    
    await user.click(screen.getByRole('button'))
    expect(screen.getByText('Updated Text')).toBeInTheDocument()
  })
})
```

### Integration Tests

```typescript
import { describe, it, expect } from 'vitest'
import { render, screen, waitFor } from '@/test/utils'
import { server } from '@/test/mocks/server'
import { http, HttpResponse } from 'msw'

describe('Component Integration', () => {
  it('fetches and displays data', async () => {
    // Mock API response
    server.use(
      http.get('/api/data', () => {
        return HttpResponse.json({ data: 'test data' })
      })
    )

    render(<ComponentWithAPI />)
    
    await waitFor(() => {
      expect(screen.getByText('test data')).toBeInTheDocument()
    })
  })
})
```

### E2E Tests

```typescript
import { test, expect } from '@playwright/test'

test('user can complete full workflow', async ({ page }) => {
  await page.goto('/')
  
  // Fill form
  await page.fill('[data-testid="url-input"]', 'https://example.com')
  await page.click('[data-testid="submit-button"]')
  
  // Verify results
  await expect(page.getByText('Success')).toBeVisible()
})
```

## Test Utilities

### Custom Render

The `src/test/utils.tsx` provides a custom render function that wraps components with necessary providers:

```typescript
import { render } from '@/test/utils'

// This automatically wraps with:
// - QueryClientProvider
// - ThemeProvider
// - TooltipProvider
// - BrowserRouter
// - AppProvider
```

### Mock Data Factories

Use the provided factory functions for consistent test data:

```typescript
import { 
  createMockScrapingJob,
  createMockScrapedData,
  createMockSEOAnalysis 
} from '@/test/utils'

const mockJob = createMockScrapingJob({ status: 'completed' })
const mockData = createMockScrapedData({ title: 'Custom Title' })
```

## Mocking Strategy

### API Mocking with MSW

MSW intercepts network requests at the network level:

```typescript
// In test file
import { server } from '@/test/mocks/server'
import { http, HttpResponse } from 'msw'

server.use(
  http.get('/api/endpoint', () => {
    return HttpResponse.json({ success: true })
  })
)
```

### Component Mocking

Mock heavy components or external dependencies:

```typescript
vi.mock('@/components/HeavyComponent', () => ({
  default: () => <div data-testid="mocked-component">Mocked</div>
}))
```

### Supabase Mocking

Supabase client is mocked globally in `setup.ts`:

```typescript
// Already configured in setup.ts
// Returns mock responses for all Supabase operations
```

## Testing Best Practices

### 1. Test Behavior, Not Implementation

```typescript
// ❌ Testing implementation details
expect(component.state.isLoading).toBe(true)

// ✅ Testing user-visible behavior
expect(screen.getByText('Loading...')).toBeInTheDocument()
```

### 2. Use Semantic Queries

```typescript
// ✅ Prefer semantic queries
screen.getByRole('button', { name: 'Submit' })
screen.getByLabelText('Email address')
screen.getByText('Welcome message')

// ❌ Avoid implementation-specific queries
screen.getByClassName('submit-btn')
screen.getById('email-input')
```

### 3. Test User Interactions

```typescript
import userEvent from '@testing-library/user-event'

const user = userEvent.setup()
await user.click(screen.getByRole('button'))
await user.type(screen.getByLabelText('Email'), '<EMAIL>')
```

### 4. Handle Async Operations

```typescript
// Wait for elements to appear
await waitFor(() => {
  expect(screen.getByText('Data loaded')).toBeInTheDocument()
})

// Wait for elements to disappear
await waitForElementToBeRemoved(screen.getByText('Loading...'))
```

### 5. Clean Up After Tests

```typescript
afterEach(() => {
  cleanup() // Automatically done in setup.ts
  vi.clearAllMocks()
  server.resetHandlers()
})
```

## Debugging Tests

### Debug Failing Tests

```typescript
// Add debug output
import { screen } from '@testing-library/react'
screen.debug() // Prints current DOM

// Or debug specific element
screen.debug(screen.getByRole('button'))
```

### Playwright Debugging

```bash
# Run with debug mode
npx playwright test --debug

# Run with headed browser
npx playwright test --headed

# Generate test code
npx playwright codegen localhost:5173
```

## CI/CD Integration

### GitHub Actions Example

```yaml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:coverage
      
      - name: Install Playwright
        run: npx playwright install
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v3
```

## Test Data Management

### Environment Variables

Tests use mock environment variables defined in `setup.ts`:

```typescript
// Test environment is isolated from real Supabase
VITE_SUPABASE_URL=https://test.supabase.co
VITE_SUPABASE_ANON_KEY=test-anon-key
```

### Database Mocking

Database operations are mocked to return predictable data:

```typescript
// All Supabase operations return mock data
// No real database connections in tests
```

## Performance Testing

### Bundle Size Testing

```bash
# Analyze bundle size
npm run build
npx vite-bundle-analyzer dist
```

### Load Testing

Consider adding load tests for critical paths:

```typescript
// Example with Playwright
test('handles multiple concurrent users', async ({ browser }) => {
  const contexts = await Promise.all([
    browser.newContext(),
    browser.newContext(),
    browser.newContext(),
  ])
  
  // Test concurrent operations
})
```

## Troubleshooting

### Common Issues

1. **Tests timeout**: Increase timeout or check for unresolved promises
2. **Mock not working**: Verify mock is set up before component render
3. **DOM not updating**: Use `waitFor` for async updates
4. **Playwright flaky**: Add proper waits and stable selectors

### Getting Help

- Check test output and error messages
- Use `screen.debug()` to inspect DOM
- Review MSW network tab for API calls
- Use Playwright trace viewer for E2E debugging

## Next Steps

1. **Add more test coverage** for edge cases
2. **Implement visual regression testing** with Playwright
3. **Add performance benchmarks** for critical operations
4. **Set up mutation testing** to verify test quality
5. **Add accessibility testing** with axe-core
