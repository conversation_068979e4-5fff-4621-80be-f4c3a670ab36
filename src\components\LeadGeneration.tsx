import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  Users,
  Search,
  Target,
  Mail,
  Phone,
  Building,
  MapPin,
  Download,
  Filter,
  Plus,
  Eye,
  TrendingUp,
  Edit,
  Trash2,
  Copy,
  RefreshCw,
  Star,
  Calendar,
  Clock,
  CheckCircle,
  AlertCircle,
  Send,
  UserPlus,
  BarChart3,
  Settings,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';

interface Lead {
  id: string;
  name: string;
  email: string;
  phone?: string;
  company: string;
  title: string;
  location: string;
  score: number;
  status: 'Hot' | 'Warm' | 'Cold' | 'Qualified' | 'Converted' | 'Lost';
  source: string;
  tags: string[];
  notes: string;
  last_contact?: string;
  next_follow_up?: string;
  created_at: string;
  updated_at: string;
  social_profiles?: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  company_info?: {
    industry?: string;
    size?: string;
    revenue?: string;
    website?: string;
  };
}

interface LeadCampaign {
  id: string;
  name: string;
  description: string;
  status: 'Active' | 'Paused' | 'Completed' | 'Draft';
  leads: number;
  qualified: number;
  converted: number;
  conversion_rate: number;
  created_at: string;
  target_criteria: {
    industries?: string[];
    job_titles?: string[];
    company_sizes?: string[];
    locations?: string[];
  };
}

interface LeadSearchCriteria {
  company?: string;
  title?: string;
  location?: string;
  industry?: string;
  keywords?: string;
  company_size?: string;
  revenue_range?: string;
}

const LeadGeneration: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [leads, setLeads] = useState<Lead[]>([]);
  const [filteredLeads, setFilteredLeads] = useState<Lead[]>([]);
  const [campaigns, setCampaigns] = useState<LeadCampaign[]>([]);
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [searchCriteria, setSearchCriteria] = useState<LeadSearchCriteria>({});
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<string>('score');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [statusFilter, setStatusFilter] = useState<string[]>([]);
  const [scoreRange, setScoreRange] = useState({ min: 0, max: 100 });
  const [showAddLead, setShowAddLead] = useState(false);
  const [newLead, setNewLead] = useState<Partial<Lead>>({});
  const [showAddCampaign, setShowAddCampaign] = useState(false);
  const [newCampaign, setNewCampaign] = useState<Partial<LeadCampaign>>({});

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Load data on component mount
  useEffect(() => {
    loadLeads();
    loadCampaigns();
  }, []);

  // Apply filters and sorting when leads or filters change
  useEffect(() => {
    applyFiltersAndSort();
  }, [leads, statusFilter, scoreRange, sortBy, sortOrder]);

  const loadLeads = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('lead_management')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setLeads(data);
      } else {
        // Set mock data if no leads exist
        const mockLeads: Lead[] = [
          {
            id: '1',
            name: 'Sarah Johnson',
            company: 'TechCorp Inc.',
            title: 'Marketing Director',
            email: '<EMAIL>',
            phone: '+****************',
            location: 'San Francisco, CA',
            score: 85,
            status: 'Hot',
            source: 'LinkedIn',
            tags: ['Enterprise', 'SaaS'],
            notes: 'Interested in our enterprise solution',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            social_profiles: {
              linkedin: 'https://linkedin.com/in/sarahjohnson',
              website: 'https://techcorp.com'
            },
            company_info: {
              industry: 'Technology',
              size: '500-1000',
              revenue: '$50M-100M'
            }
          },
          {
            id: '2',
            name: 'Michael Chen',
            company: 'DataFlow Solutions',
            title: 'VP of Sales',
            email: '<EMAIL>',
            phone: '+****************',
            location: 'New York, NY',
            score: 72,
            status: 'Warm',
            source: 'Website',
            tags: ['Mid-market', 'Analytics'],
            notes: 'Downloaded whitepaper, needs follow-up',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date().toISOString(),
            company_info: {
              industry: 'Data Analytics',
              size: '100-500',
              revenue: '$10M-50M'
            }
          }
        ];
        setLeads(mockLeads);
      }
    } catch (error) {
      console.error('Failed to load leads:', error);
      showError(error, 'Load leads');
    }
  }, [showError]);

  const loadCampaigns = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('lead_campaigns')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setCampaigns(data);
      } else {
        // Set mock data if no campaigns exist
        const mockCampaigns: LeadCampaign[] = [
          {
            id: '1',
            name: 'SaaS Decision Makers',
            description: 'Target decision makers in SaaS companies',
            status: 'Active',
            leads: 1250,
            qualified: 89,
            converted: 23,
            conversion_rate: 25.8,
            created_at: new Date().toISOString(),
            target_criteria: {
              industries: ['Technology', 'SaaS'],
              job_titles: ['CEO', 'CTO', 'VP'],
              company_sizes: ['100-500', '500+']
            }
          },
          {
            id: '2',
            name: 'E-commerce Managers',
            description: 'Marketing managers in e-commerce',
            status: 'Paused',
            leads: 890,
            qualified: 67,
            converted: 18,
            conversion_rate: 26.9,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            target_criteria: {
              industries: ['E-commerce', 'Retail'],
              job_titles: ['Marketing Manager', 'Marketing Director']
            }
          }
        ];
        setCampaigns(mockCampaigns);
      }
    } catch (error) {
      console.error('Failed to load campaigns:', error);
      showError(error, 'Load campaigns');
    }
  }, [showError]);

  const applyFiltersAndSort = useCallback(() => {
    let filtered = [...leads];

    // Apply status filter
    if (statusFilter.length > 0) {
      filtered = filtered.filter(lead => statusFilter.includes(lead.status));
    }

    // Apply score range filter
    filtered = filtered.filter(lead =>
      lead.score >= scoreRange.min && lead.score <= scoreRange.max
    );

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'score':
          aValue = a.score;
          bValue = b.score;
          break;
        case 'name':
          aValue = a.name.toLowerCase();
          bValue = b.name.toLowerCase();
          break;
        case 'company':
          aValue = a.company.toLowerCase();
          bValue = b.company.toLowerCase();
          break;
        case 'created_at':
          aValue = new Date(a.created_at);
          bValue = new Date(b.created_at);
          break;
        default:
          aValue = a.score;
          bValue = b.score;
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredLeads(filtered);
  }, [leads, statusFilter, scoreRange, sortBy, sortOrder]);

  const handleSearch = useCallback(async () => {
    if (!searchCriteria.company && !searchCriteria.title && !searchCriteria.keywords) {
      showError(new Error('Please enter at least one search criteria'), 'Lead search');
      return;
    }

    setIsSearching(true);
    try {
      // Simulate API call for lead search
      await new Promise(resolve => setTimeout(resolve, 3000));

      const mockSearchResults: Lead[] = [
        {
          id: `search-${Date.now()}-1`,
          name: 'Alex Thompson',
          company: searchCriteria.company || 'TechStart Inc.',
          title: searchCriteria.title || 'Product Manager',
          email: '<EMAIL>',
          location: searchCriteria.location || 'Austin, TX',
          score: Math.floor(Math.random() * 40) + 60,
          status: 'Cold',
          source: 'Search',
          tags: ['New Lead'],
          notes: 'Found through lead search',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ];

      setLeads(prev => [...mockSearchResults, ...prev]);
      showSuccess(`Found ${mockSearchResults.length} new leads`);
    } catch (error) {
      showError(error, 'Lead search');
    } finally {
      setIsSearching(false);
    }
  }, [searchCriteria, showError, showSuccess]);

  const addLead = useCallback(async () => {
    if (!newLead.name || !newLead.email || !newLead.company) {
      showError(new Error('Please fill in required fields'), 'Add lead');
      return;
    }

    try {
      const leadToAdd: Lead = {
        id: `lead-${Date.now()}`,
        name: newLead.name,
        email: newLead.email,
        phone: newLead.phone || '',
        company: newLead.company,
        title: newLead.title || '',
        location: newLead.location || '',
        score: newLead.score || 50,
        status: newLead.status || 'Cold',
        source: 'Manual',
        tags: [],
        notes: newLead.notes || '',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('lead_management')
        .insert([leadToAdd])
        .select()
        .single();

      if (error) throw error;

      setLeads(prev => [data, ...prev]);
      setNewLead({});
      setShowAddLead(false);
      showSuccess('Lead added successfully');
    } catch (error) {
      showError(error, 'Add lead');
    }
  }, [newLead, showError, showSuccess]);

  const addCampaign = useCallback(async () => {
    if (!newCampaign.name?.trim() || !newCampaign.description?.trim()) {
      showError(new Error('Please fill in campaign name and description'), 'Add campaign');
      return;
    }

    try {
      const campaignData: Partial<LeadCampaign> = {
        name: newCampaign.name.trim(),
        description: newCampaign.description.trim(),
        status: 'Draft',
        leads: 0,
        qualified: 0,
        converted: 0,
        conversion_rate: 0,
        target_criteria: newCampaign.target_criteria || {},
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('lead_campaigns')
        .insert([campaignData])
        .select()
        .single();

      if (error) throw error;

      setCampaigns(prev => [data, ...prev]);
      setNewCampaign({});
      setShowAddCampaign(false);
      showSuccess('Campaign created successfully');
    } catch (error) {
      showError(error, 'Add campaign');
    }
  }, [newCampaign, showError, showSuccess]);

  const updateLeadStatus = useCallback(async (leadId: string, newStatus: Lead['status']) => {
    try {
      const { error } = await supabase
        .from('leads')
        .update({ status: newStatus, updated_at: new Date().toISOString() })
        .eq('id', leadId);

      if (error) throw error;

      setLeads(prev => prev.map(lead =>
        lead.id === leadId
          ? { ...lead, status: newStatus, updated_at: new Date().toISOString() }
          : lead
      ));

      showSuccess(`Lead status updated to ${newStatus}`);
    } catch (error) {
      showError(error, 'Update lead status');
    }
  }, [showError, showSuccess]);

  const deleteLead = useCallback(async (leadId: string) => {
    try {
      const { error } = await supabase
        .from('leads')
        .delete()
        .eq('id', leadId);

      if (error) throw error;

      setLeads(prev => prev.filter(lead => lead.id !== leadId));
      showSuccess('Lead deleted successfully');
    } catch (error) {
      showError(error, 'Delete lead');
    }
  }, [showError, showSuccess]);

  const exportLeads = useCallback(async (leadsToExport: Lead[]) => {
    try {
      const csvContent = [
        ['Name', 'Email', 'Phone', 'Company', 'Title', 'Location', 'Score', 'Status', 'Source'].join(','),
        ...leadsToExport.map(lead => [
          lead.name,
          lead.email,
          lead.phone || '',
          lead.company,
          lead.title,
          lead.location,
          lead.score,
          lead.status,
          lead.source
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `leads-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      showSuccess(`Exported ${leadsToExport.length} leads`);
    } catch (error) {
      showError(error, 'Export leads');
    }
  }, [showError, showSuccess]);

  const calculateLeadScore = useCallback((lead: Partial<Lead>): number => {
    let score = 50; // Base score

    // Company size factor
    if (lead.company_info?.size) {
      if (lead.company_info.size.includes('500+')) score += 20;
      else if (lead.company_info.size.includes('100-500')) score += 15;
      else if (lead.company_info.size.includes('50-100')) score += 10;
    }

    // Title factor
    if (lead.title) {
      const seniorTitles = ['CEO', 'CTO', 'VP', 'Director', 'Manager'];
      if (seniorTitles.some(title => lead.title!.includes(title))) {
        score += 15;
      }
    }

    // Industry factor
    if (lead.company_info?.industry) {
      const targetIndustries = ['Technology', 'SaaS', 'Software'];
      if (targetIndustries.includes(lead.company_info.industry)) {
        score += 10;
      }
    }

    return Math.min(Math.max(score, 0), 100);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hot': return 'bg-red-100 text-red-800';
      case 'Warm': return 'bg-yellow-100 text-yellow-800';
      case 'Cold': return 'bg-blue-100 text-blue-800';
      case 'Qualified': return 'bg-green-100 text-green-800';
      case 'Converted': return 'bg-purple-100 text-purple-800';
      case 'Lost': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Hot': return <TrendingUp className="h-4 w-4" />;
      case 'Warm': return <ArrowUp className="h-4 w-4" />;
      case 'Cold': return <Minus className="h-4 w-4" />;
      case 'Qualified': return <CheckCircle className="h-4 w-4" />;
      case 'Converted': return <Star className="h-4 w-4" />;
      case 'Lost': return <ArrowDown className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Users className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">Lead Generation</h1>
      </div>

      <Tabs defaultValue="leads" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="leads">Leads</TabsTrigger>
          <TabsTrigger value="search">Lead Search</TabsTrigger>
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="leads" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Lead Database ({filteredLeads.length})</h2>
            <div className="flex space-x-2">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="score">Score</SelectItem>
                  <SelectItem value="name">Name</SelectItem>
                  <SelectItem value="company">Company</SelectItem>
                  <SelectItem value="created_at">Date Added</SelectItem>
                </SelectContent>
              </Select>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              >
                {sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowFilters(!showFilters)}
              >
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button
                variant="outline"
                onClick={() => exportLeads(selectedLeads.length > 0
                  ? filteredLeads.filter(lead => selectedLeads.includes(lead.id))
                  : filteredLeads
                )}
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Dialog open={showAddLead} onOpenChange={setShowAddLead}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Lead
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle>Add New Lead</DialogTitle>
                    <DialogDescription>
                      Enter lead information to add to your database
                    </DialogDescription>
                  </DialogHeader>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="lead-name">Name *</Label>
                      <Input
                        id="lead-name"
                        value={newLead.name || ''}
                        onChange={(e) => setNewLead(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-email">Email *</Label>
                      <Input
                        id="lead-email"
                        type="email"
                        value={newLead.email || ''}
                        onChange={(e) => setNewLead(prev => ({ ...prev, email: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-phone">Phone</Label>
                      <Input
                        id="lead-phone"
                        value={newLead.phone || ''}
                        onChange={(e) => setNewLead(prev => ({ ...prev, phone: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-company">Company *</Label>
                      <Input
                        id="lead-company"
                        value={newLead.company || ''}
                        onChange={(e) => setNewLead(prev => ({ ...prev, company: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-title">Job Title</Label>
                      <Input
                        id="lead-title"
                        value={newLead.title || ''}
                        onChange={(e) => setNewLead(prev => ({ ...prev, title: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-location">Location</Label>
                      <Input
                        id="lead-location"
                        value={newLead.location || ''}
                        onChange={(e) => setNewLead(prev => ({ ...prev, location: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-status">Status</Label>
                      <Select
                        value={newLead.status || 'Cold'}
                        onValueChange={(value) => setNewLead(prev => ({ ...prev, status: value as Lead['status'] }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Cold">Cold</SelectItem>
                          <SelectItem value="Warm">Warm</SelectItem>
                          <SelectItem value="Hot">Hot</SelectItem>
                          <SelectItem value="Qualified">Qualified</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lead-score">Score (0-100)</Label>
                      <Input
                        id="lead-score"
                        type="number"
                        min="0"
                        max="100"
                        value={newLead.score || 50}
                        onChange={(e) => setNewLead(prev => ({ ...prev, score: parseInt(e.target.value) || 50 }))}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lead-notes">Notes</Label>
                    <Textarea
                      id="lead-notes"
                      rows={3}
                      value={newLead.notes || ''}
                      onChange={(e) => setNewLead(prev => ({ ...prev, notes: e.target.value }))}
                    />
                  </div>
                  <div className="flex space-x-2">
                    <Button onClick={addLead} className="flex-1">
                      <UserPlus className="h-4 w-4 mr-2" />
                      Add Lead
                    </Button>
                    <Button variant="outline" onClick={() => setShowAddLead(false)}>
                      Cancel
                    </Button>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          {showFilters && (
            <Card className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label>Status</Label>
                  <div className="space-y-1">
                    {['Hot', 'Warm', 'Cold', 'Qualified', 'Converted', 'Lost'].map(status => (
                      <div key={status} className="flex items-center space-x-2">
                        <Checkbox
                          id={`status-${status}`}
                          checked={statusFilter.includes(status)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setStatusFilter(prev => [...prev, status]);
                            } else {
                              setStatusFilter(prev => prev.filter(s => s !== status));
                            }
                          }}
                        />
                        <Label htmlFor={`status-${status}`}>{status}</Label>
                      </div>
                    ))}
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Score Range</Label>
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      placeholder="Min"
                      value={scoreRange.min}
                      onChange={(e) => setScoreRange(prev => ({
                        ...prev,
                        min: parseInt(e.target.value) || 0
                      }))}
                    />
                    <Input
                      type="number"
                      placeholder="Max"
                      value={scoreRange.max}
                      onChange={(e) => setScoreRange(prev => ({
                        ...prev,
                        max: parseInt(e.target.value) || 100
                      }))}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label>Selected Leads</Label>
                  <div className="text-sm text-gray-600">
                    {selectedLeads.length} of {filteredLeads.length} selected
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedLeads([])}
                  >
                    Clear Selection
                  </Button>
                </div>
              </div>
            </Card>
          )}

          <div className="grid gap-4">
            {filteredLeads.length > 0 ? (
              filteredLeads.map((lead) => (
                <Card key={lead.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={selectedLeads.includes(lead.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedLeads(prev => [...prev, lead.id]);
                            } else {
                              setSelectedLeads(prev => prev.filter(id => id !== lead.id));
                            }
                          }}
                        />
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-medium">{lead.name}</h3>
                            <Badge className={getStatusColor(lead.status)}>
                              {getStatusIcon(lead.status)}
                              <span className="ml-1">{lead.status}</span>
                            </Badge>
                            <Badge variant="outline">{lead.source}</Badge>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                            <div className="flex items-center space-x-2">
                              <Building className="h-4 w-4" />
                              <span>{lead.company} • {lead.title}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4" />
                              <span>{lead.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4" />
                              <span>{lead.location}</span>
                            </div>
                          </div>

                          {lead.tags && lead.tags.length > 0 && (
                            <div className="flex flex-wrap gap-1 mt-2">
                              {lead.tags.map((tag, index) => (
                                <Badge key={index} variant="secondary" className="text-xs">
                                  {tag}
                                </Badge>
                              ))}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-4">
                        <div className="text-center">
                          <div className={`text-2xl font-bold ${getScoreColor(lead.score)}`}>
                            {lead.score}
                          </div>
                          <div className="text-xs text-gray-500">Score</div>
                        </div>
                        <div className="flex flex-col space-y-1">
                          <Select
                            value={lead.status}
                            onValueChange={(value) => updateLeadStatus(lead.id, value as Lead['status'])}
                          >
                            <SelectTrigger className="w-24 h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Cold">Cold</SelectItem>
                              <SelectItem value="Warm">Warm</SelectItem>
                              <SelectItem value="Hot">Hot</SelectItem>
                              <SelectItem value="Qualified">Qualified</SelectItem>
                              <SelectItem value="Converted">Converted</SelectItem>
                              <SelectItem value="Lost">Lost</SelectItem>
                            </SelectContent>
                          </Select>
                          <div className="flex space-x-1">
                            <Button variant="outline" size="sm">
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button variant="outline" size="sm">
                              <Mail className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => deleteLead(lead.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <Card className="border-dashed">
                <CardContent className="p-12 text-center">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
                  <p className="text-gray-600 mb-4">
                    {leads.length === 0
                      ? 'Start by adding your first lead or searching for new prospects'
                      : 'Try adjusting your filters or search criteria'
                    }
                  </p>
                  <Button onClick={() => setShowAddLead(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Lead
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="search" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>Lead Search & Discovery</span>
              </CardTitle>
              <CardDescription>
                Find and qualify potential leads using advanced search criteria
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="company-search">Company</Label>
                  <Input
                    id="company-search"
                    placeholder="Enter company name..."
                    value={searchCriteria.company || ''}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, company: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="title-search">Job Title</Label>
                  <Input
                    id="title-search"
                    placeholder="e.g., Marketing Manager"
                    value={searchCriteria.title || ''}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, title: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="location-search">Location</Label>
                  <Input
                    id="location-search"
                    placeholder="e.g., San Francisco, CA"
                    value={searchCriteria.location || ''}
                    onChange={(e) => setSearchCriteria(prev => ({ ...prev, location: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="industry-search">Industry</Label>
                  <Select
                    value={searchCriteria.industry || ''}
                    onValueChange={(value) => setSearchCriteria(prev => ({ ...prev, industry: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Technology">Technology</SelectItem>
                      <SelectItem value="Healthcare">Healthcare</SelectItem>
                      <SelectItem value="Finance">Finance</SelectItem>
                      <SelectItem value="E-commerce">E-commerce</SelectItem>
                      <SelectItem value="Manufacturing">Manufacturing</SelectItem>
                      <SelectItem value="Education">Education</SelectItem>
                      <SelectItem value="Real Estate">Real Estate</SelectItem>
                      <SelectItem value="Consulting">Consulting</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="company-size-search">Company Size</Label>
                  <Select
                    value={searchCriteria.company_size || ''}
                    onValueChange={(value) => setSearchCriteria(prev => ({ ...prev, company_size: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select company size" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1-10">1-10 employees</SelectItem>
                      <SelectItem value="11-50">11-50 employees</SelectItem>
                      <SelectItem value="51-200">51-200 employees</SelectItem>
                      <SelectItem value="201-500">201-500 employees</SelectItem>
                      <SelectItem value="501-1000">501-1000 employees</SelectItem>
                      <SelectItem value="1000+">1000+ employees</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="revenue-search">Revenue Range</Label>
                  <Select
                    value={searchCriteria.revenue_range || ''}
                    onValueChange={(value) => setSearchCriteria(prev => ({ ...prev, revenue_range: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select revenue range" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="<1M">Less than $1M</SelectItem>
                      <SelectItem value="1M-10M">$1M - $10M</SelectItem>
                      <SelectItem value="10M-50M">$10M - $50M</SelectItem>
                      <SelectItem value="50M-100M">$50M - $100M</SelectItem>
                      <SelectItem value="100M+">$100M+</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="keywords-search">Keywords</Label>
                <Input
                  id="keywords-search"
                  placeholder="e.g., SaaS, marketing automation, CRM"
                  value={searchCriteria.keywords || ''}
                  onChange={(e) => setSearchCriteria(prev => ({ ...prev, keywords: e.target.value }))}
                />
                <p className="text-xs text-gray-500">
                  Enter keywords related to the leads you're looking for
                </p>
              </div>

              <div className="flex space-x-2">
                <Button onClick={handleSearch} disabled={isSearching}>
                  {isSearching ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Searching...
                    </>
                  ) : (
                    <>
                      <Search className="h-4 w-4 mr-2" />
                      Search Leads
                    </>
                  )}
                </Button>
                <Button variant="outline">
                  <Copy className="h-4 w-4 mr-2" />
                  Save Search
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setSearchCriteria({})}
                >
                  Clear
                </Button>
              </div>

              {isSearching && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                    <span className="text-sm text-gray-600">Searching for leads...</span>
                  </div>
                  <Progress value={66} className="w-full" />
                  <p className="text-xs text-gray-500">
                    This may take a few moments while we search our database
                  </p>
                </div>
              )}

              <Card className="bg-blue-50 border-blue-200">
                <CardContent className="p-4">
                  <div className="flex items-start space-x-3">
                    <Target className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-blue-900">Search Tips</h4>
                      <ul className="text-sm text-blue-700 mt-1 space-y-1">
                        <li>• Use specific job titles for better targeting</li>
                        <li>• Combine multiple criteria for refined results</li>
                        <li>• Keywords help find leads with specific interests</li>
                        <li>• Save successful searches for future use</li>
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="campaigns" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Lead Campaigns</h2>
            <Dialog open={showAddCampaign} onOpenChange={setShowAddCampaign}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  New Campaign
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Create New Campaign</DialogTitle>
                  <DialogDescription>
                    Set up a new lead generation campaign with targeting criteria
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="campaign-name">Campaign Name *</Label>
                      <Input
                        id="campaign-name"
                        placeholder="Enter campaign name..."
                        value={newCampaign.name || ''}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, name: e.target.value }))}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="campaign-description">Description *</Label>
                      <Textarea
                        id="campaign-description"
                        placeholder="Describe your campaign goals and target audience..."
                        value={newCampaign.description || ''}
                        onChange={(e) => setNewCampaign(prev => ({ ...prev, description: e.target.value }))}
                        rows={3}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">Target Criteria (Optional)</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="target-industries">Industries</Label>
                        <Input
                          id="target-industries"
                          placeholder="e.g., Technology, SaaS, E-commerce"
                          value={newCampaign.target_criteria?.industries?.join(', ') || ''}
                          onChange={(e) => setNewCampaign(prev => ({
                            ...prev,
                            target_criteria: {
                              ...prev.target_criteria,
                              industries: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                            }
                          }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="target-titles">Job Titles</Label>
                        <Input
                          id="target-titles"
                          placeholder="e.g., CEO, CTO, Marketing Director"
                          value={newCampaign.target_criteria?.job_titles?.join(', ') || ''}
                          onChange={(e) => setNewCampaign(prev => ({
                            ...prev,
                            target_criteria: {
                              ...prev.target_criteria,
                              job_titles: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                            }
                          }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="target-sizes">Company Sizes</Label>
                        <Input
                          id="target-sizes"
                          placeholder="e.g., 1-50, 100-500, 500+"
                          value={newCampaign.target_criteria?.company_sizes?.join(', ') || ''}
                          onChange={(e) => setNewCampaign(prev => ({
                            ...prev,
                            target_criteria: {
                              ...prev.target_criteria,
                              company_sizes: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                            }
                          }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="target-locations">Locations</Label>
                        <Input
                          id="target-locations"
                          placeholder="e.g., San Francisco, New York, Remote"
                          value={newCampaign.target_criteria?.locations?.join(', ') || ''}
                          onChange={(e) => setNewCampaign(prev => ({
                            ...prev,
                            target_criteria: {
                              ...prev.target_criteria,
                              locations: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                            }
                          }))}
                        />
                      </div>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <Button onClick={addCampaign} className="flex-1">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Campaign
                    </Button>
                    <Button variant="outline" onClick={() => setShowAddCampaign(false)}>
                      Cancel
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          <div className="grid gap-4">
            {campaigns.map((campaign, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium">{campaign.name}</h3>
                        <Badge variant={campaign.status === 'Active' ? 'default' : 'secondary'}>
                          {campaign.status}
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-gray-900">{campaign.leads.toLocaleString()}</div>
                          <div className="text-sm text-gray-600">Total Leads</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">{campaign.qualified}</div>
                          <div className="text-sm text-gray-600">Qualified</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">{campaign.converted}</div>
                          <div className="text-sm text-gray-600">Converted</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">{campaign.conversionRate}%</div>
                          <div className="text-sm text-gray-600">Conv. Rate</div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">View</Button>
                      <Button variant="outline" size="sm">Edit</Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium">Total Leads</span>
                </div>
                <div className="text-2xl font-bold mt-2">4,240</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +18% this month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">Qualified Leads</span>
                </div>
                <div className="text-2xl font-bold mt-2">312</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +22% this month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium">Conversion Rate</span>
                </div>
                <div className="text-2xl font-bold mt-2">7.4%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +1.2% vs last month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Mail className="h-5 w-5 text-orange-600" />
                  <span className="text-sm font-medium">Avg. Lead Score</span>
                </div>
                <div className="text-2xl font-bold mt-2">74</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +5 points
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Lead Performance</CardTitle>
              <CardDescription>
                Track lead generation performance and conversion metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Analytics</h3>
                <p className="text-gray-600">
                  Detailed lead analytics and conversion tracking coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LeadGeneration;
