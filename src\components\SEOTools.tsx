import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  Search,
  Globe,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  ExternalLink,
  BarChart3,
  Target,
  RefreshCw,
  Copy,
  Download,
  Eye,
  Link,
  FileText,
  Image,
  Clock,
  Users,
  Zap,
  Shield,
  Smartphone,
  Monitor,
  Plus,
  Edit,
  Trash2,
  Star,
  Filter,
  Calendar,
  Hash
} from 'lucide-react';

interface SEOAnalysis {
  id: string;
  url: string;
  overall_score: number;
  page_speed: number;
  mobile_friendly: number;
  security: number;
  accessibility: number;
  seo_score: number;
  issues: SEOIssue[];
  recommendations: string[];
  meta_data: {
    title?: string;
    description?: string;
    keywords?: string[];
    h1_tags?: string[];
    images_without_alt?: number;
    internal_links?: number;
    external_links?: number;
  };
  created_at: string;
}

interface SEOIssue {
  type: 'error' | 'warning' | 'info';
  category: 'technical' | 'content' | 'performance' | 'mobile' | 'security';
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  fix_suggestion: string;
}

interface KeywordData {
  id: string;
  keyword: string;
  volume: string;
  difficulty: 'Low' | 'Medium' | 'High';
  cpc: string;
  competition: number;
  trend: 'up' | 'down' | 'stable';
  related_keywords: string[];
  questions: string[];
  serp_features: string[];
  created_at: string;
}

interface CompetitorAnalysis {
  id: string;
  competitor_url: string;
  domain_authority: number;
  organic_keywords: number;
  organic_traffic: string;
  backlinks: number;
  top_keywords: KeywordData[];
  content_gaps: string[];
  strengths: string[];
  weaknesses: string[];
  created_at: string;
}

interface BacklinkData {
  id: string;
  source_url: string;
  target_url: string;
  anchor_text: string;
  domain_authority: number;
  link_type: 'dofollow' | 'nofollow';
  status: 'active' | 'lost' | 'new';
  first_seen: string;
  last_seen: string;
}

const SEOTools: React.FC = () => {
  const [url, setUrl] = useState('');
  const [keyword, setKeyword] = useState('');
  const [competitorUrl, setCompetitorUrl] = useState('');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isResearching, setIsResearching] = useState(false);
  const [isAnalyzingCompetitor, setIsAnalyzingCompetitor] = useState(false);
  const [seoAnalyses, setSeoAnalyses] = useState<SEOAnalysis[]>([]);
  const [keywordData, setKeywordData] = useState<KeywordData[]>([]);
  const [competitorAnalyses, setCompetitorAnalyses] = useState<CompetitorAnalysis[]>([]);
  const [backlinks, setBacklinks] = useState<BacklinkData[]>([]);
  const [selectedAnalysis, setSelectedAnalysis] = useState<SEOAnalysis | null>(null);
  const [showAnalysisDialog, setShowAnalysisDialog] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Load data on component mount
  useEffect(() => {
    loadSEOAnalyses();
    loadKeywordData();
    loadCompetitorAnalyses();
    loadBacklinks();
  }, []);

  const loadSEOAnalyses = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('seo_analyses')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      setSeoAnalyses(data || []);
    } catch (error) {
      console.error('Failed to load SEO analyses:', error);
    }
  }, []);

  const loadKeywordData = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('keyword_data')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(20);

      if (error) throw error;
      setKeywordData(data || []);
    } catch (error) {
      console.error('Failed to load keyword data:', error);
    }
  }, []);

  const loadCompetitorAnalyses = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('competitor_analyses')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(10);

      if (error) throw error;
      setCompetitorAnalyses(data || []);
    } catch (error) {
      console.error('Failed to load competitor analyses:', error);
    }
  }, []);

  const loadBacklinks = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('backlinks')
        .select('*')
        .order('last_seen', { ascending: false })
        .limit(50);

      if (error) throw error;
      setBacklinks(data || []);
    } catch (error) {
      console.error('Failed to load backlinks:', error);
    }
  }, []);

  const handleSEOAnalysis = useCallback(async () => {
    if (!url.trim()) {
      showError(new Error('Please enter a valid URL'), 'SEO Analysis');
      return;
    }

    setIsAnalyzing(true);
    setAnalysisProgress(0);

    try {
      // Simulate analysis progress
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 20;
        });
      }, 500);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 4000));

      const mockAnalysis = await generateMockSEOAnalysis(url);

      const { data, error } = await supabase
        .from('seo_analyses')
        .insert([mockAnalysis])
        .select()
        .single();

      if (error) throw error;

      setSeoAnalyses(prev => [data, ...prev]);
      setSelectedAnalysis(data);
      setAnalysisProgress(100);

      showSuccess(`SEO analysis completed for ${url}`);
      setUrl('');

    } catch (error) {
      showError(error, 'SEO Analysis');
    } finally {
      setIsAnalyzing(false);
      setTimeout(() => setAnalysisProgress(0), 2000);
    }
  }, [url, showError, showSuccess]);

  const generateMockSEOAnalysis = useCallback(async (url: string): Promise<Partial<SEOAnalysis>> => {
    const scores = {
      overall_score: Math.floor(Math.random() * 40) + 60,
      page_speed: Math.floor(Math.random() * 30) + 70,
      mobile_friendly: Math.floor(Math.random() * 20) + 80,
      security: Math.floor(Math.random() * 15) + 85,
      accessibility: Math.floor(Math.random() * 25) + 75,
      seo_score: Math.floor(Math.random() * 35) + 65
    };

    const issues: SEOIssue[] = [
      {
        type: 'warning',
        category: 'content',
        title: 'Missing Meta Description',
        description: 'The page is missing a meta description tag',
        impact: 'medium',
        fix_suggestion: 'Add a compelling meta description between 150-160 characters'
      },
      {
        type: 'error',
        category: 'technical',
        title: 'Slow Page Load Time',
        description: 'Page takes more than 3 seconds to load',
        impact: 'high',
        fix_suggestion: 'Optimize images and minify CSS/JS files'
      }
    ];

    return {
      url,
      overall_score: scores.overall_score,
      page_speed: scores.page_speed,
      mobile_friendly: scores.mobile_friendly,
      security: scores.security,
      accessibility: scores.accessibility,
      seo_score: scores.seo_score,
      issues,
      recommendations: [
        'Optimize page loading speed',
        'Add missing meta descriptions',
        'Improve mobile responsiveness',
        'Add alt text to images'
      ],
      meta_data: {
        title: 'Sample Page Title',
        description: 'Sample meta description',
        keywords: ['web', 'scraping', 'data'],
        h1_tags: ['Main Heading'],
        images_without_alt: 3,
        internal_links: 12,
        external_links: 5
      },
      created_at: new Date().toISOString()
    };
  }, []);

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Search className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">SEO Tools</h1>
      </div>

      <Tabs defaultValue="analysis" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="analysis">SEO Analysis</TabsTrigger>
          <TabsTrigger value="keywords">Keyword Research</TabsTrigger>
          <TabsTrigger value="competitors">Competitor Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="analysis" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Website SEO Analysis</span>
              </CardTitle>
              <CardDescription>
                Analyze your website's SEO performance and get actionable insights
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="seo-url">Website URL</Label>
                <div className="flex space-x-2">
                  <Input
                    id="seo-url"
                    placeholder="https://example.com"
                    value={url}
                    onChange={(e) => setUrl(e.target.value)}
                  />
                  <Button onClick={handleSEOAnalysis} disabled={isAnalyzing || !url}>
                    {isAnalyzing ? 'Analyzing...' : 'Analyze'}
                  </Button>
                </div>
              </div>

              {isAnalyzing && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                    <span className="text-sm text-gray-600">Analyzing SEO metrics...</span>
                  </div>
                  <Progress value={33} className="w-full" />
                </div>
              )}

              {selectedAnalysis && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Overall Score</span>
                        <Badge variant="outline">{selectedAnalysis.overall_score}%</Badge>
                      </div>
                      <Progress value={selectedAnalysis.overall_score} className="mt-2" />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Page Speed</span>
                        <Badge variant="outline">{selectedAnalysis.page_speed}%</Badge>
                      </div>
                      <Progress value={selectedAnalysis.page_speed} className="mt-2" />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">Mobile Friendly</span>
                        <Badge variant="outline">{selectedAnalysis.mobile_friendly}%</Badge>
                      </div>
                      <Progress value={selectedAnalysis.mobile_friendly} className="mt-2" />
                    </CardContent>
                  </Card>
                  <Card>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium">SEO Score</span>
                        <Badge variant="outline">{selectedAnalysis.seo_score}%</Badge>
                      </div>
                      <Progress value={selectedAnalysis.seo_score} className="mt-2" />
                    </CardContent>
                  </Card>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="keywords" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Target className="h-5 w-5" />
                <span>Keyword Research</span>
              </CardTitle>
              <CardDescription>
                Discover high-value keywords for your content strategy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="keyword-input">Seed Keyword</Label>
                <div className="flex space-x-2">
                  <Input
                    id="keyword-input"
                    placeholder="Enter a keyword..."
                    value={keyword}
                    onChange={(e) => setKeyword(e.target.value)}
                  />
                  <Button>Research</Button>
                </div>
              </div>

              <div className="space-y-3">
                <h3 className="text-lg font-semibold">Keyword Suggestions</h3>
                {keywordData.map((item, index) => (
                  <Card key={index}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium">{item.keyword}</h4>
                          <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                            <span>Volume: {item.volume}</span>
                            <span>CPC: {item.cpc}</span>
                          </div>
                        </div>
                        <Badge
                          variant={item.difficulty === 'Low' ? 'default' : item.difficulty === 'Medium' ? 'secondary' : 'destructive'}
                        >
                          {item.difficulty}
                        </Badge>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="competitors" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Competitor Analysis</span>
              </CardTitle>
              <CardDescription>
                Analyze your competitors' SEO strategies and performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Competitor Analysis</h3>
                <p className="text-gray-600 mb-4">
                  Enter competitor URLs to analyze their SEO strategies and find opportunities
                </p>
                <Button>Start Analysis</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SEOTools;
