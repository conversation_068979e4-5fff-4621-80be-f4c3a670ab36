import React, { useState, useEffect } from 'react'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Activity, 
  Zap, 
  Database, 
  Wifi, 
  WifiOff, 
  Trash2,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react'
import { useMemoryMonitor, useNetworkStatus } from '@/hooks/usePerformance'
import { performanceMonitor } from '@/lib/performance'

interface PerformanceMonitorProps {
  isVisible?: boolean
  onClose?: () => void
}

const PerformanceMonitor: React.FC<PerformanceMonitorProps> = ({ 
  isVisible = false, 
  onClose 
}) => {
  const [metrics, setMetrics] = useState<Record<string, any>>({})
  const [cacheSize, setCacheSize] = useState<number>(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const memoryUsage = useMemoryMonitor()
  const { isOnline, networkInfo } = useNetworkStatus()

  useEffect(() => {
    if (isVisible) {
      refreshMetrics()
      getCacheSize()
    }
  }, [isVisible])

  const refreshMetrics = () => {
    setIsRefreshing(true)
    const allMetrics = performanceMonitor.getAllMetrics()
    setMetrics(allMetrics)
    setTimeout(() => setIsRefreshing(false), 500)
  }

  const getCacheSize = async () => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      try {
        const messageChannel = new MessageChannel()
        messageChannel.port1.onmessage = (event) => {
          if (event.data.cacheSize) {
            setCacheSize(event.data.cacheSize)
          }
        }
        
        navigator.serviceWorker.controller.postMessage(
          { type: 'GET_CACHE_SIZE' },
          [messageChannel.port2]
        )
      } catch (error) {
        console.error('Failed to get cache size:', error)
      }
    }
  }

  const clearCache = async () => {
    if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
      try {
        const messageChannel = new MessageChannel()
        messageChannel.port1.onmessage = (event) => {
          if (event.data.success) {
            setCacheSize(0)
            refreshMetrics()
          }
        }
        
        navigator.serviceWorker.controller.postMessage(
          { type: 'CLEAR_CACHE' },
          [messageChannel.port2]
        )
      } catch (error) {
        console.error('Failed to clear cache:', error)
      }
    }
  }

  const clearMetrics = () => {
    performanceMonitor.clearMetrics()
    setMetrics({})
  }

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getPerformanceStatus = () => {
    if (!memoryUsage) return 'unknown'
    if (memoryUsage.usedPercentage > 80) return 'critical'
    if (memoryUsage.usedPercentage > 60) return 'warning'
    return 'good'
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'critical': return 'destructive'
      case 'warning': return 'secondary'
      case 'good': return 'default'
      default: return 'outline'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />
      case 'warning': return <AlertTriangle className="h-4 w-4" />
      case 'good': return <CheckCircle className="h-4 w-4" />
      default: return <Activity className="h-4 w-4" />
    }
  }

  if (!isVisible) return null

  const performanceStatus = getPerformanceStatus()

  return (
    <div className="fixed inset-0 bg-black/50 z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-auto">
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Performance Monitor</span>
            <Badge variant={getStatusColor(performanceStatus)}>
              {getStatusIcon(performanceStatus)}
              {performanceStatus.toUpperCase()}
            </Badge>
          </CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={refreshMetrics}
              disabled={isRefreshing}
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
            <Button variant="outline" size="sm" onClick={onClose}>
              ×
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs defaultValue="overview" className="space-y-4">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="memory">Memory</TabsTrigger>
              <TabsTrigger value="network">Network</TabsTrigger>
              <TabsTrigger value="cache">Cache</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center space-x-2">
                      <Zap className="h-4 w-4" />
                      <span>Performance</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {performanceStatus === 'good' ? '✅' : 
                       performanceStatus === 'warning' ? '⚠️' : '🚨'}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {performanceStatus === 'good' ? 'Optimal' :
                       performanceStatus === 'warning' ? 'Moderate' : 'Critical'}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center space-x-2">
                      {isOnline ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
                      <span>Network</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {isOnline ? '🟢' : '🔴'}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {isOnline ? 'Online' : 'Offline'}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm flex items-center space-x-2">
                      <Database className="h-4 w-4" />
                      <span>Cache</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {formatBytes(cacheSize)}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Cached data
                    </p>
                  </CardContent>
                </Card>
              </div>

              {Object.keys(metrics).length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Performance Metrics</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {Object.entries(metrics).map(([label, data]) => (
                        <div key={label} className="flex justify-between items-center">
                          <span className="text-sm">{label}</span>
                          <Badge variant="outline">
                            {data?.avg?.toFixed(2)}ms avg
                          </Badge>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="memory" className="space-y-4">
              {memoryUsage && (
                <Card>
                  <CardHeader>
                    <CardTitle className="text-sm">Memory Usage</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <div className="flex justify-between text-sm mb-2">
                        <span>Used Memory</span>
                        <span>{memoryUsage.usedPercentage.toFixed(1)}%</span>
                      </div>
                      <Progress value={memoryUsage.usedPercentage} />
                    </div>
                    
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Used:</span>
                        <div className="font-mono">{formatBytes(memoryUsage.usedJSHeapSize)}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Total:</span>
                        <div className="font-mono">{formatBytes(memoryUsage.totalJSHeapSize)}</div>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Limit:</span>
                        <div className="font-mono">{formatBytes(memoryUsage.jsHeapSizeLimit)}</div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            <TabsContent value="network" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">Network Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge variant={isOnline ? 'default' : 'destructive'}>
                        {isOnline ? 'Online' : 'Offline'}
                      </Badge>
                    </div>
                    
                    {networkInfo && (
                      <>
                        <div className="flex justify-between">
                          <span>Connection Type:</span>
                          <span className="font-mono">{networkInfo.effectiveType || 'Unknown'}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Downlink:</span>
                          <span className="font-mono">{networkInfo.downlink || 'Unknown'} Mbps</span>
                        </div>
                        <div className="flex justify-between">
                          <span>RTT:</span>
                          <span className="font-mono">{networkInfo.rtt || 'Unknown'} ms</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Save Data:</span>
                          <Badge variant={networkInfo.saveData ? 'secondary' : 'outline'}>
                            {networkInfo.saveData ? 'Enabled' : 'Disabled'}
                          </Badge>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="cache" className="space-y-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-sm">Cache Management</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearCache}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Cache
                  </Button>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Cache Size:</span>
                      <span className="font-mono">{formatBytes(cacheSize)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Service Worker:</span>
                      <Badge variant={
                        'serviceWorker' in navigator && navigator.serviceWorker.controller 
                          ? 'default' 
                          : 'secondary'
                      }>
                        {'serviceWorker' in navigator && navigator.serviceWorker.controller 
                          ? 'Active' 
                          : 'Inactive'}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-sm">Performance Metrics</CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearMetrics}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Clear Metrics
                  </Button>
                </CardHeader>
                <CardContent>
                  {Object.keys(metrics).length === 0 ? (
                    <p className="text-sm text-muted-foreground">No metrics available</p>
                  ) : (
                    <div className="space-y-2">
                      {Object.entries(metrics).map(([label, data]) => (
                        <div key={label} className="border rounded p-2">
                          <div className="font-medium text-sm">{label}</div>
                          <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground mt-1">
                            <div>Avg: {data?.avg?.toFixed(2)}ms</div>
                            <div>Min: {data?.min?.toFixed(2)}ms</div>
                            <div>Max: {data?.max?.toFixed(2)}ms</div>
                          </div>
                          <div className="text-xs text-muted-foreground">
                            Count: {data?.count}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

export default PerformanceMonitor
