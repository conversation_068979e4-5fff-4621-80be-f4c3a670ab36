<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Content Tables - Supabase Setup</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #1e293b;
            margin-bottom: 10px;
        }
        .subtitle {
            color: #64748b;
            margin-bottom: 30px;
        }
        .config-section {
            background: #f1f5f9;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #374151;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .success {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .error {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 12px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .log {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            margin: 5px 0;
        }
        .status.success { background: #dcfce7; color: #166534; }
        .status.error { background: #fef2f2; color: #dc2626; }
        .status.info { background: #dbeafe; color: #1d4ed8; }
        .tables-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .table-card {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
        }
        .table-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
        }
        .table-description {
            font-size: 13px;
            color: #64748b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗄️ Create Content Tables</h1>
        <p class="subtitle">This tool will create the missing database tables needed for the Content Ideas functionality.</p>

        <div class="config-section">
            <h3>Supabase Configuration</h3>
            <div class="input-group">
                <label for="supabaseUrl">Supabase URL:</label>
                <input type="text" id="supabaseUrl" placeholder="https://your-project.supabase.co" />
            </div>
            <div class="input-group">
                <label for="supabaseKey">Supabase Anon Key:</label>
                <input type="password" id="supabaseKey" placeholder="Your anon key" />
            </div>
        </div>

        <div class="tables-grid">
            <div class="table-card">
                <div class="table-name">content_ideas</div>
                <div class="table-description">Stores AI-generated and manual content ideas with metadata</div>
            </div>
            <div class="table-card">
                <div class="table-name">trending_topics</div>
                <div class="table-description">Tracks trending topics with growth metrics and opportunities</div>
            </div>
            <div class="table-card">
                <div class="table-name">content_templates</div>
                <div class="table-description">Reusable content templates for different content types</div>
            </div>
            <div class="table-card">
                <div class="table-name">content_calendar</div>
                <div class="table-description">Content scheduling and planning calendar</div>
            </div>
        </div>

        <button onclick="createTables()" id="createBtn">🚀 Create Tables</button>
        <button onclick="testTables()" id="testBtn" disabled>🧪 Test Tables</button>
        <button onclick="clearLog()" id="clearBtn">🗑️ Clear Log</button>

        <div id="output"></div>
        <div id="log" class="log"></div>
    </div>

    <script>
        let supabase = null;

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('log');
            const statusClass = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            
            logElement.innerHTML += `<span class="status ${statusClass}">[${timestamp}] ${message}</span>\n`;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[${timestamp}] ${message}`);
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
            document.getElementById('output').innerHTML = '';
        }

        function initSupabase() {
            const url = document.getElementById('supabaseUrl').value.trim();
            const key = document.getElementById('supabaseKey').value.trim();

            if (!url || !key) {
                log('❌ Please enter both Supabase URL and key', 'error');
                return false;
            }

            try {
                supabase = window.supabase.createClient(url, key);
                log('✅ Supabase client initialized', 'success');
                return true;
            } catch (error) {
                log(`❌ Failed to initialize Supabase: ${error.message}`, 'error');
                return false;
            }
        }

        async function createTables() {
            if (!initSupabase()) return;

            const createBtn = document.getElementById('createBtn');
            const testBtn = document.getElementById('testBtn');

            createBtn.disabled = true;
            createBtn.textContent = '⏳ Creating Tables...';

            try {
                log('🏗️ Starting table creation process...', 'info');

                // Create tables directly using Supabase client
                await createTablesDirectly();
                await createIndexesAndPolicies();
                await insertSampleData();

                testBtn.disabled = false;
                log('🎉 All tables created successfully!', 'success');

            } catch (error) {
                log('❌ Error creating tables: ' + error.message, 'error');
                console.error('Full error:', error);
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = '🚀 Create Tables';
            }
        }

        async function createTablesDirectly() {
            log('🔧 Creating tables directly...', 'info');

            try {
                // Create content_ideas table
                log('Creating content_ideas table...', 'info');
                await supabase.rpc('exec_sql', {
                    sql_query: `
                    CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
                    DROP TABLE IF EXISTS content_ideas CASCADE;
                    CREATE TABLE content_ideas (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        title TEXT NOT NULL,
                        type TEXT NOT NULL,
                        difficulty TEXT NOT NULL,
                        estimated_time TEXT NOT NULL DEFAULT '30 min',
                        keywords JSONB DEFAULT '[]',
                        trending BOOLEAN DEFAULT false,
                        engagement TEXT DEFAULT 'Medium',
                        description TEXT,
                        outline JSONB DEFAULT '[]',
                        target_audience TEXT,
                        content_pillars JSONB DEFAULT '[]',
                        seo_score INTEGER DEFAULT 0,
                        status TEXT NOT NULL DEFAULT 'Draft',
                        performance JSONB DEFAULT '{}',
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    );`
                });
                log('✅ content_ideas table created', 'success');

                // Create trending_topics table
                log('Creating trending_topics table...', 'info');
                await supabase.rpc('exec_sql', {
                    sql_query: `
                    CREATE TABLE trending_topics (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        topic TEXT NOT NULL,
                        growth TEXT NOT NULL,
                        volume TEXT NOT NULL,
                        category TEXT NOT NULL,
                        related_keywords JSONB DEFAULT '[]',
                        content_opportunities INTEGER DEFAULT 0,
                        competition_level TEXT DEFAULT 'Medium',
                        last_updated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    );`
                });
                log('✅ trending_topics table created', 'success');

                // Create content_templates table
                log('Creating content_templates table...', 'info');
                await supabase.rpc('exec_sql', {
                    sql_query: `
                    CREATE TABLE content_templates (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        name TEXT NOT NULL,
                        type TEXT NOT NULL,
                        description TEXT,
                        structure JSONB DEFAULT '[]',
                        usage_count INTEGER DEFAULT 0,
                        category TEXT NOT NULL,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    );`
                });
                log('✅ content_templates table created', 'success');

                // Create content_calendar table
                log('Creating content_calendar table...', 'info');
                await supabase.rpc('exec_sql', {
                    sql_query: `
                    CREATE TABLE content_calendar (
                        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                        title TEXT NOT NULL,
                        type TEXT NOT NULL,
                        scheduled_date TIMESTAMP WITH TIME ZONE NOT NULL,
                        status TEXT NOT NULL DEFAULT 'Planned',
                        assigned_to TEXT,
                        content_idea_id UUID,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
                    );`
                });
                log('✅ content_calendar table created', 'success');

            } catch (error) {
                log('❌ Error in createTablesDirectly: ' + error.message, 'error');
                throw error;
            }
        }

        async function createIndexesAndPolicies() {
            log('🔐 Creating indexes and security policies...', 'info');

            try {
                // Enable RLS and create policies
                const policies = [
                    'ALTER TABLE content_ideas ENABLE ROW LEVEL SECURITY;',
                    'ALTER TABLE trending_topics ENABLE ROW LEVEL SECURITY;',
                    'ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;',
                    'ALTER TABLE content_calendar ENABLE ROW LEVEL SECURITY;',
                    'CREATE POLICY "Allow all operations on content_ideas" ON content_ideas FOR ALL USING (true);',
                    'CREATE POLICY "Allow all operations on trending_topics" ON trending_topics FOR ALL USING (true);',
                    'CREATE POLICY "Allow all operations on content_templates" ON content_templates FOR ALL USING (true);',
                    'CREATE POLICY "Allow all operations on content_calendar" ON content_calendar FOR ALL USING (true);'
                ];

                for (const policy of policies) {
                    try {
                        await supabase.rpc('exec_sql', { sql_query: policy });
                    } catch (err) {
                        log('⚠️ Policy creation warning: ' + err.message, 'info');
                    }
                }

                log('✅ Security policies created', 'success');
            } catch (error) {
                log('⚠️ Warning creating policies: ' + error.message, 'info');
            }
        }

        async function insertSampleData() {
            log('📊 Inserting sample data...', 'info');

            try {
                // Insert sample content ideas
                const { error: ideasError } = await supabase
                    .from('content_ideas')
                    .insert([
                        {
                            title: 'The Ultimate Guide to Web Scraping Ethics',
                            type: 'Blog Post',
                            difficulty: 'Medium',
                            estimated_time: '45 min',
                            keywords: ['web scraping', 'ethics', 'best practices'],
                            trending: true,
                            engagement: 'High',
                            description: 'A comprehensive guide covering ethical considerations in web scraping',
                            outline: ['Introduction to Ethics', 'Legal Considerations', 'Best Practices', 'Case Studies'],
                            target_audience: 'Developers and Data Scientists',
                            content_pillars: ['Education', 'Best Practices'],
                            seo_score: 85,
                            status: 'Draft'
                        }
                    ]);

                if (ideasError) {
                    log('⚠️ Sample data warning: ' + ideasError.message, 'info');
                } else {
                    log('✅ Sample content ideas inserted', 'success');
                }

                // Insert sample trending topics
                const { error: topicsError } = await supabase
                    .from('trending_topics')
                    .insert([
                        {
                            topic: 'AI-powered web scraping',
                            growth: '+156%',
                            volume: '12.5K',
                            category: 'Technology',
                            related_keywords: ['AI', 'machine learning', 'automation'],
                            content_opportunities: 15,
                            competition_level: 'Medium'
                        }
                    ]);

                if (topicsError) {
                    log('⚠️ Trending topics warning: ' + topicsError.message, 'info');
                } else {
                    log('✅ Sample trending topics inserted', 'success');
                }

                // Insert sample templates
                const { error: templatesError } = await supabase
                    .from('content_templates')
                    .insert([
                        {
                            name: 'How-to Guide Template',
                            type: 'Blog Post',
                            description: 'Template for creating step-by-step guides',
                            structure: ['Introduction', 'Prerequisites', 'Step-by-step Instructions', 'Troubleshooting', 'Conclusion'],
                            usage_count: 5,
                            category: 'Educational'
                        }
                    ]);

                if (templatesError) {
                    log('⚠️ Templates warning: ' + templatesError.message, 'info');
                } else {
                    log('✅ Sample templates inserted', 'success');
                }

            } catch (error) {
                log('⚠️ Warning inserting sample data: ' + error.message, 'info');
            }
        }

        async function testTables() {
            if (!supabase) {
                log('❌ Supabase not initialized', 'error');
                return;
            }

            log('🧪 Testing table access...', 'info');

            const tables = ['content_ideas', 'trending_topics', 'content_templates', 'content_calendar'];
            let allTablesWork = true;

            for (const table of tables) {
                try {
                    const { data, error, count } = await supabase
                        .from(table)
                        .select('*', { count: 'exact', head: true });

                    if (error) {
                        log('❌ ' + table + ': ' + error.message, 'error');
                        allTablesWork = false;
                    } else {
                        log('✅ ' + table + ': Table accessible (' + (count || 0) + ' rows)', 'success');
                    }
                } catch (err) {
                    log('💥 ' + table + ': ' + err.message, 'error');
                    allTablesWork = false;
                }
            }

            if (allTablesWork) {
                log('🎉 All tables are working correctly!', 'success');
                document.getElementById('output').innerHTML = '<div class="success">✅ All content tables are now ready! You can refresh your application to use the Content Ideas feature.</div>';
            } else {
                log('⚠️ Some tables have issues. Check the logs above.', 'error');
            }
        }

        // Auto-fill Supabase URL if we can detect it
        window.addEventListener('load', () => {
            const currentUrl = window.location.href;
            if (currentUrl.includes('supabase.co')) {
                const urlMatch = currentUrl.match(/https:\/\/([^.]+)\.supabase\.co/);
                if (urlMatch) {
                    document.getElementById('supabaseUrl').value = 'https://' + urlMatch[1] + '.supabase.co';
                }
            }
        });
    </script>
</body>
</html>
