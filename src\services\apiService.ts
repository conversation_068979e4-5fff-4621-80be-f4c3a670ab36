import { supabase } from '@/lib/supabase';

export interface ApiResponse<T = any> {
  data?: T;
  error?: string;
  success: boolean;
  message?: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface FilterParams {
  [key: string]: any;
}

class ApiService {
  private baseUrl: string;
  private apiKey: string | null = null;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '/api';
  }

  setApiKey(key: string) {
    this.apiKey = key;
  }

  private getHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`;
    }

    return headers;
  }

  private async handleResponse<T>(response: Response): Promise<ApiResponse<T>> {
    try {
      const data = await response.json();
      
      if (!response.ok) {
        return {
          success: false,
          error: data.message || `HTTP ${response.status}: ${response.statusText}`,
          data: data
        };
      }

      return {
        success: true,
        data: data,
        message: data.message
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Generic CRUD operations
  async get<T>(endpoint: string, params?: FilterParams & PaginationParams): Promise<ApiResponse<T>> {
    try {
      const url = new URL(`${this.baseUrl}${endpoint}`);
      
      if (params) {
        Object.entries(params).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            url.searchParams.append(key, value.toString());
          }
        });
      }

      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: this.getHeaders(),
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  async post<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'POST',
        headers: this.getHeaders(),
        body: JSON.stringify(data),
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  async put<T>(endpoint: string, data: any): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'PUT',
        headers: this.getHeaders(),
        body: JSON.stringify(data),
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    try {
      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method: 'DELETE',
        headers: this.getHeaders(),
      });

      return this.handleResponse<T>(response);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Network error'
      };
    }
  }

  // Specialized methods for different services
  
  // Web Scraping
  async startScraping(config: any): Promise<ApiResponse<{ jobId: string }>> {
    return this.post('/scraping/start', config);
  }

  async getScrapingStatus(jobId: string): Promise<ApiResponse<any>> {
    return this.get(`/scraping/status/${jobId}`);
  }

  async getScrapingResults(jobId: string): Promise<ApiResponse<any[]>> {
    return this.get(`/scraping/results/${jobId}`);
  }

  // Keyword Research
  async searchKeywords(query: string, options?: any): Promise<ApiResponse<any[]>> {
    return this.post('/keywords/search', { query, ...options });
  }

  async getKeywordSuggestions(seed: string): Promise<ApiResponse<any[]>> {
    return this.get(`/keywords/suggestions?seed=${encodeURIComponent(seed)}`);
  }

  // SEO Analysis
  async analyzeSEO(url: string): Promise<ApiResponse<any>> {
    return this.post('/seo/analyze', { url });
  }

  async getCompetitorAnalysis(domain: string): Promise<ApiResponse<any>> {
    return this.post('/seo/competitor', { domain });
  }

  // Lead Generation
  async searchLeads(criteria: any): Promise<ApiResponse<any[]>> {
    return this.post('/leads/search', criteria);
  }

  async enrichLead(leadId: string): Promise<ApiResponse<any>> {
    return this.post(`/leads/${leadId}/enrich`, {});
  }

  // Email Marketing
  async sendEmail(campaignData: any): Promise<ApiResponse<any>> {
    return this.post('/email/send', campaignData);
  }

  async getEmailTemplates(): Promise<ApiResponse<any[]>> {
    return this.get('/email/templates');
  }

  // Social Media
  async postToSocial(platforms: string[], content: any): Promise<ApiResponse<any>> {
    return this.post('/social/post', { platforms, content });
  }

  async getSocialAnalytics(platform: string, dateRange?: any): Promise<ApiResponse<any>> {
    return this.get(`/social/analytics/${platform}`, dateRange);
  }

  // Content Generation
  async generateContent(prompt: string, type: string): Promise<ApiResponse<any>> {
    return this.post('/content/generate', { prompt, type });
  }

  async getContentIdeas(topic: string): Promise<ApiResponse<any[]>> {
    return this.post('/content/ideas', { topic });
  }

  // Data Export
  async exportData(format: string, data: any[], options?: any): Promise<ApiResponse<{ downloadUrl: string }>> {
    return this.post('/export', { format, data, options });
  }

  async getExportStatus(exportId: string): Promise<ApiResponse<any>> {
    return this.get(`/export/status/${exportId}`);
  }

  // File Upload
  async uploadFile(file: File, type?: string): Promise<ApiResponse<{ url: string }>> {
    const formData = new FormData();
    formData.append('file', file);
    if (type) formData.append('type', type);

    try {
      const response = await fetch(`${this.baseUrl}/upload`, {
        method: 'POST',
        headers: {
          'Authorization': this.apiKey ? `Bearer ${this.apiKey}` : '',
        },
        body: formData,
      });

      return this.handleResponse(response);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Upload failed'
      };
    }
  }

  // Health check
  async healthCheck(): Promise<ApiResponse<{ status: string }>> {
    return this.get('/health');
  }
}

// Create singleton instance
export const apiService = new ApiService();

// Supabase integration helpers
export class SupabaseService {
  static async query<T>(
    table: string,
    options: {
      select?: string;
      filters?: Record<string, any>;
      orderBy?: string;
      orderDirection?: 'asc' | 'desc';
      limit?: number;
      offset?: number;
    } = {}
  ): Promise<ApiResponse<T[]>> {
    try {
      let query = supabase.from(table).select(options.select || '*');

      if (options.filters) {
        Object.entries(options.filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value);
          }
        });
      }

      if (options.orderBy) {
        query = query.order(options.orderBy, { 
          ascending: options.orderDirection === 'asc' 
        });
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 10) - 1);
      }

      const { data, error } = await query;

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data: data || [] };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Database error'
      };
    }
  }

  static async insert<T>(table: string, data: any): Promise<ApiResponse<T>> {
    try {
      const { data: result, error } = await supabase
        .from(table)
        .insert([data])
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Insert failed'
      };
    }
  }

  static async update<T>(table: string, id: string, data: any): Promise<ApiResponse<T>> {
    try {
      const { data: result, error } = await supabase
        .from(table)
        .update(data)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true, data: result };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Update failed'
      };
    }
  }

  static async delete(table: string, id: string): Promise<ApiResponse<void>> {
    try {
      const { error } = await supabase
        .from(table)
        .delete()
        .eq('id', id);

      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Delete failed'
      };
    }
  }
}

// Rate limiting helper
export class RateLimiter {
  private requests: number[] = [];
  private maxRequests: number;
  private timeWindow: number; // in milliseconds

  constructor(maxRequests: number = 100, timeWindow: number = 60000) {
    this.maxRequests = maxRequests;
    this.timeWindow = timeWindow;
  }

  async checkLimit(): Promise<boolean> {
    const now = Date.now();

    // Remove old requests outside the time window
    this.requests = this.requests.filter(time => now - time < this.timeWindow);

    if (this.requests.length >= this.maxRequests) {
      return false; // Rate limit exceeded
    }

    this.requests.push(now);
    return true;
  }

  getTimeUntilReset(): number {
    if (this.requests.length === 0) return 0;

    const oldestRequest = Math.min(...this.requests);
    const timeUntilReset = this.timeWindow - (Date.now() - oldestRequest);

    return Math.max(0, timeUntilReset);
  }
}

// Request queue for handling concurrent requests
export class RequestQueue {
  private queue: Array<() => Promise<any>> = [];
  private processing = false;
  private maxConcurrent: number;
  private currentRequests = 0;

  constructor(maxConcurrent: number = 5) {
    this.maxConcurrent = maxConcurrent;
  }

  async add<T>(request: () => Promise<T>): Promise<T> {
    return new Promise((resolve, reject) => {
      this.queue.push(async () => {
        try {
          const result = await request();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });

      this.processQueue();
    });
  }

  private async processQueue() {
    if (this.processing || this.currentRequests >= this.maxConcurrent) {
      return;
    }

    this.processing = true;

    while (this.queue.length > 0 && this.currentRequests < this.maxConcurrent) {
      const request = this.queue.shift();
      if (request) {
        this.currentRequests++;
        request().finally(() => {
          this.currentRequests--;
          this.processQueue();
        });
      }
    }

    this.processing = false;
  }
}

// Create instances
export const rateLimiter = new RateLimiter();
export const requestQueue = new RequestQueue();

export default apiService;
