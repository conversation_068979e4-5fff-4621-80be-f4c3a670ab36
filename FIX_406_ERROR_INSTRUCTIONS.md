# 🔧 Fix 406 Error - Step by Step Instructions

The 406 (Not Acceptable) error you're seeing when querying the `user_settings` table is likely caused by one of these issues:

1. **Missing or incorrectly structured tables**
2. **Row Level Security (RLS) policies blocking access**
3. **Authentication issues**
4. **Foreign key constraint problems**

## 🚀 Quick Fix Steps

### Step 1: Run the Database Fix Script

1. **Open your Supabase Dashboard**: https://supabase.com/dashboard/project/rclikclltlyzyojjttqv/sql
2. **Go to SQL Editor**
3. **Copy and paste the entire contents** of `database/comprehensive_406_fix.sql`
4. **Click "Run"** to execute the script

This script will:
- ✅ Create all missing tables with proper structure
- ✅ Set up correct foreign key relationships
- ✅ Configure Row Level Security policies
- ✅ Create triggers for automatic user setup
- ✅ Insert default settings for existing users

### Step 2: Test the Fix

You have several options to test:

#### Option A: Use the HTML Test File
1. Open `test-406-fix.html` in your browser
2. Click "Test Connection"
3. Click "Sign In Anonymously"
4. Click "Test User Settings Query"

#### Option B: Use the React Debug Component
1. Start your React app: `npm run dev`
2. Navigate to: `http://localhost:5173/debug-406`
3. Click "Sign In Anonymously"
4. Click "Run All Tests"

#### Option C: Check Browser Console
1. Open your app normally
2. Open browser DevTools (F12)
3. Check the Console tab for any remaining 406 errors

### Step 3: Verify the Fix

After running the database script, you should see:
- ✅ No more 406 errors in the console
- ✅ User settings loading properly
- ✅ All settings tables accessible

## 🔍 What the Fix Does

### Database Structure
- Creates `user_profiles` table as the foundation
- Creates `user_settings` table with proper foreign keys
- Creates additional settings tables (`notification_settings`, `privacy_settings`, `security_settings`)
- Adds proper indexes for performance

### Row Level Security
- Enables RLS on all tables
- Creates policies that allow users to access only their own data
- Uses `auth.uid() = user_id` pattern for security

### Automatic User Setup
- Creates a trigger that automatically creates user profiles and default settings
- Ensures every new user gets proper database records

### Data Migration
- Inserts default settings for any existing users
- Handles conflicts gracefully with `ON CONFLICT DO NOTHING`

## 🐛 If You Still See Issues

### Common Problems and Solutions

1. **Still getting 406 errors**
   - Check if the SQL script ran completely without errors
   - Verify you're signed in (anonymous sign-in is fine for testing)
   - Check the browser console for specific error details

2. **Authentication issues**
   - Make sure your `.env` file has the correct Supabase credentials
   - Try signing out and signing in again
   - Use the anonymous sign-in for testing

3. **RLS policy issues**
   - The script temporarily disables RLS during setup
   - If needed, you can manually disable RLS for testing:
     ```sql
     ALTER TABLE user_settings DISABLE ROW LEVEL SECURITY;
     ```

4. **Foreign key constraint errors**
   - The script creates tables in the correct order
   - If you see FK errors, run the script again

## 📊 Debug Information

The debug components will show you:
- ✅ Connection status to Supabase
- ✅ Authentication status
- ✅ User profile existence
- ✅ Settings table accessibility
- ✅ Specific error messages and codes

## 🔄 After the Fix

Once the 406 error is resolved:

1. **Remove the debug route** from `src/App.tsx` (the `/debug-406` route)
2. **Delete the debug files** if you don't need them:
   - `src/components/Enhanced406Debug.tsx`
   - `test-406-fix.html`
   - `FIX_406_ERROR_INSTRUCTIONS.md`

3. **Test your normal app functionality** to ensure everything works

## 📞 Need Help?

If you're still experiencing issues after following these steps:

1. **Check the browser console** for specific error messages
2. **Run the debug component** to get detailed information
3. **Verify your Supabase project settings** in the dashboard
4. **Check that your environment variables** are correct

The most common cause of 406 errors is missing tables or incorrect RLS policies, which this fix addresses comprehensively.

## ✅ Success Indicators

You'll know the fix worked when:
- No 406 errors in the browser console
- The debug component shows all green checkmarks
- User settings load properly in your app
- You can create and update user settings without errors

Good luck! 🚀
