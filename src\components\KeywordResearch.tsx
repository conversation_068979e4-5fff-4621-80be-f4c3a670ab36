import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  TrendingUp,
  Search,
  Target,
  BarChart3,
  Filter,
  Download,
  Star,
  Eye,
  <PERSON><PERSON>,
  <PERSON>fresh<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>D<PERSON>,
  Minus,
  Calendar,
  Plus
} from 'lucide-react';

interface Keyword {
  id: string;
  keyword: string;
  volume: string;
  difficulty: number;
  cpc: string;
  trend: 'up' | 'down' | 'stable';
  competition: 'High' | 'Medium' | 'Low';
  intent: 'Commercial' | 'Informational' | 'Navigational' | 'Transactional';
  serp_features?: string[];
  related_keywords?: string[];
  questions?: string[];
  saved?: boolean;
  created_at?: string;
}

interface FilterOptions {
  minVolume: number;
  maxVolume: number;
  minDifficulty: number;
  maxDifficulty: number;
  competition: string[];
  intent: string[];
  trend: string[];
}

const KeywordResearch: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [keywords, setKeywords] = useState<Keyword[]>([]);
  const [filteredKeywords, setFilteredKeywords] = useState<Keyword[]>([]);
  const [savedKeywords, setSavedKeywords] = useState<Keyword[]>([]);
  const [sortBy, setSortBy] = useState<string>('volume');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedKeywords, setSelectedKeywords] = useState<string[]>([]);
  const [filters, setFilters] = useState<FilterOptions>({
    minVolume: 0,
    maxVolume: 1000000,
    minDifficulty: 0,
    maxDifficulty: 100,
    competition: [],
    intent: [],
    trend: []
  });

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Generate mock keyword data based on search term
  const generateKeywordData = useCallback(async (searchTerm: string): Promise<Keyword[]> => {
    const baseKeywords = [
      { base: 'web scraping', volume: '49,500', difficulty: 72, cpc: '$3.45', trend: 'up' as const, competition: 'High' as const, intent: 'Commercial' as const },
      { base: 'data extraction', volume: '12,100', difficulty: 45, cpc: '$2.80', trend: 'up' as const, competition: 'Medium' as const, intent: 'Commercial' as const },
      { base: 'automated scraping', volume: '8,100', difficulty: 38, cpc: '$2.15', trend: 'stable' as const, competition: 'Medium' as const, intent: 'Informational' as const },
      { base: 'python scraping', volume: '22,200', difficulty: 55, cpc: '$1.90', trend: 'up' as const, competition: 'Medium' as const, intent: 'Informational' as const },
      { base: 'scraping api', volume: '6,600', difficulty: 42, cpc: '$4.20', trend: 'down' as const, competition: 'Low' as const, intent: 'Commercial' as const }
    ];

    return baseKeywords.map((item, index) => ({
      id: `kw-${Date.now()}-${index}`,
      keyword: `${searchTerm} ${item.base}`,
      volume: item.volume,
      difficulty: item.difficulty,
      cpc: item.cpc,
      trend: item.trend,
      competition: item.competition,
      intent: item.intent,
      serp_features: ['Featured Snippet', 'People Also Ask', 'Related Searches'],
      related_keywords: [`${searchTerm} tools`, `${searchTerm} tutorial`, `${searchTerm} guide`],
      questions: [`What is ${searchTerm}?`, `How to ${searchTerm}?`, `Best ${searchTerm} tools?`],
      saved: false,
      created_at: new Date().toISOString()
    }));
  }, []);

  // Save search history to database
  const saveSearchHistory = useCallback(async (term: string, resultCount: number) => {
    try {
      const { error } = await supabase
        .from('keyword_searches')
        .insert([
          {
            search_term: term,
            result_count: resultCount,
            created_at: new Date().toISOString()
          }
        ]);

      if (error) throw error;
    } catch (error) {
      console.error('Failed to save search history:', error);
    }
  }, []);

  const handleSearch = useCallback(async () => {
    if (!searchTerm.trim()) {
      showError(new Error('Please enter a search term'), 'Keyword search');
      return;
    }

    setIsSearching(true);
    try {
      // Simulate API call for keyword research
      await new Promise(resolve => setTimeout(resolve, 2000));

      const mockKeywords = await generateKeywordData(searchTerm);
      setKeywords(mockKeywords);
      setFilteredKeywords(mockKeywords);

      // Save search to database
      await saveSearchHistory(searchTerm, mockKeywords.length);

      showSuccess(`Found ${mockKeywords.length} keywords for "${searchTerm}"`);
    } catch (error) {
      showError(error, 'Keyword search');
    } finally {
      setIsSearching(false);
    }
  }, [searchTerm, generateKeywordData, saveSearchHistory, showError, showSuccess]);

  // Load saved keywords on component mount
  useEffect(() => {
    loadSavedKeywords();
  }, []);

  // Apply filters and sorting when keywords or filters change
  useEffect(() => {
    applyFiltersAndSort();
  }, [keywords, filters, sortBy, sortOrder]);

  const loadSavedKeywords = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('saved_keywords')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setSavedKeywords(data || []);
    } catch (error) {
      console.error('Failed to load saved keywords:', error);
    }
  }, []);

  const applyFiltersAndSort = useCallback(() => {
    let filtered = [...keywords];

    // Apply filters
    if (filters.competition.length > 0) {
      filtered = filtered.filter(kw => filters.competition.includes(kw.competition));
    }
    if (filters.intent.length > 0) {
      filtered = filtered.filter(kw => filters.intent.includes(kw.intent));
    }
    if (filters.trend.length > 0) {
      filtered = filtered.filter(kw => filters.trend.includes(kw.trend));
    }

    // Apply difficulty filter
    filtered = filtered.filter(kw =>
      kw.difficulty >= filters.minDifficulty && kw.difficulty <= filters.maxDifficulty
    );

    // Apply volume filter (convert string to number for comparison)
    filtered = filtered.filter(kw => {
      const volume = parseInt(kw.volume.replace(/,/g, ''));
      return volume >= filters.minVolume && volume <= filters.maxVolume;
    });

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'volume':
          aValue = parseInt(a.volume.replace(/,/g, ''));
          bValue = parseInt(b.volume.replace(/,/g, ''));
          break;
        case 'difficulty':
          aValue = a.difficulty;
          bValue = b.difficulty;
          break;
        case 'cpc':
          aValue = parseFloat(a.cpc.replace('$', ''));
          bValue = parseFloat(b.cpc.replace('$', ''));
          break;
        case 'keyword':
          aValue = a.keyword.toLowerCase();
          bValue = b.keyword.toLowerCase();
          break;
        default:
          aValue = a.keyword.toLowerCase();
          bValue = b.keyword.toLowerCase();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredKeywords(filtered);
  }, [keywords, filters, sortBy, sortOrder]);

  const saveKeyword = useCallback(async (keyword: Keyword) => {
    try {
      const { error } = await supabase
        .from('saved_keywords')
        .insert([{
          keyword: keyword.keyword,
          volume: keyword.volume,
          difficulty: keyword.difficulty,
          cpc: keyword.cpc,
          trend: keyword.trend,
          competition: keyword.competition,
          intent: keyword.intent,
          serp_features: keyword.serp_features,
          related_keywords: keyword.related_keywords,
          questions: keyword.questions
        }]);

      if (error) throw error;

      // Update local state
      setKeywords(prev => prev.map(kw =>
        kw.id === keyword.id ? { ...kw, saved: true } : kw
      ));

      await loadSavedKeywords();
      showSuccess(`Saved keyword: ${keyword.keyword}`);
    } catch (error) {
      showError(error, 'Save keyword');
    }
  }, [loadSavedKeywords, showError, showSuccess]);

  const exportKeywords = useCallback(async (keywordsToExport: Keyword[]) => {
    try {
      const csvContent = [
        ['Keyword', 'Volume', 'Difficulty', 'CPC', 'Competition', 'Intent', 'Trend'].join(','),
        ...keywordsToExport.map(kw => [
          kw.keyword,
          kw.volume,
          kw.difficulty,
          kw.cpc,
          kw.competition,
          kw.intent,
          kw.trend
        ].join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `keywords-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      showSuccess(`Exported ${keywordsToExport.length} keywords`);
    } catch (error) {
      showError(error, 'Export keywords');
    }
  }, [showError, showSuccess]);

  const copyToClipboard = useCallback(async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to clipboard",
        description: "Keyword copied successfully",
      });
    } catch (error) {
      showError(error, 'Copy to clipboard');
    }
  }, [toast, showError]);

  const trendingKeywords = [
    { keyword: 'ai web scraping', growth: '+125%' },
    { keyword: 'no-code scraping', growth: '+89%' },
    { keyword: 'real-time data extraction', growth: '+67%' },
    { keyword: 'cloud scraping service', growth: '+45%' }
  ];

  const getDifficultyColor = (difficulty: number) => {
    if (difficulty < 30) return 'bg-green-500';
    if (difficulty < 60) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const getDifficultyLabel = (difficulty: number) => {
    if (difficulty < 30) return 'Easy';
    if (difficulty < 60) return 'Medium';
    return 'Hard';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <TrendingUp className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">Keyword Research</h1>
      </div>

      <Tabs defaultValue="research" className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="research">Keyword Research</TabsTrigger>
          <TabsTrigger value="trending">Trending Keywords</TabsTrigger>
          <TabsTrigger value="analysis">Keyword Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="research" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>Keyword Discovery</span>
              </CardTitle>
              <CardDescription>
                Find profitable keywords for your content and SEO strategy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="keyword-search">Search Keywords</Label>
                <div className="flex space-x-2">
                  <Input
                    id="keyword-search"
                    placeholder="Enter seed keyword..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                  <Button onClick={handleSearch} disabled={isSearching || !searchTerm}>
                    <RefreshCw className={`h-4 w-4 mr-2 ${isSearching ? 'animate-spin' : ''}`} />
                    {isSearching ? 'Searching...' : 'Search'}
                  </Button>
                </div>
              </div>

              {isSearching && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                    <span className="text-sm text-gray-600">Finding keywords...</span>
                  </div>
                  <Progress value={66} className="w-full" />
                </div>
              )}

              {filteredKeywords.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      Keyword Results ({filteredKeywords.length})
                    </h3>
                    <div className="flex space-x-2">
                      <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger className="w-32">
                          <SelectValue placeholder="Sort by" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="volume">Volume</SelectItem>
                          <SelectItem value="difficulty">Difficulty</SelectItem>
                          <SelectItem value="cpc">CPC</SelectItem>
                          <SelectItem value="keyword">Keyword</SelectItem>
                        </SelectContent>
                      </Select>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                      >
                        {sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setShowFilters(!showFilters)}
                      >
                        <Filter className="h-4 w-4 mr-2" />
                        Filter
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => exportKeywords(selectedKeywords.length > 0
                          ? filteredKeywords.filter(kw => selectedKeywords.includes(kw.id))
                          : filteredKeywords
                        )}
                      >
                        <Download className="h-4 w-4 mr-2" />
                        Export
                      </Button>
                    </div>
                  </div>

                  {showFilters && (
                    <Card className="p-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Competition</Label>
                          <div className="space-y-1">
                            {['High', 'Medium', 'Low'].map(comp => (
                              <div key={comp} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`comp-${comp}`}
                                  checked={filters.competition.includes(comp)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setFilters(prev => ({
                                        ...prev,
                                        competition: [...prev.competition, comp]
                                      }));
                                    } else {
                                      setFilters(prev => ({
                                        ...prev,
                                        competition: prev.competition.filter(c => c !== comp)
                                      }));
                                    }
                                  }}
                                />
                                <Label htmlFor={`comp-${comp}`}>{comp}</Label>
                              </div>
                            ))}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Intent</Label>
                          <div className="space-y-1">
                            {['Commercial', 'Informational', 'Navigational', 'Transactional'].map(intent => (
                              <div key={intent} className="flex items-center space-x-2">
                                <Checkbox
                                  id={`intent-${intent}`}
                                  checked={filters.intent.includes(intent)}
                                  onCheckedChange={(checked) => {
                                    if (checked) {
                                      setFilters(prev => ({
                                        ...prev,
                                        intent: [...prev.intent, intent]
                                      }));
                                    } else {
                                      setFilters(prev => ({
                                        ...prev,
                                        intent: prev.intent.filter(i => i !== intent)
                                      }));
                                    }
                                  }}
                                />
                                <Label htmlFor={`intent-${intent}`}>{intent}</Label>
                              </div>
                            ))}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label>Difficulty Range</Label>
                          <div className="flex space-x-2">
                            <Input
                              type="number"
                              placeholder="Min"
                              value={filters.minDifficulty}
                              onChange={(e) => setFilters(prev => ({
                                ...prev,
                                minDifficulty: parseInt(e.target.value) || 0
                              }))}
                            />
                            <Input
                              type="number"
                              placeholder="Max"
                              value={filters.maxDifficulty}
                              onChange={(e) => setFilters(prev => ({
                                ...prev,
                                maxDifficulty: parseInt(e.target.value) || 100
                              }))}
                            />
                          </div>
                        </div>
                      </div>
                    </Card>
                  )}
                </div>
              )}

              {filteredKeywords.length > 0 && (
                <div className="space-y-3">
                  {filteredKeywords.map((item) => (
                    <Card key={item.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <Checkbox
                              checked={selectedKeywords.includes(item.id)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  setSelectedKeywords(prev => [...prev, item.id]);
                                } else {
                                  setSelectedKeywords(prev => prev.filter(id => id !== item.id));
                                }
                              }}
                            />
                            <div className="flex-1">
                              <div className="flex items-center space-x-2">
                                <h4 className="font-medium">{item.keyword}</h4>
                                <Badge variant="outline">{item.intent}</Badge>
                                {item.saved && (
                                  <Badge className="bg-green-100 text-green-800">
                                    <Star className="h-3 w-3 mr-1" />
                                    Saved
                                  </Badge>
                                )}
                              </div>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-2 text-sm text-gray-600">
                                <div>
                                  <span className="font-medium">Volume:</span> {item.volume}
                                </div>
                                <div>
                                  <span className="font-medium">CPC:</span> {item.cpc}
                                </div>
                                <div>
                                  <span className="font-medium">Competition:</span> {item.competition}
                                </div>
                                <div className="flex items-center space-x-1">
                                  <span className="font-medium">Trend:</span>
                                  {item.trend === 'up' && <ArrowUp className="h-3 w-3 text-green-500" />}
                                  {item.trend === 'down' && <ArrowDown className="h-3 w-3 text-red-500" />}
                                  {item.trend === 'stable' && <Minus className="h-3 w-3 text-gray-400" />}
                                </div>
                              </div>

                              {item.related_keywords && item.related_keywords.length > 0 && (
                                <div className="mt-2">
                                  <span className="text-xs font-medium text-gray-500">Related: </span>
                                  <span className="text-xs text-gray-600">
                                    {item.related_keywords.slice(0, 3).join(', ')}
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="text-center">
                              <div className="text-sm font-medium">Difficulty</div>
                              <Badge
                                className={`${getDifficultyColor(item.difficulty)} text-white`}
                              >
                                {getDifficultyLabel(item.difficulty)}
                              </Badge>
                            </div>
                            <div className="flex flex-col space-y-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => copyToClipboard(item.keyword)}
                              >
                                <Copy className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => saveKeyword(item)}
                                disabled={item.saved}
                              >
                                <Star className={`h-4 w-4 ${item.saved ? 'fill-yellow-400 text-yellow-400' : ''}`} />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Trending Keywords</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    // Refresh trending keywords
                    showSuccess('Trending keywords refreshed');
                  }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </CardTitle>
              <CardDescription>
                Discover rapidly growing keywords in your industry
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {trendingKeywords.map((item, index) => (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{item.keyword}</h4>
                          <Badge className="bg-green-100 text-green-800">
                            {item.growth}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">Growing rapidly in search volume</p>
                        <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                          <span>Search Volume: 15.2K</span>
                          <span>Competition: Medium</span>
                          <span>CPC: $2.45</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSearchTerm(item.keyword);
                            handleSearch();
                          }}
                        >
                          <Search className="h-4 w-4 mr-2" />
                          Research
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(item.keyword)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              <Card className="border-dashed">
                <CardContent className="p-6 text-center">
                  <TrendingUp className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <h3 className="font-medium text-gray-900 mb-1">Get More Trending Keywords</h3>
                  <p className="text-sm text-gray-600 mb-3">
                    Upgrade to access real-time trending keyword data
                  </p>
                  <Button variant="outline" size="sm">
                    Learn More
                  </Button>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analysis" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Search className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium">Total Keywords</span>
                </div>
                <div className="text-2xl font-bold mt-2">{keywords.length}</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +{keywords.length} this session
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-yellow-600" />
                  <span className="text-sm font-medium">Saved Keywords</span>
                </div>
                <div className="text-2xl font-bold mt-2">{savedKeywords.length}</div>
                <div className="flex items-center text-sm text-blue-600 mt-1">
                  <Plus className="h-3 w-3 mr-1" />
                  {keywords.filter(kw => kw.saved).length} new saves
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">Avg. Difficulty</span>
                </div>
                <div className="text-2xl font-bold mt-2">
                  {keywords.length > 0
                    ? Math.round(keywords.reduce((sum, kw) => sum + kw.difficulty, 0) / keywords.length)
                    : 0
                  }
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {keywords.filter(kw => kw.difficulty < 30).length} easy keywords
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium">High Volume</span>
                </div>
                <div className="text-2xl font-bold mt-2">
                  {keywords.filter(kw => parseInt(kw.volume.replace(/,/g, '')) > 10000).length}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  Keywords with 10K+ volume
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Keyword Distribution</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Commercial Intent</span>
                      <span>{keywords.filter(kw => kw.intent === 'Commercial').length}</span>
                    </div>
                    <Progress
                      value={keywords.length > 0 ? (keywords.filter(kw => kw.intent === 'Commercial').length / keywords.length) * 100 : 0}
                      className="h-2"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Informational Intent</span>
                      <span>{keywords.filter(kw => kw.intent === 'Informational').length}</span>
                    </div>
                    <Progress
                      value={keywords.length > 0 ? (keywords.filter(kw => kw.intent === 'Informational').length / keywords.length) * 100 : 0}
                      className="h-2"
                    />
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>High Competition</span>
                      <span>{keywords.filter(kw => kw.competition === 'High').length}</span>
                    </div>
                    <Progress
                      value={keywords.length > 0 ? (keywords.filter(kw => kw.competition === 'High').length / keywords.length) * 100 : 0}
                      className="h-2"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5" />
                  <span>Saved Keywords</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {savedKeywords.length > 0 ? (
                  <div className="space-y-2 max-h-64 overflow-y-auto">
                    {savedKeywords.slice(0, 10).map((keyword, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm font-medium">{keyword.keyword}</span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">
                            {keyword.volume}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => copyToClipboard(keyword.keyword)}
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    ))}
                    {savedKeywords.length > 10 && (
                      <p className="text-xs text-gray-500 text-center">
                        +{savedKeywords.length - 10} more keywords
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Star className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">No saved keywords yet</p>
                    <p className="text-xs text-gray-500">Save keywords from your research to see them here</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default KeywordResearch;
