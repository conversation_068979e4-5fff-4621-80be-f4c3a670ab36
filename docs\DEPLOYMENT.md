# Deployment Guide

This guide covers deployment strategies, CI/CD setup, and production configuration for the MarketCrawler Pro application.

## Overview

The application is a static React SPA that can be deployed to various platforms. It requires:
- Static file hosting
- Environment variable configuration
- Supabase backend setup
- Optional CDN for performance

## Deployment Platforms

### Vercel (Recommended)

Vercel provides excellent React support with automatic deployments.

#### Setup Steps:

1. **Connect Repository**:
   ```bash
   # Install Vercel CLI
   npm i -g vercel
   
   # Login and connect project
   vercel login
   vercel
   ```

2. **Configure Environment Variables**:
   - Go to Vercel Dashboard → Project → Settings → Environment Variables
   - Add all required environment variables from `.env.example`

3. **Build Settings**:
   ```json
   {
     "buildCommand": "npm run build",
     "outputDirectory": "dist",
     "installCommand": "npm ci"
   }
   ```

4. **Custom Domain** (Optional):
   - Add custom domain in Vercel Dashboard
   - Configure DNS records as instructed

#### Vercel Configuration File:

Create `vercel.json`:
```json
{
  "buildCommand": "npm run build",
  "outputDirectory": "dist",
  "framework": "vite",
  "rewrites": [
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ],
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

### Netlify

Netlify offers great static site hosting with form handling and edge functions.

#### Setup Steps:

1. **Connect Repository**:
   - Go to Netlify Dashboard
   - Click "New site from Git"
   - Connect your repository

2. **Build Settings**:
   ```
   Build command: npm run build
   Publish directory: dist
   ```

3. **Environment Variables**:
   - Go to Site Settings → Environment Variables
   - Add all required variables

#### Netlify Configuration File:

Create `netlify.toml`:
```toml
[build]
  command = "npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "18"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
```

### Docker Deployment

For containerized deployments using Docker.

#### Dockerfile:

```dockerfile
# Multi-stage build
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY . .

# Build application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=builder /app/dist /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
```

#### Nginx Configuration:

Create `nginx.conf`:
```nginx
events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Security headers
    add_header X-Frame-Options "DENY" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;
        
        # Handle client-side routing
        location / {
            try_files $uri $uri/ /index.html;
        }
        
        # Cache static assets
        location /assets/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
        
        # Security
        location ~ /\. {
            deny all;
        }
    }
}
```

#### Docker Commands:

```bash
# Build image
docker build -t marketcrawler-pro .

# Run container
docker run -d -p 80:80 --name marketcrawler marketcrawler-pro

# Run with environment variables
docker run -d -p 80:80 \
  -e VITE_SUPABASE_URL=your-url \
  -e VITE_SUPABASE_ANON_KEY=your-key \
  --name marketcrawler marketcrawler-pro
```

### AWS S3 + CloudFront

For AWS-based deployment with global CDN.

#### Setup Steps:

1. **Create S3 Bucket**:
   ```bash
   aws s3 mb s3://your-bucket-name
   aws s3 website s3://your-bucket-name --index-document index.html
   ```

2. **Upload Files**:
   ```bash
   npm run build
   aws s3 sync dist/ s3://your-bucket-name --delete
   ```

3. **Create CloudFront Distribution**:
   - Origin: S3 bucket
   - Default root object: index.html
   - Error pages: 404 → /index.html (for SPA routing)

4. **Configure Custom Domain** (Optional):
   - Add CNAME record pointing to CloudFront distribution
   - Add SSL certificate via AWS Certificate Manager

## CI/CD Pipeline

### GitHub Actions

Create `.github/workflows/deploy.yml`:

```yaml
name: Deploy to Production

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run linting
        run: npm run lint
      
      - name: Run tests
        run: npm run test:coverage
      
      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
      
      - name: Install Playwright
        run: npx playwright install
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload E2E test results
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/

  build:
    needs: test
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Build application
        run: npm run build
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
          VITE_SUPABASE_FUNCTIONS_URL: ${{ secrets.VITE_SUPABASE_FUNCTIONS_URL }}
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: dist
          path: dist/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Download build artifacts
        uses: actions/download-artifact@v3
        with:
          name: dist
          path: dist/
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'

  lighthouse:
    needs: deploy
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - uses: actions/checkout@v3
      
      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v9
        with:
          urls: |
            https://your-domain.com
          uploadArtifacts: true
          temporaryPublicStorage: true
```

### Environment Variables for CI/CD

Set these secrets in your repository settings:

```
VITE_SUPABASE_URL
VITE_SUPABASE_ANON_KEY
VITE_SUPABASE_FUNCTIONS_URL
VERCEL_TOKEN
VERCEL_ORG_ID
VERCEL_PROJECT_ID
CODECOV_TOKEN
```

## Production Configuration

### Environment Variables

Production environment variables:

```env
# Supabase (Production)
VITE_SUPABASE_URL=https://your-prod-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-production-anon-key
VITE_SUPABASE_FUNCTIONS_URL=https://your-prod-project.supabase.co/functions/v1

# Application
VITE_APP_NAME=MarketCrawler Pro
VITE_APP_VERSION=1.0.0

# Production Settings
VITE_DEV_MODE=false
VITE_LOG_LEVEL=error
```

### Performance Optimization

1. **Enable Compression**:
   - Gzip/Brotli compression on server
   - Pre-compressed assets if possible

2. **Caching Strategy**:
   ```
   /index.html: no-cache
   /assets/*: max-age=31536000, immutable
   /sw.js: max-age=0
   ```

3. **CDN Configuration**:
   - Global content delivery
   - Edge caching rules
   - Image optimization

4. **Monitoring**:
   - Core Web Vitals tracking
   - Error monitoring (Sentry)
   - Performance monitoring (New Relic)

### Security Configuration

1. **Security Headers**:
   ```
   Strict-Transport-Security: max-age=31536000; includeSubDomains
   X-Content-Type-Options: nosniff
   X-Frame-Options: DENY
   X-XSS-Protection: 1; mode=block
   Content-Security-Policy: default-src 'self'; ...
   ```

2. **HTTPS Enforcement**:
   - SSL/TLS certificate
   - HTTP to HTTPS redirects
   - HSTS headers

3. **Rate Limiting**:
   - API rate limiting
   - DDoS protection
   - Bot detection

## Monitoring and Maintenance

### Health Checks

Implement health check endpoints:

```typescript
// Health check for monitoring
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: process.env.VITE_APP_VERSION
  })
})
```

### Logging

Configure structured logging:

```typescript
// Production logging configuration
const logger = {
  level: process.env.VITE_LOG_LEVEL || 'info',
  format: 'json',
  transports: ['console', 'file']
}
```

### Backup Strategy

1. **Database Backups**:
   - Automated Supabase backups
   - Point-in-time recovery
   - Cross-region replication

2. **Asset Backups**:
   - Static file backups
   - Configuration backups
   - Environment variable backups

### Update Strategy

1. **Rolling Updates**:
   - Zero-downtime deployments
   - Blue-green deployment strategy
   - Canary releases for major updates

2. **Rollback Plan**:
   - Quick rollback capability
   - Database migration rollbacks
   - Feature flag toggles

## Troubleshooting

### Common Issues

1. **Build Failures**:
   - Check Node.js version compatibility
   - Verify environment variables
   - Review dependency conflicts

2. **Runtime Errors**:
   - Check browser console for errors
   - Verify API connectivity
   - Review network requests

3. **Performance Issues**:
   - Analyze bundle size
   - Check Core Web Vitals
   - Review caching configuration

### Support Resources

- **Documentation**: Comprehensive guides in docs/
- **Monitoring**: Application performance monitoring
- **Logs**: Centralized logging and error tracking
- **Support**: Contact support team for critical issues
