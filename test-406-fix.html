<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test 406 Fix - Supabase User Settings</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #45a049;
        }
        .button.danger {
            background: #f44336;
        }
        .button.danger:hover {
            background: #da190b;
        }
        .result {
            background: #f9f9f9;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        .auth-section {
            border: 2px solid #007bff;
            background: #e7f3ff;
        }
        .test-section {
            border: 2px solid #28a745;
            background: #e8f5e9;
        }
        .status {
            font-weight: bold;
            padding: 5px 10px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px 0;
        }
        .status.connected {
            background: #d4edda;
            color: #155724;
        }
        .status.disconnected {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>🔧 Test 406 Fix - Supabase User Settings</h1>
    
    <div class="container auth-section">
        <h2>🔐 Authentication Status</h2>
        <div id="authStatus" class="status disconnected">Not Connected</div>
        <div>
            <button class="button" onclick="testConnection()">Test Connection</button>
            <button class="button" onclick="signInAnonymously()">Sign In Anonymously</button>
            <button class="button danger" onclick="signOut()">Sign Out</button>
        </div>
        <div id="authResult" class="result" style="display: none;"></div>
    </div>

    <div class="container test-section">
        <h2>🧪 User Settings Tests</h2>
        <div>
            <button class="button" onclick="testUserSettings()">Test User Settings Query</button>
            <button class="button" onclick="createTestSettings()">Create Test Settings</button>
            <button class="button" onclick="testAllTables()">Test All Settings Tables</button>
            <button class="button" onclick="testRLSPolicies()">Test RLS Policies</button>
        </div>
        <div id="testResult" class="result" style="display: none;"></div>
    </div>

    <div class="container">
        <h2>📊 Database Status</h2>
        <div>
            <button class="button" onclick="checkTableStructure()">Check Table Structure</button>
            <button class="button" onclick="checkUserProfile()">Check User Profile</button>
            <button class="button" onclick="clearResults()">Clear Results</button>
        </div>
        <div id="dbResult" class="result" style="display: none;"></div>
    </div>

    <script type="module">
        // Import Supabase
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Initialize Supabase client
        const supabaseUrl = 'https://rclikclltlyzyojjttqv.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTYzOTIsImV4cCI6MjA2NTk3MjM5Mn0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw';
        
        const supabase = createClient(supabaseUrl, supabaseKey);

        // Global functions
        window.testConnection = async function() {
            const authStatus = document.getElementById('authStatus');
            const authResult = document.getElementById('authResult');
            
            try {
                authResult.style.display = 'block';
                authResult.textContent = 'Testing connection...';
                authResult.className = 'result';
                
                const { data, error } = await supabase.auth.getSession();
                
                if (error) {
                    throw error;
                }
                
                authStatus.textContent = data.session ? 'Connected & Authenticated' : 'Connected (Not Authenticated)';
                authStatus.className = data.session ? 'status connected' : 'status disconnected';
                
                authResult.textContent = `Connection successful!\nSession: ${data.session ? 'Active' : 'None'}\nUser: ${data.session?.user?.email || 'None'}`;
                authResult.className = 'result success';
                
            } catch (error) {
                authStatus.textContent = 'Connection Failed';
                authStatus.className = 'status disconnected';
                authResult.textContent = `Connection failed: ${error.message}`;
                authResult.className = 'result error';
            }
        };

        window.signInAnonymously = async function() {
            const authResult = document.getElementById('authResult');
            
            try {
                authResult.style.display = 'block';
                authResult.textContent = 'Signing in anonymously...';
                authResult.className = 'result';
                
                const { data, error } = await supabase.auth.signInAnonymously();
                
                if (error) {
                    throw error;
                }
                
                document.getElementById('authStatus').textContent = 'Connected & Authenticated (Anonymous)';
                document.getElementById('authStatus').className = 'status connected';
                
                authResult.textContent = `Anonymous sign-in successful!\nUser ID: ${data.user.id}`;
                authResult.className = 'result success';
                
            } catch (error) {
                authResult.textContent = `Anonymous sign-in failed: ${error.message}`;
                authResult.className = 'result error';
            }
        };

        window.signOut = async function() {
            const authResult = document.getElementById('authResult');
            
            try {
                authResult.style.display = 'block';
                authResult.textContent = 'Signing out...';
                authResult.className = 'result';
                
                const { error } = await supabase.auth.signOut();
                
                if (error) {
                    throw error;
                }
                
                document.getElementById('authStatus').textContent = 'Not Connected';
                document.getElementById('authStatus').className = 'status disconnected';
                
                authResult.textContent = 'Signed out successfully!';
                authResult.className = 'result success';
                
            } catch (error) {
                authResult.textContent = `Sign out failed: ${error.message}`;
                authResult.className = 'result error';
            }
        };

        window.testUserSettings = async function() {
            const testResult = document.getElementById('testResult');
            
            try {
                testResult.style.display = 'block';
                testResult.textContent = 'Testing user_settings query...';
                testResult.className = 'result';
                
                // Get current user
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                
                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }
                
                // Test the exact query that was failing
                const { data, error } = await supabase
                    .from('user_settings')
                    .select('*')
                    .eq('user_id', user.id);
                
                if (error) {
                    throw error;
                }
                
                testResult.textContent = `✅ User settings query successful!\n\nUser ID: ${user.id}\nSettings found: ${data.length}\n\nData:\n${JSON.stringify(data, null, 2)}`;
                testResult.className = 'result success';
                
            } catch (error) {
                testResult.textContent = `❌ User settings query failed:\n\nError: ${error.message}\nCode: ${error.code || 'N/A'}\nDetails: ${error.details || 'N/A'}`;
                testResult.className = 'result error';
            }
        };

        window.createTestSettings = async function() {
            const testResult = document.getElementById('testResult');
            
            try {
                testResult.style.display = 'block';
                testResult.textContent = 'Creating test settings...';
                testResult.className = 'result';
                
                // Get current user
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                
                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }
                
                // Create test settings
                const { data, error } = await supabase
                    .from('user_settings')
                    .upsert([{
                        user_id: user.id,
                        theme: 'dark',
                        language: 'en',
                        timezone: 'America/New_York',
                        notifications: {
                            email: true,
                            push: false,
                            marketing: true
                        },
                        privacy: {
                            analytics: true,
                            data_collection: true
                        }
                    }])
                    .select();
                
                if (error) {
                    throw error;
                }
                
                testResult.textContent = `✅ Test settings created successfully!\n\nData:\n${JSON.stringify(data, null, 2)}`;
                testResult.className = 'result success';
                
            } catch (error) {
                testResult.textContent = `❌ Create test settings failed:\n\nError: ${error.message}\nCode: ${error.code || 'N/A'}`;
                testResult.className = 'result error';
            }
        };

        window.testAllTables = async function() {
            const testResult = document.getElementById('testResult');
            
            try {
                testResult.style.display = 'block';
                testResult.textContent = 'Testing all settings tables...';
                testResult.className = 'result';
                
                // Get current user
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                
                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }
                
                const tables = ['user_settings', 'notification_settings', 'privacy_settings', 'security_settings'];
                const results = {};
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('*')
                            .eq('user_id', user.id);
                        
                        if (error) {
                            results[table] = `❌ Error: ${error.message}`;
                        } else {
                            results[table] = `✅ Success: ${data.length} records`;
                        }
                    } catch (err) {
                        results[table] = `❌ Exception: ${err.message}`;
                    }
                }
                
                let resultText = `All tables test results:\n\n`;
                for (const [table, result] of Object.entries(results)) {
                    resultText += `${table}: ${result}\n`;
                }
                
                testResult.textContent = resultText;
                testResult.className = 'result success';
                
            } catch (error) {
                testResult.textContent = `❌ Test all tables failed:\n\nError: ${error.message}`;
                testResult.className = 'result error';
            }
        };

        window.testRLSPolicies = async function() {
            const testResult = document.getElementById('testResult');
            
            try {
                testResult.style.display = 'block';
                testResult.textContent = 'Testing RLS policies...';
                testResult.className = 'result';
                
                // Test without authentication first
                await supabase.auth.signOut();
                
                const { data: unauthData, error: unauthError } = await supabase
                    .from('user_settings')
                    .select('*')
                    .limit(1);
                
                let resultText = 'RLS Policy Test Results:\n\n';
                
                if (unauthError) {
                    resultText += `✅ Unauthenticated access blocked: ${unauthError.message}\n\n`;
                } else {
                    resultText += `❌ Unauthenticated access allowed (security issue!)\n\n`;
                }
                
                // Sign in and test authenticated access
                const { data: authData, error: authError } = await supabase.auth.signInAnonymously();
                
                if (authError) {
                    throw authError;
                }
                
                const { data: authUserData, error: authUserError } = await supabase
                    .from('user_settings')
                    .select('*')
                    .eq('user_id', authData.user.id);
                
                if (authUserError) {
                    resultText += `❌ Authenticated access failed: ${authUserError.message}`;
                } else {
                    resultText += `✅ Authenticated access successful: ${authUserData.length} records`;
                }
                
                testResult.textContent = resultText;
                testResult.className = 'result success';
                
            } catch (error) {
                testResult.textContent = `❌ RLS policy test failed:\n\nError: ${error.message}`;
                testResult.className = 'result error';
            }
        };

        window.checkTableStructure = async function() {
            const dbResult = document.getElementById('dbResult');
            
            try {
                dbResult.style.display = 'block';
                dbResult.textContent = 'Checking table structure...';
                dbResult.className = 'result';
                
                // This is a simple check - in a real scenario you'd need a custom function
                const tables = ['user_settings', 'notification_settings', 'privacy_settings', 'security_settings', 'user_profiles'];
                let resultText = 'Table Structure Check:\n\n';
                
                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('*')
                            .limit(0);
                        
                        if (error) {
                            resultText += `❌ ${table}: ${error.message}\n`;
                        } else {
                            resultText += `✅ ${table}: Table exists and accessible\n`;
                        }
                    } catch (err) {
                        resultText += `❌ ${table}: ${err.message}\n`;
                    }
                }
                
                dbResult.textContent = resultText;
                dbResult.className = 'result success';
                
            } catch (error) {
                dbResult.textContent = `❌ Table structure check failed:\n\nError: ${error.message}`;
                dbResult.className = 'result error';
            }
        };

        window.checkUserProfile = async function() {
            const dbResult = document.getElementById('dbResult');
            
            try {
                dbResult.style.display = 'block';
                dbResult.textContent = 'Checking user profile...';
                dbResult.className = 'result';
                
                // Get current user
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                
                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }
                
                // Check user profile
                const { data, error } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('id', user.id);
                
                if (error) {
                    throw error;
                }
                
                dbResult.textContent = `User Profile Check:\n\nUser ID: ${user.id}\nProfile found: ${data.length > 0 ? 'Yes' : 'No'}\n\nProfile Data:\n${JSON.stringify(data, null, 2)}`;
                dbResult.className = 'result success';
                
            } catch (error) {
                dbResult.textContent = `❌ User profile check failed:\n\nError: ${error.message}`;
                dbResult.className = 'result error';
            }
        };

        window.clearResults = function() {
            document.getElementById('authResult').style.display = 'none';
            document.getElementById('testResult').style.display = 'none';
            document.getElementById('dbResult').style.display = 'none';
        };

        // Auto-test connection on load
        window.addEventListener('load', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
