import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/lib/supabase';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { KeywordData, LeadData } from '@/types';
import {
  Search,
  Mail,
  Share2,
  Users,
  Download,
  Copy,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  TrendingUp
} from 'lucide-react';

const MarketingTools: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'keywords' | 'leads' | 'content' | 'social'>('keywords');
  const [loading, setLoading] = useState<boolean>(false);
  const [keywords, setKeywords] = useState<KeywordData[]>([]);
  const [leads, setLeads] = useState<LeadData[]>([]);
  const [contentIdeas, setContentIdeas] = useState<string[]>([]);
  const [socialPosts, setSocialPosts] = useState<string[]>([]);
  const [generatedContent, setGeneratedContent] = useState<string>('');
  const { showError, showSuccess } = useErrorHandler();

  const extractKeywords = async () => {
    setLoading(true);
    try {
      const { data: scrapedData, error } = await supabase
        .from('scraped_data')
        .select('title, content, metadata')
        .limit(50);

      if (error) throw new Error(`Failed to fetch data: ${error.message}`);

      if (!scrapedData || scrapedData.length === 0) {
        throw new Error('No scraped data available. Please scrape some websites first.');
      }

      // Simple keyword extraction logic
      const keywordMap = new Map<string, { frequency: number; contexts: string[] }>();

      scrapedData.forEach(item => {
        const text = `${item.title || ''} ${item.content || ''}`.toLowerCase();
        const words = text.match(/\b[a-z]{3,}\b/g) || [];

        words.forEach(word => {
          if (!['the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use'].includes(word)) {
            const existing = keywordMap.get(word) || { frequency: 0, contexts: [] };
            existing.frequency++;
            if (existing.contexts.length < 3) {
              existing.contexts.push(item.title || 'Untitled');
            }
            keywordMap.set(word, existing);
          }
        });
      });

      const extractedKeywords = Array.from(keywordMap.entries())
        .filter(([_, data]) => data.frequency >= 2)
        .sort((a, b) => b[1].frequency - a[1].frequency)
        .slice(0, 20)
        .map(([keyword, data]) => ({
          keyword,
          frequency: data.frequency,
          context: data.contexts[0] || 'Unknown'
        }));

      setKeywords(extractedKeywords);
      showSuccess(`Extracted ${extractedKeywords.length} keywords from ${scrapedData.length} pages`);
    } catch (error) {
      showError(error, 'Keyword extraction');
    } finally {
      setLoading(false);
    }
  };

  const extractLeads = async () => {
    setLoading(true);
    try {
      const { data: scrapedData, error } = await supabase
        .from('scraped_data')
        .select('url, title, content')
        .limit(50);

      if (error) throw new Error(`Failed to fetch data: ${error.message}`);

      if (!scrapedData || scrapedData.length === 0) {
        throw new Error('No scraped data available. Please scrape some websites first.');
      }

      // Simple lead extraction logic
      const emailRegex = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
      const extractedLeads: LeadData[] = [];

      scrapedData.forEach(item => {
        const content = item.content || '';
        const emails = content.match(emailRegex) || [];

        emails.forEach(email => {
          if (!extractedLeads.some(lead => lead.email === email)) {
            extractedLeads.push({
              email,
              url: item.url,
              title: item.title || 'Untitled',
              name: extractNameFromContent(content, email),
              company: extractCompanyFromUrl(item.url)
            });
          }
        });
      });

      setLeads(extractedLeads.slice(0, 50));
      showSuccess(`Found ${extractedLeads.length} potential leads`);
    } catch (error) {
      showError(error, 'Lead extraction');
    } finally {
      setLoading(false);
    }
  };

  const generateContentIdeas = async () => {
    setLoading(true);
    try {
      const { data: scrapedData, error } = await supabase
        .from('scraped_data')
        .select('title, content')
        .limit(20);

      if (error) throw new Error(`Failed to fetch data: ${error.message}`);

      if (!scrapedData || scrapedData.length === 0) {
        throw new Error('No scraped data available. Please scrape some websites first.');
      }

      // Generate content ideas based on scraped titles and content
      const ideas = [
        ...scrapedData.map(item => `How to: ${item.title}`),
        ...scrapedData.map(item => `Top 10 Tips for ${extractMainTopic(item.title || '')}`),
        ...scrapedData.map(item => `Complete Guide to ${extractMainTopic(item.title || '')}`),
        ...scrapedData.map(item => `${extractMainTopic(item.title || '')} Best Practices`),
        ...scrapedData.map(item => `Common Mistakes in ${extractMainTopic(item.title || '')}`)
      ].filter(idea => idea.length > 10 && !idea.includes('undefined'))
       .slice(0, 15);

      setContentIdeas(ideas);
      showSuccess(`Generated ${ideas.length} content ideas`);
    } catch (error) {
      showError(error, 'Content idea generation');
    } finally {
      setLoading(false);
    }
  };

  const generateSocialPosts = async () => {
    setLoading(true);
    try {
      const { data: scrapedData, error } = await supabase
        .from('scraped_data')
        .select('title, url')
        .limit(10);

      if (error) throw new Error(`Failed to fetch data: ${error.message}`);

      if (!scrapedData || scrapedData.length === 0) {
        throw new Error('No scraped data available. Please scrape some websites first.');
      }

      const posts = scrapedData.map(item => {
        const templates = [
          `🚀 Just discovered: ${item.title} ${item.url} #innovation #insights`,
          `💡 Interesting read: "${item.title}" - What are your thoughts? ${item.url}`,
          `📖 Worth checking out: ${item.title} ${item.url} #learning #growth`,
          `🔍 Found this valuable resource: ${item.title} ${item.url}`,
          `✨ Great insights in: "${item.title}" ${item.url} #knowledge #sharing`
        ];
        return templates[Math.floor(Math.random() * templates.length)];
      });

      setSocialPosts(posts);
      showSuccess(`Generated ${posts.length} social media posts`);
    } catch (error) {
      showError(error, 'Social post generation');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const extractNameFromContent = (content: string, email: string): string => {
    const emailPrefix = email.split('@')[0];
    return emailPrefix.replace(/[._]/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const extractCompanyFromUrl = (url: string): string => {
    try {
      const domain = new URL(url).hostname.replace('www.', '');
      return domain.split('.')[0].replace(/\b\w/g, l => l.toUpperCase());
    } catch {
      return 'Unknown';
    }
  };

  const extractMainTopic = (title: string): string => {
    const words = title.split(' ').filter(word => word.length > 3);
    return words.slice(0, 2).join(' ') || 'Topic';
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      showSuccess('Copied to clipboard');
    } catch {
      showError(new Error('Failed to copy to clipboard'));
    }
  };

  const downloadAsCSV = (data: any[], filename: string) => {
    try {
      const headers = Object.keys(data[0] || {});
      const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
      ].join('\n');

      const blob = new Blob([csvContent], { type: 'text/csv' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      a.click();
      URL.revokeObjectURL(url);
      showSuccess(`Downloaded ${filename}`);
    } catch (error) {
      showError(error, 'File download');
    }
  };

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="flex flex-wrap gap-2 p-1 bg-muted rounded-lg">
        {[
          { id: 'keywords', label: 'SEO Keywords', icon: Search },
          { id: 'leads', label: 'Lead Generation', icon: Users },
          { id: 'content', label: 'Content Ideas', icon: TrendingUp },
          { id: 'social', label: 'Social Posts', icon: Share2 }
        ].map(tab => {
          const Icon = tab.icon;
          return (
            <Button
              key={tab.id}
              variant={activeTab === tab.id ? 'default' : 'ghost'}
              onClick={() => setActiveTab(tab.id as any)}
              className="flex-1 min-w-0"
            >
              <Icon className="h-4 w-4 mr-2" />
              {tab.label}
            </Button>
          );
        })}
      </div>

      {/* SEO Keywords Tab */}
      {activeTab === 'keywords' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Search className="h-5 w-5" />
                <span>SEO Keyword Research</span>
              </div>
              <Button onClick={extractKeywords} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Extract Keywords
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {keywords.length > 0 ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    Found {keywords.length} keywords from scraped content
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadAsCSV(keywords, 'keywords.csv')}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download CSV
                  </Button>
                </div>
                <div className="grid gap-2 max-h-96 overflow-y-auto">
                  {keywords.map((keyword, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <span className="font-medium">{keyword.keyword}</span>
                        <p className="text-sm text-muted-foreground">From: {keyword.context}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="secondary">{keyword.frequency}x</Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(keyword.keyword)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Search className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Click "Extract Keywords" to analyze your scraped content for SEO keywords
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Lead Generation Tab */}
      {activeTab === 'leads' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Lead Generation</span>
              </div>
              <Button onClick={extractLeads} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Extract Leads
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {leads.length > 0 ? (
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <p className="text-sm text-muted-foreground">
                    Found {leads.length} potential leads
                  </p>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => downloadAsCSV(leads, 'leads.csv')}
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download CSV
                  </Button>
                </div>
                <div className="grid gap-2 max-h-96 overflow-y-auto">
                  {leads.map((lead, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{lead.email}</p>
                          <p className="text-sm text-muted-foreground">
                            {lead.name} • {lead.company}
                          </p>
                          <p className="text-xs text-muted-foreground">From: {lead.title}</p>
                        </div>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(lead.email || '')}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Users className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Click "Extract Leads" to find email addresses and contact information from scraped pages
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Content Ideas Tab */}
      {activeTab === 'content' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Content Ideas</span>
              </div>
              <Button onClick={generateContentIdeas} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Generate Ideas
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {contentIdeas.length > 0 ? (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Generated {contentIdeas.length} content ideas based on scraped data
                </p>
                <div className="grid gap-2 max-h-96 overflow-y-auto">
                  {contentIdeas.map((idea, index) => (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <span className="flex-1">{idea}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(idea)}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Click "Generate Ideas" to create content ideas based on your scraped data
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Social Posts Tab */}
      {activeTab === 'social' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Share2 className="h-5 w-5" />
                <span>Social Media Posts</span>
              </div>
              <Button onClick={generateSocialPosts} disabled={loading}>
                <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                Generate Posts
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {socialPosts.length > 0 ? (
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Generated {socialPosts.length} social media posts
                </p>
                <div className="grid gap-4 max-h-96 overflow-y-auto">
                  {socialPosts.map((post, index) => (
                    <div key={index} className="p-4 border rounded-lg bg-muted/50">
                      <div className="flex justify-between items-start">
                        <p className="flex-1 whitespace-pre-wrap">{post}</p>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => copyToClipboard(post)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              <div className="text-center py-8">
                <Share2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground">
                  Click "Generate Posts" to create social media content from your scraped data
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default MarketingTools;