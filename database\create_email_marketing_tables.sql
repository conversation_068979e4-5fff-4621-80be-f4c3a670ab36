-- Email Marketing Tables Creation Script
-- Run this in your Supabase SQL Editor to create the missing email marketing tables
-- This will fix the 404 errors you're seeing in the EmailMarketing component

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Contact Lists Table
-- Stores email contact lists for organizing contacts
CREATE TABLE IF NOT EXISTS contact_lists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    contact_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_contact_count CHECK (contact_count >= 0)
);

-- Email Contacts Table
-- Stores email contacts for marketing campaigns
CREATE TABLE IF NOT EXISTS email_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    first_name TEX<PERSON>,
    last_name TEX<PERSON>,
    status TEXT NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Unsubscribed', 'Bounced')),
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT unique_email UNIQUE (email)
);

-- Email Templates Table
-- Stores reusable email templates
CREATE TABLE IF NOT EXISTS email_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    subject TEXT NOT NULL,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_usage_count CHECK (usage_count >= 0)
);

-- Email Campaigns Table
-- Stores email marketing campaigns
CREATE TABLE IF NOT EXISTS email_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Draft', 'Scheduled', 'Active', 'Completed', 'Paused')),
    sent INTEGER DEFAULT 0,
    opens INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    open_rate DECIMAL(5,2) DEFAULT 0.0,
    click_rate DECIMAL(5,2) DEFAULT 0.0,
    recipient_count INTEGER DEFAULT 0,
    template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_sent CHECK (sent >= 0),
    CONSTRAINT valid_opens CHECK (opens >= 0),
    CONSTRAINT valid_clicks CHECK (clicks >= 0),
    CONSTRAINT valid_open_rate CHECK (open_rate >= 0.0 AND open_rate <= 100.0),
    CONSTRAINT valid_click_rate CHECK (click_rate >= 0.0 AND click_rate <= 100.0),
    CONSTRAINT valid_recipient_count CHECK (recipient_count >= 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_email_contacts_email ON email_contacts(email);
CREATE INDEX IF NOT EXISTS idx_email_contacts_status ON email_contacts(status);
CREATE INDEX IF NOT EXISTS idx_email_contacts_created_at ON email_contacts(created_at);
CREATE INDEX IF NOT EXISTS idx_email_templates_category ON email_templates(category);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_status ON email_campaigns(status);
CREATE INDEX IF NOT EXISTS idx_email_campaigns_created_at ON email_campaigns(created_at);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE contact_lists ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_campaigns ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for development (you may want to restrict these later)
CREATE POLICY "Allow all operations on contact_lists" ON contact_lists FOR ALL USING (true);
CREATE POLICY "Allow all operations on email_contacts" ON email_contacts FOR ALL USING (true);
CREATE POLICY "Allow all operations on email_templates" ON email_templates FOR ALL USING (true);
CREATE POLICY "Allow all operations on email_campaigns" ON email_campaigns FOR ALL USING (true);

-- Insert some sample data for testing
INSERT INTO email_templates (name, category, description, content, subject, usage_count) VALUES
('Welcome Email', 'Onboarding', 'Perfect for new subscribers', '<h1>Welcome!</h1><p>Thank you for joining us...</p>', 'Welcome to {{company_name}}!', 15),
('Product Announcement', 'Marketing', 'Showcase new products or features', '<h1>New Product Launch</h1><p>We are excited to announce...</p>', 'Introducing {{product_name}}', 8),
('Newsletter Template', 'Newsletter', 'Monthly newsletter template', '<h1>Monthly Update</h1><p>Here''s what''s new this month...</p>', 'Monthly Newsletter - {{month}} {{year}}', 25)
ON CONFLICT DO NOTHING;

INSERT INTO contact_lists (name, description, contact_count) VALUES
('Newsletter Subscribers', 'Main newsletter subscriber list', 0),
('Product Updates', 'Users interested in product announcements', 0),
('VIP Customers', 'Premium customers and early adopters', 0)
ON CONFLICT DO NOTHING;

-- Verify tables were created successfully
SELECT 'Tables created successfully!' as status;
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('contact_lists', 'email_contacts', 'email_templates', 'email_campaigns')
ORDER BY table_name;
