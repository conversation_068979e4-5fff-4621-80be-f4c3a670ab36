// Authentication utilities and user management

import { supabase } from './supabase'
import { securityAudit } from './security'
import { accountSecurity } from './accountSecurity'
import { twoFactorAuth } from './twoFactorAuth'

export interface User {
  id: string
  email: string
  name?: string
  avatar_url?: string
  role: 'user' | 'admin' | 'premium'
  created_at: string
  last_sign_in_at?: string
  email_confirmed_at?: string
  subscription_status?: 'free' | 'premium' | 'enterprise'
  usage_limits: {
    daily_scrapes: number
    monthly_scrapes: number
    max_urls_per_batch: number
    data_retention_days: number
  }
}

export interface AuthState {
  user: User | null
  session: any | null
  loading: boolean
  error: string | null
}

export interface SignUpData {
  email: string
  password: string
  name?: string
}

export interface SignInData {
  email: string
  password: string
}

export interface ResetPasswordData {
  email: string
}

export interface UpdatePasswordData {
  password: string
  confirmPassword: string
}

export interface PasswordValidationResult {
  isValid: boolean
  score: number
  errors: string[]
  warnings: string[]
}

export interface VerifyEmailData {
  token: string
  type: 'signup' | 'email_change' | 'recovery'
}

export interface UpdateProfileData {
  name?: string
  avatar_url?: string
}

/**
 * Authentication service class
 */
export class AuthService {
  private static instance: AuthService
  private authStateListeners: ((state: AuthState) => void)[] = []

  static getInstance(): AuthService {
    if (!AuthService.instance) {
      AuthService.instance = new AuthService()
    }
    return AuthService.instance
  }

  /**
   * Validate password strength and security requirements
   */
  validatePassword(password: string): PasswordValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    let score = 0

    // Length requirements
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    } else if (password.length >= 12) {
      score += 25
    } else {
      score += 15
      warnings.push('Consider using 12+ characters for better security')
    }

    // Character type requirements
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    } else {
      score += 15
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    } else {
      score += 15
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    } else {
      score += 15
    }

    if (!/[^a-zA-Z0-9]/.test(password)) {
      errors.push('Password must contain at least one special character')
    } else {
      score += 15
    }

    // Security checks
    if (/(.)\1{2,}/.test(password)) {
      warnings.push('Avoid repeating characters')
      score -= 10
    }

    if (/123|abc|qwe|password|admin|user|login/i.test(password)) {
      errors.push('Password contains common patterns or words')
      score -= 15
    }

    // Bonus for complexity
    if (password.length >= 12 && /[a-z]/.test(password) && /[A-Z]/.test(password) &&
        /\d/.test(password) && /[^a-zA-Z0-9]/.test(password)) {
      score += 15
    }

    score = Math.max(0, Math.min(100, score))
    const isValid = errors.length === 0 && score >= 60

    return {
      isValid,
      score,
      errors,
      warnings
    }
  }

  /**
   * Sign up a new user
   */
  async signUp(data: SignUpData): Promise<{ user: User | null; error: string | null }> {
    try {
      // Validate password strength
      const passwordValidation = this.validatePassword(data.password)
      if (!passwordValidation.isValid) {
        return { user: null, error: passwordValidation.errors.join('. ') }
      }

      securityAudit.log('User signup attempt', { email: data.email }, 'low')

      const { data: authData, error } = await supabase.auth.signUp({
        email: data.email,
        password: data.password,
        options: {
          data: {
            name: data.name || data.email.split('@')[0]
          }
        }
      })

      if (error) {
        securityAudit.log('User signup failed', { email: data.email, error: error.message }, 'medium')
        return { user: null, error: error.message }
      }

      if (authData.user) {
        // Create user profile
        const userProfile = await this.createUserProfile(authData.user)
        securityAudit.log('User signup successful', { userId: authData.user.id }, 'low')
        return { user: userProfile, error: null }
      }

      return { user: null, error: 'Failed to create user' }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('User signup error', { error: errorMessage }, 'high')
      return { user: null, error: errorMessage }
    }
  }

  /**
   * Sign in an existing user
   */
  async signIn(data: SignInData): Promise<{ user: User | null; error: string | null }> {
    try {
      // Check if account is locked
      const isLocked = await accountSecurity.isAccountLocked(data.email)
      if (isLocked) {
        const lockoutInfo = await accountSecurity.getAccountLockoutInfo(data.email)
        const lockedUntil = lockoutInfo ? new Date(lockoutInfo.locked_until).toLocaleString() : 'unknown'
        return {
          user: null,
          error: `Account is temporarily locked due to too many failed login attempts. Please try again after ${lockedUntil}.`
        }
      }

      securityAudit.log('User signin attempt', { email: data.email }, 'low')

      const { data: authData, error } = await supabase.auth.signInWithPassword({
        email: data.email,
        password: data.password
      })

      if (error) {
        // Record failed login attempt
        await accountSecurity.recordLoginAttempt(
          data.email,
          false,
          'unknown', // In a real app, you'd get the actual IP
          navigator.userAgent,
          error.message
        )
        securityAudit.log('User signin failed', { email: data.email, error: error.message }, 'medium')
        return { user: null, error: error.message }
      }

      if (authData.user) {
        // Record successful login attempt
        await accountSecurity.recordLoginAttempt(
          data.email,
          true,
          'unknown', // In a real app, you'd get the actual IP
          navigator.userAgent
        )
        const userProfile = await this.getUserProfile(authData.user.id)
        securityAudit.log('User signin successful', { userId: authData.user.id }, 'low')
        return { user: userProfile, error: null }
      }

      return { user: null, error: 'Failed to sign in' }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('User signin error', { error: errorMessage }, 'high')
      return { user: null, error: errorMessage }
    }
  }

  /**
   * Sign out the current user
   */
  async signOut(): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase.auth.signOut()
      
      if (error) {
        securityAudit.log('User signout failed', { error: error.message }, 'medium')
        return { error: error.message }
      }

      securityAudit.log('User signout successful', {}, 'low')
      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('User signout error', { error: errorMessage }, 'medium')
      return { error: errorMessage }
    }
  }

  /**
   * Reset user password
   */
  async resetPassword(data: ResetPasswordData): Promise<{ error: string | null }> {
    try {
      securityAudit.log('Password reset attempt', { email: data.email }, 'low')

      const { error } = await supabase.auth.resetPasswordForEmail(data.email, {
        redirectTo: `${window.location.origin}/reset-password`
      })

      if (error) {
        securityAudit.log('Password reset failed', { email: data.email, error: error.message }, 'medium')
        return { error: error.message }
      }

      securityAudit.log('Password reset email sent', { email: data.email }, 'low')
      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('Password reset error', { error: errorMessage }, 'medium')
      return { error: errorMessage }
    }
  }

  /**
   * Resend email verification
   */
  async resendEmailVerification(email: string): Promise<{ error: string | null }> {
    try {
      securityAudit.log('Email verification resend attempt', { email }, 'low')

      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/verify-email`
        }
      })

      if (error) {
        securityAudit.log('Email verification resend failed', { email, error: error.message }, 'medium')
        return { error: error.message }
      }

      securityAudit.log('Email verification resent', { email }, 'low')
      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('Email verification resend error', { error: errorMessage }, 'medium')
      return { error: errorMessage }
    }
  }

  /**
   * Verify email with token
   */
  async verifyEmail(data: VerifyEmailData): Promise<{ error: string | null }> {
    try {
      securityAudit.log('Email verification attempt', { type: data.type }, 'low')

      const { error } = await supabase.auth.verifyOtp({
        token_hash: data.token,
        type: data.type
      })

      if (error) {
        securityAudit.log('Email verification failed', { error: error.message }, 'medium')
        return { error: error.message }
      }

      securityAudit.log('Email verification successful', { type: data.type }, 'low')
      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('Email verification error', { error: errorMessage }, 'medium')
      return { error: errorMessage }
    }
  }

  /**
   * Check if user has 2FA enabled
   */
  async isTwoFactorEnabled(userId: string): Promise<boolean> {
    return await twoFactorAuth.isTwoFactorEnabled(userId)
  }

  /**
   * Setup 2FA for user
   */
  async setupTwoFactor(userId: string, email: string) {
    return await twoFactorAuth.generateTwoFactorSetup(userId, email)
  }

  /**
   * Enable 2FA after verification
   */
  async enableTwoFactor(userId: string, token: string) {
    return await twoFactorAuth.enableTwoFactor(userId, token)
  }

  /**
   * Verify 2FA token
   */
  async verifyTwoFactor(userId: string, token: string, backupCode?: string) {
    return await twoFactorAuth.verifyTwoFactor(userId, { token, backupCode })
  }

  /**
   * Disable 2FA
   */
  async disableTwoFactor(userId: string, token: string) {
    return await twoFactorAuth.disableTwoFactor(userId, token)
  }

  /**
   * Update user password
   */
  async updatePassword(newPassword: string): Promise<{ error: string | null }> {
    try {
      // Validate password strength
      const passwordValidation = this.validatePassword(newPassword)
      if (!passwordValidation.isValid) {
        return { error: passwordValidation.errors.join('. ') }
      }

      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        securityAudit.log('Password update failed', { error: error.message }, 'medium')
        return { error: error.message }
      }

      securityAudit.log('Password updated successfully', {}, 'low')
      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('Password update error', { error: errorMessage }, 'medium')
      return { error: errorMessage }
    }
  }

  /**
   * Update user profile
   */
  async updateProfile(data: UpdateProfileData): Promise<{ user: User | null; error: string | null }> {
    try {
      const { data: authData, error: authError } = await supabase.auth.updateUser({
        data: data
      })

      if (authError) {
        return { user: null, error: authError.message }
      }

      if (authData.user) {
        const { data: profileData, error: profileError } = await supabase
          .from('user_profiles')
          .update(data)
          .eq('id', authData.user.id)
          .select()
          .single()

        if (profileError) {
          return { user: null, error: profileError.message }
        }

        const userProfile = this.mapToUser(authData.user, profileData)
        securityAudit.log('Profile updated', { userId: authData.user.id }, 'low')
        return { user: userProfile, error: null }
      }

      return { user: null, error: 'Failed to update profile' }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      securityAudit.log('Profile update error', { error: errorMessage }, 'medium')
      return { user: null, error: errorMessage }
    }
  }

  /**
   * Get current user
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      
      if (!user) return null

      return await this.getUserProfile(user.id)
    } catch (error) {
      console.error('Error getting current user:', error)
      return null
    }
  }

  /**
   * Get user session
   */
  async getSession() {
    try {
      const { data: { session } } = await supabase.auth.getSession()
      return session
    } catch (error) {
      console.error('Error getting session:', error)
      return null
    }
  }

  /**
   * Listen to auth state changes
   */
  onAuthStateChange(callback: (state: AuthState) => void) {
    this.authStateListeners.push(callback)

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        const user = session?.user ? await this.getUserProfile(session.user.id) : null
        
        const state: AuthState = {
          user,
          session,
          loading: false,
          error: null
        }

        this.authStateListeners.forEach(listener => listener(state))
        
        securityAudit.log('Auth state changed', { event, userId: user?.id }, 'low')
      }
    )

    return () => {
      subscription.unsubscribe()
      this.authStateListeners = this.authStateListeners.filter(l => l !== callback)
    }
  }

  /**
   * Create user profile in database
   */
  private async createUserProfile(authUser: any): Promise<User> {
    const defaultLimits = {
      daily_scrapes: 10,
      monthly_scrapes: 100,
      max_urls_per_batch: 5,
      data_retention_days: 30
    }

    const profileData = {
      id: authUser.id,
      email: authUser.email,
      name: authUser.user_metadata?.name || authUser.email.split('@')[0],
      role: 'user' as const,
      subscription_status: 'free' as const,
      usage_limits: defaultLimits,
      created_at: new Date().toISOString()
    }

    const { data, error } = await supabase
      .from('user_profiles')
      .insert(profileData)
      .select()
      .single()

    if (error) {
      console.error('Error creating user profile:', error)
      // Return basic user data even if profile creation fails
      return this.mapToUser(authUser, profileData)
    }

    return this.mapToUser(authUser, data)
  }

  /**
   * Get user profile from database
   */
  private async getUserProfile(userId: string): Promise<User> {
    const { data, error } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', userId)
      .single()

    if (error) {
      console.error('Error fetching user profile:', error)
      // Return basic user data if profile fetch fails
      const { data: { user } } = await supabase.auth.getUser()
      return this.mapToUser(user, {
        id: userId,
        email: user?.email || '',
        role: 'user',
        subscription_status: 'free',
        usage_limits: {
          daily_scrapes: 10,
          monthly_scrapes: 100,
          max_urls_per_batch: 5,
          data_retention_days: 30
        }
      })
    }

    const { data: { user } } = await supabase.auth.getUser()
    return this.mapToUser(user, data)
  }

  /**
   * Map auth user and profile data to User interface
   */
  private mapToUser(authUser: any, profileData: any): User {
    return {
      id: authUser?.id || profileData.id,
      email: authUser?.email || profileData.email,
      name: profileData.name || authUser?.user_metadata?.name,
      avatar_url: profileData.avatar_url || authUser?.user_metadata?.avatar_url,
      role: profileData.role || 'user',
      created_at: profileData.created_at || authUser?.created_at,
      last_sign_in_at: authUser?.last_sign_in_at,
      email_confirmed_at: authUser?.email_confirmed_at,
      subscription_status: profileData.subscription_status || 'free',
      usage_limits: profileData.usage_limits || {
        daily_scrapes: 10,
        monthly_scrapes: 100,
        max_urls_per_batch: 5,
        data_retention_days: 30
      }
    }
  }
}

// Export singleton instance
export const authService = AuthService.getInstance()
