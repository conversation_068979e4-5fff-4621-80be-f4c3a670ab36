import { describe, it, expect, vi, beforeEach } from 'vitest'
import { screen, fireEvent, waitFor, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { render } from '@/test/utils'
import ScraperForm from '../ScraperForm'

// Mock the scraping library with better async handling
vi.mock('@/lib/scraping', () => ({
  webScraper: {
    scrapeUrl: vi.fn().mockImplementation((url) => {
      // Add a small delay to simulate real async behavior
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            url,
            title: 'Test Page',
            content: 'Test content',
            headings: ['Test Heading'],
            links: ['https://example.com/link'],
            wordCount: 10,
            metadata: {},
          })
        }, 100)
      })
    }),
    scrapeUrls: vi.fn().mockImplementation((urls) => {
      return Promise.all(urls.map(url => ({
        url,
        title: 'Test Page',
        content: 'Test content',
        headings: ['Test Heading'],
        links: ['https://example.com/link'],
        wordCount: 10,
        metadata: {},
      })))
    }),
  },
}))

describe('ScraperForm', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders the form correctly', () => {
    render(<ScraperForm />)

    expect(screen.getByText('Web Scraper')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('https://example.com')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Start Scraping/i })).toBeInTheDocument()
  })

  it('validates URL input', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Test with invalid URL
    await user.clear(input)
    await user.type(input, 'invalid-url')
    await user.click(submitButton)

    // The component should show some kind of validation error
    await waitFor(() => {
      // Look for any error-related text that might appear
      const errorElements = screen.queryAllByText(/invalid|error|warning|failed/i)
      expect(errorElements.length).toBeGreaterThan(0)
    })
  })

  it('accepts valid URLs', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Test with valid URL - clear first since there might be a default value
    await user.clear(input)
    await user.type(input, 'https://example.com')
    await user.click(submitButton)

    await waitFor(() => {
      // Look for progress or scraping related text
      const progressElements = screen.queryAllByText(/scraping|progress|processing/i)
      expect(progressElements.length).toBeGreaterThan(0)
    })
  })

  it('handles multiple URLs', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const addButton = screen.getByRole('button', { name: /Add URL/i })
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Add first URL
    await act(async () => {
      await user.type(input, 'https://example.com')
    })

    // Add second URL
    await act(async () => {
      await user.click(addButton)
    })

    const inputs = screen.getAllByPlaceholderText('https://example.com')
    await act(async () => {
      await user.type(inputs[1], 'https://test.com')
    })

    await act(async () => {
      await user.click(submitButton)
    })

    await waitFor(() => {
      expect(screen.getByText(/Scraping Progress/)).toBeInTheDocument()
    }, { timeout: 3000 })
  })

  it('shows progress during scraping', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    await act(async () => {
      await user.clear(input)
      await user.type(input, 'https://example.com')
    })

    await act(async () => {
      await user.click(submitButton)
    })

    // Should show loading state - button should be disabled during scraping
    await waitFor(() => {
      expect(submitButton).toBeDisabled()
    }, { timeout: 2000 })
  })

  it('displays results after successful scraping', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    await act(async () => {
      await user.clear(input)
      await user.type(input, 'https://example.com')
    })

    await act(async () => {
      await user.click(submitButton)
    })

    // Wait for scraping to complete - button should be enabled again
    await waitFor(() => {
      const button = screen.getByRole('button', { name: /Start Scraping/i })
      expect(button).not.toBeDisabled()
    }, { timeout: 5000 })
  })

  it('handles scraping errors gracefully', async () => {
    // Mock scraping failure
    const { webScraper } = await import('@/lib/scraping')
    vi.mocked(webScraper.scrapeUrl).mockResolvedValueOnce({
      url: 'https://example.com',
      error: 'Scraping failed'
    })

    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    await act(async () => {
      await user.clear(input)
      await user.type(input, 'https://example.com')
    })

    await act(async () => {
      await user.click(submitButton)
    })

    // Should handle error gracefully - button should be enabled again after error
    await waitFor(() => {
      const button = screen.getByRole('button', { name: /Start Scraping/i })
      expect(button).not.toBeDisabled()
    }, { timeout: 5000 })
  })

  it('allows clearing the form', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')

    await user.type(input, 'https://example.com')
    expect(input).toHaveValue('https://example.com')

    // Clear the input manually
    await user.clear(input)
    expect(input).toHaveValue('')
  })

  it('prevents submission with empty input', async () => {
    const user = userEvent.setup()
    render(<ScraperForm />)

    const input = screen.getByPlaceholderText('https://example.com')
    const submitButton = screen.getByRole('button', { name: /Start Scraping/i })

    // Clear any default value and try to submit
    await user.clear(input)
    await user.click(submitButton)

    // Should show some validation message or prevent submission
    await waitFor(() => {
      // Look for validation messages or check that nothing happened
      const validationElements = screen.queryAllByText(/enter|url|required|empty/i)
      expect(validationElements.length).toBeGreaterThan(0)
    })
  })
})
