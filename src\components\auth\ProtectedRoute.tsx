import React, { useEffect, useState } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Shield, Lock, AlertTriangle, RefreshCw } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string[];
  requiredPermissions?: string[];
  requireEmailVerification?: boolean;
  require2FA?: boolean;
  fallbackPath?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredRole = [],
  requiredPermissions = [],
  requireEmailVerification = true,
  require2FA = false,
  fallbackPath = '/'
}) => {
  const { user, loading, isAuthenticated, isTwoFactorEnabled } = useAuth();
  const location = useLocation();
  const [checking2FA, setChecking2FA] = useState(false);
  const [has2FA, setHas2FA] = useState(false);

  useEffect(() => {
    const check2FA = async () => {
      if (user && require2FA) {
        setChecking2FA(true);
        try {
          const enabled = await isTwoFactorEnabled(user.id);
          setHas2FA(enabled);
        } catch (error) {
          console.error('Error checking 2FA status:', error);
          setHas2FA(false);
        } finally {
          setChecking2FA(false);
        }
      }
    };

    check2FA();
  }, [user, require2FA, isTwoFactorEnabled]);

  // Show loading state while checking authentication
  if (loading || checking2FA) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
              <p className="text-gray-600">Verifying access...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return <Navigate to={fallbackPath} state={{ from: location }} replace />;
  }

  // Check email verification requirement
  if (requireEmailVerification && !user.email_confirmed_at) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <CardTitle className="text-xl">Email Verification Required</CardTitle>
            <CardDescription>
              Please verify your email address to access this page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4 border-yellow-200 bg-yellow-50">
              <AlertDescription className="text-yellow-700">
                You need to verify your email address before you can access this feature.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-3">
              <Button
                onClick={() => window.location.href = '/verify-email'}
                className="w-full"
              >
                Verify Email Address
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="w-full"
              >
                Return to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check 2FA requirement
  if (require2FA && !has2FA) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Two-Factor Authentication Required</CardTitle>
            <CardDescription>
              This page requires two-factor authentication to be enabled
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4 border-blue-200 bg-blue-50">
              <AlertDescription className="text-blue-700">
                For enhanced security, this feature requires two-factor authentication to be enabled on your account.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-3">
              <Button
                onClick={() => window.location.href = '/settings'}
                className="w-full"
              >
                Enable Two-Factor Authentication
              </Button>
              
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="w-full"
              >
                Return to Dashboard
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role requirements
  if (requiredRole.length > 0 && !requiredRole.includes(user.role || 'user')) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Lock className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Access Denied</CardTitle>
            <CardDescription>
              You don't have permission to access this page
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4 border-red-200 bg-red-50">
              <AlertDescription className="text-red-700">
                This page requires {requiredRole.join(' or ')} access level. Your current role is: {user.role || 'user'}.
              </AlertDescription>
            </Alert>
            
            <div className="space-y-3">
              <Button
                variant="outline"
                onClick={() => window.location.href = '/'}
                className="w-full"
              >
                Return to Dashboard
              </Button>
              
              <div className="text-center text-sm text-gray-600">
                <p>Need access? Contact your administrator.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check permission requirements
  if (requiredPermissions.length > 0) {
    const userPermissions = user.permissions || [];
    const hasRequiredPermissions = requiredPermissions.every(permission => 
      userPermissions.includes(permission)
    );

    if (!hasRequiredPermissions) {
      const missingPermissions = requiredPermissions.filter(permission => 
        !userPermissions.includes(permission)
      );

      return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <Lock className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle className="text-xl">Insufficient Permissions</CardTitle>
              <CardDescription>
                You don't have the required permissions to access this page
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Alert className="mb-4 border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">
                  Missing permissions: {missingPermissions.join(', ')}
                </AlertDescription>
              </Alert>
              
              <div className="space-y-3">
                <Button
                  variant="outline"
                  onClick={() => window.location.href = '/'}
                  className="w-full"
                >
                  Return to Dashboard
                </Button>
                
                <div className="text-center text-sm text-gray-600">
                  <p>Need access? Contact your administrator to request the required permissions.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      );
    }
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

export default ProtectedRoute;
