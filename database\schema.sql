-- Scraping Analysis Marketing Database Schema
-- This file contains the complete database schema for the Supabase backend

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- <PERSON><PERSON> custom types
CREATE TYPE job_status AS ENUM ('pending', 'running', 'completed', 'failed');

-- =============================================
-- TABLES
-- =============================================

-- User Profiles Table
-- Stores user information and subscription details
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL,
    name TEXT,
    avatar_url TEXT,
    role TEXT NOT NULL DEFAULT 'user' CHECK (role IN ('user', 'admin', 'premium')),
    subscription_status TEXT NOT NULL DEFAULT 'free' CHECK (subscription_status IN ('free', 'premium', 'enterprise')),
    usage_limits JSONB NOT NULL DEFAULT '{
        "daily_scrapes": 10,
        "monthly_scrapes": 100,
        "max_urls_per_batch": 5,
        "data_retention_days": 30
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_login_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- API Keys Table
-- Stores API keys for integrations
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    key_hash TEXT NOT NULL, -- Hashed API key
    service TEXT NOT NULL, -- slack, discord, zapier, etc.
    permissions JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT TRUE,
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_service CHECK (service IN ('slack', 'discord', 'zapier', 'email', 'google_sheets', 'webhook'))
);

-- Webhooks Table
-- Stores webhook configurations
CREATE TABLE IF NOT EXISTS webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    url TEXT NOT NULL,
    events JSONB NOT NULL DEFAULT '[]',
    secret TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_webhook_url CHECK (url ~ '^https?://')
);

-- Export Jobs Table
-- Tracks data export operations
CREATE TABLE IF NOT EXISTS export_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    export_type TEXT NOT NULL CHECK (export_type IN ('scraped_data', 'seo_analysis', 'keywords', 'leads', 'comprehensive')),
    format TEXT NOT NULL CHECK (format IN ('csv', 'json', 'xlsx', 'pdf')),
    filters JSONB DEFAULT '{}',
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    file_url TEXT,
    file_size INTEGER DEFAULT 0,
    record_count INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),

    -- Constraints
    CONSTRAINT valid_file_size CHECK (file_size >= 0),
    CONSTRAINT valid_record_count CHECK (record_count >= 0)
);

-- Scraping Jobs Table
-- Stores information about web scraping jobs
CREATE TABLE IF NOT EXISTS scraping_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    status job_status NOT NULL DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    results JSONB,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    priority INTEGER DEFAULT 0,
    user_agent TEXT,
    timeout_seconds INTEGER DEFAULT 30,
    
    -- Indexes for performance
    CONSTRAINT valid_url CHECK (url ~ '^https?://'),
    CONSTRAINT valid_timeout CHECK (timeout_seconds > 0 AND timeout_seconds <= 300),
    CONSTRAINT valid_retries CHECK (retry_count >= 0 AND max_retries >= 0)
);

-- Scraped Data Table
-- Stores the actual scraped content and metadata
CREATE TABLE IF NOT EXISTS scraped_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES scraping_jobs(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    title TEXT,
    content TEXT,
    description TEXT,
    metadata JSONB DEFAULT '{}',
    scraped_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    content_hash TEXT, -- For deduplication
    word_count INTEGER DEFAULT 0,
    language TEXT DEFAULT 'unknown',
    
    -- Constraints
    CONSTRAINT valid_scraped_url CHECK (url ~ '^https?://'),
    CONSTRAINT valid_word_count CHECK (word_count >= 0)
);

-- SEO Analysis Results Table
-- Stores SEO analysis results for scraped pages
CREATE TABLE IF NOT EXISTS seo_analysis (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scraped_data_id UUID NOT NULL REFERENCES scraped_data(id) ON DELETE CASCADE,
    overall_score INTEGER NOT NULL,
    title_score INTEGER DEFAULT 0,
    content_score INTEGER DEFAULT 0,
    technical_score INTEGER DEFAULT 0,
    keyword_score INTEGER DEFAULT 0,
    analysis_details JSONB DEFAULT '{}',
    recommendations JSONB DEFAULT '[]',
    issues JSONB DEFAULT '[]',
    analyzed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_scores CHECK (
        overall_score >= 0 AND overall_score <= 100 AND
        title_score >= 0 AND title_score <= 100 AND
        content_score >= 0 AND content_score <= 100 AND
        technical_score >= 0 AND technical_score <= 100 AND
        keyword_score >= 0 AND keyword_score <= 100
    )
);

-- Keywords Table
-- Stores extracted keywords from scraped content
CREATE TABLE IF NOT EXISTS keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scraped_data_id UUID NOT NULL REFERENCES scraped_data(id) ON DELETE CASCADE,
    keyword TEXT NOT NULL,
    frequency INTEGER NOT NULL DEFAULT 1,
    density DECIMAL(5,2) DEFAULT 0.0,
    context TEXT,
    position_first INTEGER,
    position_last INTEGER,
    is_title_keyword BOOLEAN DEFAULT FALSE,
    is_heading_keyword BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_frequency CHECK (frequency > 0),
    CONSTRAINT valid_density CHECK (density >= 0.0 AND density <= 100.0),
    CONSTRAINT valid_positions CHECK (
        (position_first IS NULL AND position_last IS NULL) OR
        (position_first IS NOT NULL AND position_last IS NOT NULL AND position_first <= position_last)
    )
);

-- Leads Table
-- Stores extracted lead information (emails, contacts, etc.)
CREATE TABLE IF NOT EXISTS leads (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scraped_data_id UUID NOT NULL REFERENCES scraped_data(id) ON DELETE CASCADE,
    email TEXT,
    name TEXT,
    company TEXT,
    phone TEXT,
    social_profiles JSONB DEFAULT '{}',
    context TEXT,
    confidence_score DECIMAL(3,2) DEFAULT 0.0,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0.0 AND confidence_score <= 1.0),
    CONSTRAINT has_contact_info CHECK (
        email IS NOT NULL OR phone IS NOT NULL OR 
        (social_profiles IS NOT NULL AND jsonb_array_length(jsonb_object_keys(social_profiles)) > 0)
    )
);

-- Lead Campaigns Table
-- Stores lead generation campaigns with targeting criteria and performance metrics
CREATE TABLE IF NOT EXISTS lead_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Active', 'Paused', 'Completed', 'Draft')),
    leads INTEGER DEFAULT 0,
    qualified INTEGER DEFAULT 0,
    converted INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.0,
    target_criteria JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_leads_count CHECK (leads >= 0),
    CONSTRAINT valid_qualified_count CHECK (qualified >= 0 AND qualified <= leads),
    CONSTRAINT valid_converted_count CHECK (converted >= 0 AND converted <= qualified),
    CONSTRAINT valid_conversion_rate CHECK (conversion_rate >= 0.0 AND conversion_rate <= 100.0)
);

-- Lead Management Table
-- Stores leads for the LeadGeneration component (separate from scraped leads)
CREATE TABLE IF NOT EXISTS lead_management (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    company TEXT NOT NULL,
    title TEXT NOT NULL,
    location TEXT NOT NULL,
    score INTEGER DEFAULT 50,
    status TEXT NOT NULL DEFAULT 'Cold' CHECK (status IN ('Hot', 'Warm', 'Cold', 'Qualified', 'Converted', 'Lost')),
    source TEXT NOT NULL DEFAULT 'Manual',
    tags JSONB DEFAULT '[]',
    notes TEXT DEFAULT '',
    last_contact TIMESTAMP WITH TIME ZONE,
    next_follow_up TIMESTAMP WITH TIME ZONE,
    social_profiles JSONB DEFAULT '{}',
    company_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_email_format CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_score_range CHECK (score >= 0 AND score <= 100)
);

-- Content Ideas Table
-- Stores generated content ideas based on scraped data
CREATE TABLE IF NOT EXISTS content_ideas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scraped_data_id UUID NOT NULL REFERENCES scraped_data(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    content_type TEXT DEFAULT 'article',
    target_keywords JSONB DEFAULT '[]',
    estimated_difficulty INTEGER DEFAULT 5,
    potential_traffic INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_used BOOLEAN DEFAULT FALSE,
    
    -- Constraints
    CONSTRAINT valid_difficulty CHECK (estimated_difficulty >= 1 AND estimated_difficulty <= 10),
    CONSTRAINT valid_traffic CHECK (potential_traffic >= 0)
);

-- Social Media Posts Table
-- Stores generated social media posts
CREATE TABLE IF NOT EXISTS social_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    scraped_data_id UUID NOT NULL REFERENCES scraped_data(id) ON DELETE CASCADE,
    platform TEXT NOT NULL,
    content TEXT NOT NULL,
    hashtags JSONB DEFAULT '[]',
    mentions JSONB DEFAULT '[]',
    scheduled_for TIMESTAMP WITH TIME ZONE,
    posted_at TIMESTAMP WITH TIME ZONE,
    engagement_metrics JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_published BOOLEAN DEFAULT FALSE,

    -- Constraints
    CONSTRAINT valid_platform CHECK (platform IN ('twitter', 'linkedin', 'facebook', 'instagram', 'generic')),
    CONSTRAINT valid_content_length CHECK (char_length(content) > 0 AND char_length(content) <= 2000)
);

-- Email Marketing Tables
-- Contact Lists Table
-- Stores email contact lists for organizing contacts
CREATE TABLE IF NOT EXISTS contact_lists (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    contact_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_contact_count CHECK (contact_count >= 0)
);

-- Email Contacts Table
-- Stores email contacts for marketing campaigns
CREATE TABLE IF NOT EXISTS email_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT NOT NULL,
    first_name TEXT,
    last_name TEXT,
    status TEXT NOT NULL DEFAULT 'Active' CHECK (status IN ('Active', 'Unsubscribed', 'Bounced')),
    tags JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_email CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT unique_email UNIQUE (email)
);

-- Email Templates Table
-- Stores reusable email templates
CREATE TABLE IF NOT EXISTS email_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    description TEXT,
    content TEXT NOT NULL,
    subject TEXT NOT NULL,
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_usage_count CHECK (usage_count >= 0)
);

-- Email Campaigns Table
-- Stores email marketing campaigns
CREATE TABLE IF NOT EXISTS email_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    subject TEXT NOT NULL,
    content TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Draft', 'Scheduled', 'Active', 'Completed', 'Paused')),
    sent INTEGER DEFAULT 0,
    opens INTEGER DEFAULT 0,
    clicks INTEGER DEFAULT 0,
    open_rate DECIMAL(5,2) DEFAULT 0.0,
    click_rate DECIMAL(5,2) DEFAULT 0.0,
    recipient_count INTEGER DEFAULT 0,
    template_id UUID REFERENCES email_templates(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,

    -- Constraints
    CONSTRAINT valid_sent CHECK (sent >= 0),
    CONSTRAINT valid_opens CHECK (opens >= 0),
    CONSTRAINT valid_clicks CHECK (clicks >= 0),
    CONSTRAINT valid_open_rate CHECK (open_rate >= 0.0 AND open_rate <= 100.0),
    CONSTRAINT valid_click_rate CHECK (click_rate >= 0.0 AND click_rate <= 100.0),
    CONSTRAINT valid_recipient_count CHECK (recipient_count >= 0)
);

-- User Settings Table
-- Stores user preferences and application settings
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    theme TEXT NOT NULL DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    language TEXT NOT NULL DEFAULT 'en',
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    notifications JSONB NOT NULL DEFAULT '{
        "email": true,
        "push": false,
        "marketing": true
    }',
    privacy JSONB NOT NULL DEFAULT '{
        "analytics": true,
        "data_collection": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Ensure one settings record per user
    UNIQUE(user_id)
);

-- Notifications Table
-- Stores user notifications
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    read BOOLEAN NOT NULL DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')
);

-- Saved Keywords Table
-- Stores user-saved keywords from keyword research
CREATE TABLE IF NOT EXISTS saved_keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    keyword TEXT NOT NULL,
    volume TEXT,
    difficulty INTEGER,
    cpc TEXT,
    trend TEXT CHECK (trend IN ('up', 'down', 'stable')),
    competition TEXT CHECK (competition IN ('High', 'Medium', 'Low')),
    intent TEXT CHECK (intent IN ('Commercial', 'Informational', 'Navigational', 'Transactional')),
    serp_features JSONB DEFAULT '[]',
    related_keywords JSONB DEFAULT '[]',
    questions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Prevent duplicate keywords per user
    UNIQUE(user_id, keyword)
);

-- =============================================
-- INDEXES
-- =============================================

-- Performance indexes
CREATE INDEX IF NOT EXISTS idx_scraping_jobs_status ON scraping_jobs(status);
CREATE INDEX IF NOT EXISTS idx_scraping_jobs_created_at ON scraping_jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_scraping_jobs_url ON scraping_jobs(url);

CREATE INDEX IF NOT EXISTS idx_scraped_data_job_id ON scraped_data(job_id);
CREATE INDEX IF NOT EXISTS idx_scraped_data_url ON scraped_data(url);
CREATE INDEX IF NOT EXISTS idx_scraped_data_scraped_at ON scraped_data(scraped_at DESC);
CREATE INDEX IF NOT EXISTS idx_scraped_data_content_hash ON scraped_data(content_hash);

-- Full-text search indexes
CREATE INDEX IF NOT EXISTS idx_scraped_data_content_fts ON scraped_data USING gin(to_tsvector('english', content));
CREATE INDEX IF NOT EXISTS idx_scraped_data_title_fts ON scraped_data USING gin(to_tsvector('english', title));

-- Keywords indexes
CREATE INDEX IF NOT EXISTS idx_keywords_scraped_data_id ON keywords(scraped_data_id);
CREATE INDEX IF NOT EXISTS idx_keywords_keyword ON keywords(keyword);
CREATE INDEX IF NOT EXISTS idx_keywords_frequency ON keywords(frequency DESC);

-- Leads indexes
CREATE INDEX IF NOT EXISTS idx_leads_scraped_data_id ON leads(scraped_data_id);
CREATE INDEX IF NOT EXISTS idx_leads_email ON leads(email);
CREATE INDEX IF NOT EXISTS idx_leads_company ON leads(company);

-- SEO Analysis indexes
CREATE INDEX IF NOT EXISTS idx_seo_analysis_scraped_data_id ON seo_analysis(scraped_data_id);
CREATE INDEX IF NOT EXISTS idx_seo_analysis_overall_score ON seo_analysis(overall_score DESC);

-- User Profiles indexes
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_profiles_role ON user_profiles(role);
CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription_status ON user_profiles(subscription_status);

-- API Keys indexes
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_service ON api_keys(service);
CREATE INDEX IF NOT EXISTS idx_api_keys_is_active ON api_keys(is_active);

-- Webhooks indexes
CREATE INDEX IF NOT EXISTS idx_webhooks_user_id ON webhooks(user_id);
CREATE INDEX IF NOT EXISTS idx_webhooks_is_active ON webhooks(is_active);

-- Export Jobs indexes
CREATE INDEX IF NOT EXISTS idx_export_jobs_user_id ON export_jobs(user_id);
CREATE INDEX IF NOT EXISTS idx_export_jobs_status ON export_jobs(status);
CREATE INDEX IF NOT EXISTS idx_export_jobs_created_at ON export_jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_export_jobs_expires_at ON export_jobs(expires_at);

-- User Settings indexes
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- Notifications indexes
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_expires_at ON notifications(expires_at);

-- Saved Keywords indexes
CREATE INDEX IF NOT EXISTS idx_saved_keywords_user_id ON saved_keywords(user_id);
CREATE INDEX IF NOT EXISTS idx_saved_keywords_keyword ON saved_keywords(keyword);
CREATE INDEX IF NOT EXISTS idx_saved_keywords_created_at ON saved_keywords(created_at DESC);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to update word count automatically
CREATE OR REPLACE FUNCTION update_word_count()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.content IS NOT NULL THEN
        NEW.word_count = array_length(string_to_array(trim(NEW.content), ' '), 1);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate content hash for deduplication
CREATE OR REPLACE FUNCTION generate_content_hash()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.content IS NOT NULL THEN
        NEW.content_hash = md5(NEW.content);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old failed jobs
CREATE OR REPLACE FUNCTION cleanup_old_jobs()
RETURNS void AS $$
BEGIN
    DELETE FROM scraping_jobs 
    WHERE status = 'failed' 
    AND created_at < NOW() - INTERVAL '30 days';
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Trigger to automatically update word count
CREATE TRIGGER trigger_update_word_count
    BEFORE INSERT OR UPDATE ON scraped_data
    FOR EACH ROW
    EXECUTE FUNCTION update_word_count();

-- Trigger to automatically generate content hash
CREATE TRIGGER trigger_generate_content_hash
    BEFORE INSERT OR UPDATE ON scraped_data
    FOR EACH ROW
    EXECUTE FUNCTION generate_content_hash();

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on all tables
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE export_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scraping_jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE scraped_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_analysis ENABLE ROW LEVEL SECURITY;
ALTER TABLE keywords ENABLE ROW LEVEL SECURITY;
ALTER TABLE leads ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_ideas ENABLE ROW LEVEL SECURITY;
ALTER TABLE social_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_keywords ENABLE ROW LEVEL SECURITY;

-- User Profiles policies
CREATE POLICY "Users can view own profile" ON user_profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON user_profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON user_profiles FOR INSERT WITH CHECK (auth.uid() = id);

-- API Keys policies
CREATE POLICY "Users can manage own API keys" ON api_keys FOR ALL USING (auth.uid() = user_id);

-- Webhooks policies
CREATE POLICY "Users can manage own webhooks" ON webhooks FOR ALL USING (auth.uid() = user_id);

-- Export Jobs policies
CREATE POLICY "Users can manage own export jobs" ON export_jobs FOR ALL USING (auth.uid() = user_id);

-- User Settings policies
CREATE POLICY "Users can manage own settings" ON user_settings FOR ALL USING (auth.uid() = user_id);

-- Notifications policies
CREATE POLICY "Users can manage own notifications" ON notifications FOR ALL USING (auth.uid() = user_id);

-- Saved Keywords policies
CREATE POLICY "Users can manage own saved keywords" ON saved_keywords FOR ALL USING (auth.uid() = user_id);

-- For demo purposes, allow all operations on main tables (in production, use user-based policies)
-- These policies allow anonymous access for the demo application
CREATE POLICY "Allow all operations on scraping_jobs" ON scraping_jobs FOR ALL USING (true);
CREATE POLICY "Allow all operations on scraped_data" ON scraped_data FOR ALL USING (true);
CREATE POLICY "Allow all operations on seo_analysis" ON seo_analysis FOR ALL USING (true);
CREATE POLICY "Allow all operations on keywords" ON keywords FOR ALL USING (true);
CREATE POLICY "Allow all operations on leads" ON leads FOR ALL USING (true);
CREATE POLICY "Allow all operations on content_ideas" ON content_ideas FOR ALL USING (true);
CREATE POLICY "Allow all operations on social_posts" ON social_posts FOR ALL USING (true);

-- Production-ready policies (commented out for demo)
-- CREATE POLICY "Users can view own scraping jobs" ON scraping_jobs FOR SELECT USING (auth.uid() = user_id);
-- CREATE POLICY "Users can create scraping jobs" ON scraping_jobs FOR INSERT WITH CHECK (auth.uid() = user_id);
-- CREATE POLICY "Users can update own scraping jobs" ON scraping_jobs FOR UPDATE USING (auth.uid() = user_id);
-- CREATE POLICY "Users can delete own scraping jobs" ON scraping_jobs FOR DELETE USING (auth.uid() = user_id);

-- =============================================
-- SAMPLE DATA (for development/testing)
-- =============================================

-- Insert sample scraping job
INSERT INTO scraping_jobs (url, status, results) VALUES 
('https://example.com', 'completed', '{"title": "Example Domain", "status": "success"}')
ON CONFLICT DO NOTHING;

-- Note: More sample data can be added as needed for development and testing
