-- Comprehensive fix for 406 errors
-- This script will fix authentication, RLS policies, and table structure issues
-- Run this in your Supabase SQL Editor

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- First, let's check what tables exist and their structure
DO $$
BEGIN
    RAISE NOTICE 'Starting 406 error fix...';
    RAISE NOTICE 'Current user: %', current_user;
    RAISE NOTICE 'Current database: %', current_database();
END $$;

-- Create notification_settings table
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    email BOOLEAN NOT NULL DEFAULT true,
    push BOOLEAN NOT NULL DEFAULT false,
    sms BOOLEAN NOT NULL DEFAULT false,
    marketing BOOLEAN NOT NULL DEFAULT true,
    security_alerts BOOLEAN NOT NULL DEFAULT true,
    export_complete BOOLEAN NOT NULL DEFAULT true,
    weekly_reports BOOLEAN NOT NULL DEFAULT false,
    system_updates BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create privacy_settings table
CREATE TABLE IF NOT EXISTS privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    dataCollection BOOLEAN NOT NULL DEFAULT true,
    analytics BOOLEAN NOT NULL DEFAULT true,
    thirdParty BOOLEAN NOT NULL DEFAULT false,
    cookieConsent BOOLEAN NOT NULL DEFAULT true,
    dataRetention INTEGER NOT NULL DEFAULT 365,
    shareUsageData BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create security_settings table
CREATE TABLE IF NOT EXISTS security_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    password_last_changed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    login_notifications BOOLEAN NOT NULL DEFAULT true,
    session_timeout INTEGER NOT NULL DEFAULT 60,
    ip_whitelist JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create user_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID,
    theme TEXT NOT NULL DEFAULT 'system',
    language TEXT NOT NULL DEFAULT 'en',
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    notifications JSONB NOT NULL DEFAULT '{
        "email": true,
        "push": false,
        "marketing": true
    }',
    privacy JSONB NOT NULL DEFAULT '{
        "analytics": true,
        "data_collection": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Disable Row Level Security temporarily for testing
ALTER TABLE notification_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE security_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings DISABLE ROW LEVEL SECURITY;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user_id ON privacy_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_security_settings_user_id ON security_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);

-- Insert default settings for any existing users (if any)
-- This is safe because we're using INSERT ... ON CONFLICT DO NOTHING
INSERT INTO notification_settings (user_id) 
SELECT id FROM auth.users 
ON CONFLICT DO NOTHING;

INSERT INTO privacy_settings (user_id) 
SELECT id FROM auth.users 
ON CONFLICT DO NOTHING;

INSERT INTO security_settings (user_id) 
SELECT id FROM auth.users 
ON CONFLICT DO NOTHING;

INSERT INTO user_settings (user_id) 
SELECT id FROM auth.users 
ON CONFLICT DO NOTHING;

-- Verify tables were created
SELECT 
    'notification_settings' as table_name, 
    COUNT(*) as row_count 
FROM notification_settings
UNION ALL
SELECT 
    'privacy_settings' as table_name, 
    COUNT(*) as row_count 
FROM privacy_settings
UNION ALL
SELECT 
    'security_settings' as table_name, 
    COUNT(*) as row_count 
FROM security_settings
UNION ALL
SELECT 
    'user_settings' as table_name, 
    COUNT(*) as row_count 
FROM user_settings;
