// Security utilities and validation functions

/**
 * URL validation and sanitization
 */
export class URLValidator {
  private static readonly ALLOWED_PROTOCOLS = ['http:', 'https:']
  private static readonly BLOCKED_DOMAINS = [
    'localhost',
    '127.0.0.1',
    '0.0.0.0',
    '::1',
    'file://',
    'data:',
    'javascript:',
    'vbscript:',
  ]

  static isValidURL(url: string): boolean {
    try {
      const urlObj = new URL(url)
      
      // Check protocol
      if (!this.ALLOWED_PROTOCOLS.includes(urlObj.protocol)) {
        return false
      }

      // Check for blocked domains
      const hostname = urlObj.hostname.toLowerCase()
      if (this.BLOCKED_DOMAINS.some(blocked => hostname.includes(blocked))) {
        return false
      }

      // Check for private IP ranges
      if (this.isPrivateIP(hostname)) {
        return false
      }

      // Check for suspicious patterns
      if (this.hasSuspiciousPatterns(url)) {
        return false
      }

      return true
    } catch {
      return false
    }
  }

  static sanitizeURL(url: string): string {
    try {
      const urlObj = new URL(url)
      
      // Remove dangerous query parameters
      const dangerousParams = ['javascript', 'vbscript', 'data', 'file']
      dangerousParams.forEach(param => {
        urlObj.searchParams.delete(param)
      })

      // Remove fragments that might contain scripts
      urlObj.hash = ''

      return urlObj.toString()
    } catch {
      throw new Error('Invalid URL format')
    }
  }

  private static isPrivateIP(hostname: string): boolean {
    const privateRanges = [
      /^10\./,
      /^172\.(1[6-9]|2[0-9]|3[01])\./,
      /^192\.168\./,
      /^169\.254\./,
      /^fc00:/,
      /^fe80:/,
    ]

    return privateRanges.some(range => range.test(hostname))
  }

  private static hasSuspiciousPatterns(url: string): boolean {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /data:/i,
      /file:/i,
      /\.\./,
      /%2e%2e/i,
      /%252e%252e/i,
    ]

    return suspiciousPatterns.some(pattern => pattern.test(url))
  }
}

/**
 * Input sanitization and validation
 */
export class InputSanitizer {
  static sanitizeHTML(input: string): string {
    // Remove script tags and their content
    let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    
    // Remove dangerous attributes
    sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '')
    sanitized = sanitized.replace(/\s*javascript\s*:\s*[^"'\s>]*/gi, '')
    
    // Remove dangerous tags
    const dangerousTags = ['script', 'iframe', 'object', 'embed', 'form', 'input', 'textarea', 'button']
    dangerousTags.forEach(tag => {
      const regex = new RegExp(`<\\/?${tag}\\b[^>]*>`, 'gi')
      sanitized = sanitized.replace(regex, '')
    })

    return sanitized.trim()
  }

  static sanitizeText(input: string): string {
    // Remove HTML tags
    let sanitized = input.replace(/<[^>]*>/g, '')
    
    // Decode HTML entities
    sanitized = sanitized
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&quot;/g, '"')
      .replace(/&#x27;/g, "'")
      .replace(/&#x2F;/g, '/')

    return sanitized.trim()
  }

  static validateEmail(email: string): boolean {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/
    return emailRegex.test(email) && email.length <= 254
  }

  static validatePhoneNumber(phone: string): boolean {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,15}$/
    return phoneRegex.test(phone)
  }

  static sanitizeFilename(filename: string): string {
    // Remove dangerous characters
    let sanitized = filename.replace(/[<>:"/\\|?*\x00-\x1f]/g, '')
    
    // Remove leading/trailing dots and spaces
    sanitized = sanitized.replace(/^[\.\s]+|[\.\s]+$/g, '')
    
    // Limit length
    if (sanitized.length > 255) {
      const ext = sanitized.split('.').pop()
      const name = sanitized.substring(0, 255 - (ext ? ext.length + 1 : 0))
      sanitized = ext ? `${name}.${ext}` : name
    }

    return sanitized || 'untitled'
  }
}

/**
 * Rate limiting utility
 */
export class RateLimiter {
  private static instances = new Map<string, RateLimiter>()
  private requests: number[] = []
  private readonly maxRequests: number
  private readonly windowMs: number

  constructor(maxRequests: number, windowMs: number) {
    this.maxRequests = maxRequests
    this.windowMs = windowMs
  }

  static getInstance(key: string, maxRequests: number, windowMs: number): RateLimiter {
    if (!this.instances.has(key)) {
      this.instances.set(key, new RateLimiter(maxRequests, windowMs))
    }
    return this.instances.get(key)!
  }

  isAllowed(): boolean {
    const now = Date.now()
    
    // Remove old requests outside the window
    this.requests = this.requests.filter(time => now - time < this.windowMs)
    
    // Check if we're under the limit
    if (this.requests.length < this.maxRequests) {
      this.requests.push(now)
      return true
    }
    
    return false
  }

  getRemainingRequests(): number {
    const now = Date.now()
    this.requests = this.requests.filter(time => now - time < this.windowMs)
    return Math.max(0, this.maxRequests - this.requests.length)
  }

  getResetTime(): number {
    if (this.requests.length === 0) return 0
    return this.requests[0] + this.windowMs
  }
}

/**
 * Content Security Policy utilities
 */
export class CSPManager {
  static generateNonce(): string {
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  static createCSPHeader(nonce?: string): string {
    const directives = [
      "default-src 'self'",
      `script-src 'self' ${nonce ? `'nonce-${nonce}'` : "'unsafe-inline'"} 'unsafe-eval'`,
      "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
      "font-src 'self' https://fonts.gstatic.com",
      "img-src 'self' data: https: blob:",
      "connect-src 'self' https://*.supabase.co https://api.allorigins.win https://cors-anywhere.herokuapp.com",
      "frame-src 'none'",
      "object-src 'none'",
      "base-uri 'self'",
      "form-action 'self'",
      "upgrade-insecure-requests"
    ]

    return directives.join('; ')
  }
}

/**
 * XSS protection utilities
 */
export class XSSProtection {
  static escapeHTML(text: string): string {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }

  static unescapeHTML(html: string): string {
    const div = document.createElement('div')
    div.innerHTML = html
    return div.textContent || div.innerText || ''
  }

  static sanitizeForAttribute(value: string): string {
    return value
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#x27;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
  }

  static isValidJSON(str: string): boolean {
    try {
      JSON.parse(str)
      return true
    } catch {
      return false
    }
  }
}

/**
 * Secure storage utilities
 */
export class SecureStorage {
  private static readonly ENCRYPTION_KEY = 'scraping-app-key'

  static setItem(key: string, value: any, encrypt = false): void {
    try {
      const serialized = JSON.stringify(value)
      const data = encrypt ? this.encrypt(serialized) : serialized
      localStorage.setItem(key, data)
    } catch (error) {
      console.error('Failed to store item:', error)
    }
  }

  static getItem<T>(key: string, decrypt = false): T | null {
    try {
      const data = localStorage.getItem(key)
      if (!data) return null
      
      const serialized = decrypt ? this.decrypt(data) : data
      return JSON.parse(serialized)
    } catch (error) {
      console.error('Failed to retrieve item:', error)
      return null
    }
  }

  static removeItem(key: string): void {
    localStorage.removeItem(key)
  }

  static clear(): void {
    localStorage.clear()
  }

  private static encrypt(text: string): string {
    // Simple XOR encryption for demo purposes
    // In production, use proper encryption libraries
    return btoa(text.split('').map((char, i) => 
      String.fromCharCode(char.charCodeAt(0) ^ this.ENCRYPTION_KEY.charCodeAt(i % this.ENCRYPTION_KEY.length))
    ).join(''))
  }

  private static decrypt(encryptedText: string): string {
    try {
      const text = atob(encryptedText)
      return text.split('').map((char, i) => 
        String.fromCharCode(char.charCodeAt(0) ^ this.ENCRYPTION_KEY.charCodeAt(i % this.ENCRYPTION_KEY.length))
      ).join('')
    } catch {
      throw new Error('Failed to decrypt data')
    }
  }
}

/**
 * Security headers validation
 */
export class SecurityHeaders {
  static validateResponse(response: Response): boolean {
    const requiredHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
    ]

    return requiredHeaders.every(header => response.headers.has(header))
  }

  static getSecurityScore(response: Response): number {
    const headers = [
      'strict-transport-security',
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'content-security-policy',
      'referrer-policy',
    ]

    const presentHeaders = headers.filter(header => response.headers.has(header))
    return Math.round((presentHeaders.length / headers.length) * 100)
  }
}

/**
 * Audit logging for security events
 */
export class SecurityAudit {
  private static logs: Array<{
    timestamp: number
    event: string
    details: any
    severity: 'low' | 'medium' | 'high' | 'critical'
  }> = []

  static log(event: string, details: any, severity: 'low' | 'medium' | 'high' | 'critical' = 'low'): void {
    this.logs.push({
      timestamp: Date.now(),
      event,
      details,
      severity
    })

    // Keep only last 1000 logs
    if (this.logs.length > 1000) {
      this.logs = this.logs.slice(-1000)
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`🔒 Security Event [${severity.toUpperCase()}]:`, event, details)
    }
  }

  static getLogs(severity?: string): typeof SecurityAudit.logs {
    if (severity) {
      return this.logs.filter(log => log.severity === severity)
    }
    return [...this.logs]
  }

  static clearLogs(): void {
    this.logs = []
  }

  static exportLogs(): string {
    return JSON.stringify(this.logs, null, 2)
  }
}

// Export singleton instances
export const urlValidator = URLValidator
export const inputSanitizer = InputSanitizer
export const xssProtection = XSSProtection
export const secureStorage = SecureStorage
export const securityHeaders = SecurityHeaders
export const securityAudit = SecurityAudit
