import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  Database,
  Download,
  FileText,
  Table,
  Code,
  Cloud,
  Calendar,
  Filter,
  Settings,
  CheckCircle,
  Clock,
  AlertCircle,
  Plus,
  Edit,
  Trash2,
  Copy,
  RefreshCw,
  Play,
  Pause,
  Eye,
  Mail,
  Zap,
  Archive,
  Upload,
  Share2,
  Server,
  HardDrive,
  Globe,
  Lock,
  Unlock
} from 'lucide-react';

interface ExportFormat {
  id: string;
  name: string;
  icon: any;
  description: string;
  extension: string;
  mimeType: string;
}

interface DataField {
  id: string;
  name: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'url';
  selected: boolean;
  required?: boolean;
}

interface ExportHistory {
  id: string;
  name: string;
  format: string;
  size: string;
  records: number;
  date: string;
  status: 'Completed' | 'Failed' | 'Processing' | 'Cancelled';
  download_url?: string;
  error_message?: string;
  created_at: string;
  completed_at?: string;
}

interface ScheduledExport {
  id: string;
  name: string;
  format: string;
  schedule: string;
  next_run: string;
  status: 'Active' | 'Paused' | 'Disabled';
  fields: string[];
  filters?: any;
  destination?: 'local' | 'cloud' | 'email' | 'ftp';
  destination_config?: any;
  created_at: string;
  last_run?: string;
}

interface ExportSettings {
  default_format: string;
  include_headers: boolean;
  compress_files: boolean;
  email_notification: boolean;
  max_records: number;
  chunk_size: number;
  retention_days: number;
}

interface CloudDestination {
  id: string;
  name: string;
  type: 'aws_s3' | 'google_drive' | 'dropbox' | 'azure_blob';
  config: any;
  connected: boolean;
}

const DataExport: React.FC = () => {
  const [selectedFormat, setSelectedFormat] = useState('csv');
  const [isExporting, setIsExporting] = useState(false);
  const [exportHistory, setExportHistory] = useState<ExportHistory[]>([]);
  const [scheduledExports, setScheduledExports] = useState<ScheduledExport[]>([]);
  const [dataFields, setDataFields] = useState<DataField[]>([]);
  const [exportSettings, setExportSettings] = useState<ExportSettings>({
    default_format: 'csv',
    include_headers: true,
    compress_files: false,
    email_notification: true,
    max_records: 100000,
    chunk_size: 10000,
    retention_days: 30
  });
  const [cloudDestinations, setCloudDestinations] = useState<CloudDestination[]>([]);
  const [selectedFields, setSelectedFields] = useState<string[]>([]);
  const [dateFrom, setDateFrom] = useState('');
  const [dateTo, setDateTo] = useState('');
  const [filename, setFilename] = useState('');
  const [exportProgress, setExportProgress] = useState(0);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [newSchedule, setNewSchedule] = useState<Partial<ScheduledExport>>({});
  const [showCloudDialog, setShowCloudDialog] = useState(false);
  const [selectedDestination, setSelectedDestination] = useState<string>('local');

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Load data on component mount
  useEffect(() => {
    loadExportHistory();
    loadScheduledExports();
    loadDataFields();
    loadExportSettings();
    loadCloudDestinations();
  }, []);

  const exportFormats: ExportFormat[] = [
    {
      id: 'csv',
      name: 'CSV',
      icon: Table,
      description: 'Comma-separated values',
      extension: 'csv',
      mimeType: 'text/csv'
    },
    {
      id: 'json',
      name: 'JSON',
      icon: Code,
      description: 'JavaScript Object Notation',
      extension: 'json',
      mimeType: 'application/json'
    },
    {
      id: 'xlsx',
      name: 'Excel',
      icon: FileText,
      description: 'Microsoft Excel format',
      extension: 'xlsx',
      mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    },
    {
      id: 'xml',
      name: 'XML',
      icon: Code,
      description: 'Extensible Markup Language',
      extension: 'xml',
      mimeType: 'application/xml'
    },
    {
      id: 'parquet',
      name: 'Parquet',
      icon: Database,
      description: 'Columnar storage format',
      extension: 'parquet',
      mimeType: 'application/octet-stream'
    }
  ];

  const loadExportHistory = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('export_history')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      if (data && data.length > 0) {
        setExportHistory(data);
      } else {
        // Set mock data if no history exists
        const mockHistory: ExportHistory[] = [
          {
            id: '1',
            name: 'Product Data Export',
            format: 'CSV',
            size: '2.4 MB',
            records: 15420,
            date: '2024-01-15 14:30',
            status: 'Completed',
            download_url: '/exports/product-data-20240115.csv',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            completed_at: new Date(Date.now() - 23 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            name: 'Customer Leads Export',
            format: 'JSON',
            size: '1.8 MB',
            records: 8950,
            date: '2024-01-14 09:15',
            status: 'Completed',
            download_url: '/exports/leads-20240114.json',
            created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
            completed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000).toISOString()
          }
        ];
        setExportHistory(mockHistory);
      }
    } catch (error) {
      console.error('Failed to load export history:', error);
      showError(error, 'Load export history');
    }
  }, [showError]);

  const loadScheduledExports = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('scheduled_exports')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setScheduledExports(data);
      } else {
        // Set mock data if no scheduled exports exist
        const mockScheduled: ScheduledExport[] = [
          {
            id: '1',
            name: 'Daily Sales Report',
            format: 'CSV',
            schedule: 'Daily at 9:00 AM',
            next_run: '2024-01-16 09:00',
            status: 'Active',
            fields: ['id', 'title', 'price', 'date'],
            destination: 'email',
            created_at: new Date().toISOString(),
            last_run: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: '2',
            name: 'Weekly Analytics',
            format: 'Excel',
            schedule: 'Weekly on Monday',
            next_run: '2024-01-22 08:00',
            status: 'Active',
            fields: ['id', 'title', 'description', 'category', 'url'],
            destination: 'cloud',
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          }
        ];
        setScheduledExports(mockScheduled);
      }
    } catch (error) {
      console.error('Failed to load scheduled exports:', error);
      showError(error, 'Load scheduled exports');
    }
  }, [showError]);

  const loadDataFields = useCallback(async () => {
    try {
      // Mock data fields - in real app, this would come from your data schema
      const mockFields: DataField[] = [
        { id: 'id', name: 'ID', type: 'string', selected: true, required: true },
        { id: 'title', name: 'Title', type: 'string', selected: true },
        { id: 'description', name: 'Description', type: 'string', selected: true },
        { id: 'price', name: 'Price', type: 'number', selected: true },
        { id: 'category', name: 'Category', type: 'string', selected: false },
        { id: 'url', name: 'URL', type: 'url', selected: true },
        { id: 'image', name: 'Image URL', type: 'url', selected: false },
        { id: 'date', name: 'Date Scraped', type: 'date', selected: true },
        { id: 'status', name: 'Status', type: 'string', selected: false },
        { id: 'tags', name: 'Tags', type: 'string', selected: false }
      ];

      setDataFields(mockFields);
      setSelectedFields(mockFields.filter(f => f.selected).map(f => f.id));
    } catch (error) {
      console.error('Failed to load data fields:', error);
    }
  }, []);

  const loadExportSettings = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('export_settings')
        .select('*')
        .single();

      if (error && error.code !== 'PGRST116') throw error;

      if (data) {
        setExportSettings(data);
      }
    } catch (error) {
      console.error('Failed to load export settings:', error);
    }
  }, []);

  const loadCloudDestinations = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('cloud_destinations')
        .select('*')
        .order('name');

      if (error) throw error;
      setCloudDestinations(data || []);
    } catch (error) {
      console.error('Failed to load cloud destinations:', error);
    }
  }, []);

  const handleExport = useCallback(async () => {
    if (selectedFields.length === 0) {
      showError(new Error('Please select at least one field to export'), 'Export data');
      return;
    }

    setIsExporting(true);
    setExportProgress(0);

    try {
      const exportName = filename || `export-${new Date().toISOString().split('T')[0]}`;
      const format = exportFormats.find(f => f.id === selectedFormat);

      if (!format) throw new Error('Invalid export format');

      // Simulate export progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + Math.random() * 20;
        });
      }, 500);

      // Create export record
      const exportRecord: Partial<ExportHistory> = {
        name: exportName,
        format: format.name,
        status: 'Processing',
        created_at: new Date().toISOString()
      };

      const { data: newExport, error } = await supabase
        .from('export_history')
        .insert([exportRecord])
        .select()
        .single();

      if (error) throw error;

      // Simulate data processing
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Generate mock export data
      const mockData = generateMockExportData(selectedFields, 1000);
      const exportData = await formatExportData(mockData, selectedFormat);

      // Create download
      const blob = new Blob([exportData], { type: format.mimeType });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${exportName}.${format.extension}`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);

      // Update export record
      const updatedRecord = {
        ...newExport,
        status: 'Completed' as const,
        size: `${(blob.size / 1024 / 1024).toFixed(1)} MB`,
        records: mockData.length,
        completed_at: new Date().toISOString(),
        download_url: url
      };

      await supabase
        .from('export_history')
        .update(updatedRecord)
        .eq('id', newExport.id);

      setExportHistory(prev => [updatedRecord, ...prev.filter(e => e.id !== newExport.id)]);
      setExportProgress(100);

      showSuccess(`Export completed: ${exportName}.${format.extension}`);

      // Reset form
      setFilename('');
      setDateFrom('');
      setDateTo('');

    } catch (error) {
      showError(error, 'Export data');
    } finally {
      setIsExporting(false);
      setTimeout(() => setExportProgress(0), 2000);
    }
  }, [selectedFields, selectedFormat, filename, exportFormats, showError, showSuccess]);

  const generateMockExportData = useCallback((fields: string[], count: number) => {
    const data = [];
    for (let i = 0; i < count; i++) {
      const record: any = {};
      fields.forEach(field => {
        switch (field) {
          case 'id':
            record[field] = `item-${i + 1}`;
            break;
          case 'title':
            record[field] = `Product ${i + 1}`;
            break;
          case 'description':
            record[field] = `Description for product ${i + 1}`;
            break;
          case 'price':
            record[field] = (Math.random() * 1000).toFixed(2);
            break;
          case 'category':
            record[field] = ['Electronics', 'Clothing', 'Books', 'Home'][Math.floor(Math.random() * 4)];
            break;
          case 'url':
            record[field] = `https://example.com/product/${i + 1}`;
            break;
          case 'image':
            record[field] = `https://example.com/images/product-${i + 1}.jpg`;
            break;
          case 'date':
            record[field] = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString();
            break;
          default:
            record[field] = `Value ${i + 1}`;
        }
      });
      data.push(record);
    }
    return data;
  }, []);

  const formatExportData = useCallback(async (data: any[], format: string): Promise<string> => {
    switch (format) {
      case 'csv':
        const headers = Object.keys(data[0] || {});
        const csvRows = [
          headers.join(','),
          ...data.map(row => headers.map(header => `"${row[header] || ''}"`).join(','))
        ];
        return csvRows.join('\n');

      case 'json':
        return JSON.stringify(data, null, 2);

      case 'xml':
        let xml = '<?xml version="1.0" encoding="UTF-8"?>\n<data>\n';
        data.forEach(item => {
          xml += '  <item>\n';
          Object.entries(item).forEach(([key, value]) => {
            xml += `    <${key}>${value}</${key}>\n`;
          });
          xml += '  </item>\n';
        });
        xml += '</data>';
        return xml;

      default:
        return JSON.stringify(data, null, 2);
    }
  }, []);

  const createScheduledExport = useCallback(async () => {
    if (!newSchedule.name || !newSchedule.format || !newSchedule.schedule) {
      showError(new Error('Please fill in all required fields'), 'Create scheduled export');
      return;
    }

    try {
      const scheduleData: Partial<ScheduledExport> = {
        ...newSchedule,
        status: 'Active',
        fields: selectedFields,
        created_at: new Date().toISOString(),
        next_run: calculateNextRun(newSchedule.schedule || '')
      };

      const { data, error } = await supabase
        .from('scheduled_exports')
        .insert([scheduleData])
        .select()
        .single();

      if (error) throw error;

      setScheduledExports(prev => [data, ...prev]);
      setNewSchedule({});
      setShowScheduleDialog(false);
      showSuccess('Scheduled export created successfully');
    } catch (error) {
      showError(error, 'Create scheduled export');
    }
  }, [newSchedule, selectedFields, showError, showSuccess]);

  const calculateNextRun = useCallback((schedule: string): string => {
    const now = new Date();
    // Simple calculation - in real app, use a proper cron parser
    if (schedule.includes('Daily')) {
      const tomorrow = new Date(now);
      tomorrow.setDate(tomorrow.getDate() + 1);
      tomorrow.setHours(9, 0, 0, 0);
      return tomorrow.toISOString();
    } else if (schedule.includes('Weekly')) {
      const nextWeek = new Date(now);
      nextWeek.setDate(nextWeek.getDate() + 7);
      nextWeek.setHours(8, 0, 0, 0);
      return nextWeek.toISOString();
    } else {
      const nextMonth = new Date(now);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      nextMonth.setDate(1);
      nextMonth.setHours(10, 0, 0, 0);
      return nextMonth.toISOString();
    }
  }, []);

  const toggleScheduledExport = useCallback(async (id: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'Active' ? 'Paused' : 'Active';

      const { error } = await supabase
        .from('scheduled_exports')
        .update({ status: newStatus })
        .eq('id', id);

      if (error) throw error;

      setScheduledExports(prev => prev.map(schedule =>
        schedule.id === id ? { ...schedule, status: newStatus } : schedule
      ));

      showSuccess(`Export ${newStatus.toLowerCase()}`);
    } catch (error) {
      showError(error, 'Toggle scheduled export');
    }
  }, [showError, showSuccess]);

  const deleteScheduledExport = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from('scheduled_exports')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setScheduledExports(prev => prev.filter(schedule => schedule.id !== id));
      showSuccess('Scheduled export deleted');
    } catch (error) {
      showError(error, 'Delete scheduled export');
    }
  }, [showError, showSuccess]);

  const saveExportSettings = useCallback(async () => {
    try {
      const { error } = await supabase
        .from('export_settings')
        .upsert([exportSettings]);

      if (error) throw error;

      showSuccess('Export settings saved');
    } catch (error) {
      showError(error, 'Save export settings');
    }
  }, [exportSettings, showError, showSuccess]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'Failed': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'Active': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'Paused': return <Clock className="h-4 w-4 text-yellow-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Paused': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Database className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">Data Export</h1>
      </div>

      <Tabs defaultValue="export" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="export">Export Data</TabsTrigger>
          <TabsTrigger value="history">Export History</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Exports</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="export" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Download className="h-5 w-5" />
                <span>Export Data</span>
              </CardTitle>
              <CardDescription>
                Export your scraped data in various formats
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Select Export Format</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-2">
                    {exportFormats.map((format) => (
                      <Card 
                        key={format.id} 
                        className={`cursor-pointer transition-all ${
                          selectedFormat === format.id 
                            ? 'ring-2 ring-purple-500 bg-purple-50' 
                            : 'hover:shadow-md'
                        }`}
                        onClick={() => setSelectedFormat(format.id)}
                      >
                        <CardContent className="p-4 text-center">
                          <format.icon className="h-8 w-8 mx-auto mb-2 text-purple-600" />
                          <h3 className="font-medium">{format.name}</h3>
                          <p className="text-xs text-gray-600 mt-1">{format.description}</p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                <div>
                  <Label className="text-base font-medium">Select Data Fields</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mt-2">
                    {dataFields.map((field) => (
                      <div key={field.id} className="flex items-center space-x-2">
                        <Checkbox 
                          id={field.id}
                          defaultChecked={field.selected}
                        />
                        <Label htmlFor={field.id} className="text-sm">
                          {field.name}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="date-from">Date From</Label>
                    <Input
                      id="date-from"
                      type="date"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date-to">Date To</Label>
                    <Input
                      id="date-to"
                      type="date"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="filename">Export Filename</Label>
                  <Input
                    id="filename"
                    placeholder="my-export-data"
                  />
                </div>
              </div>

              <div className="flex space-x-2">
                <Button onClick={handleExport} disabled={isExporting}>
                  {isExporting ? 'Exporting...' : 'Export Data'}
                </Button>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Advanced Filters
                </Button>
              </div>

              {isExporting && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                    <span className="text-sm text-gray-600">Preparing export...</span>
                  </div>
                  <Progress value={75} className="w-full" />
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Export History</h2>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download All
            </Button>
          </div>

          <div className="space-y-3">
            {exportHistory.map((export_, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium">{export_.name}</h3>
                        <Badge className={getStatusColor(export_.status)}>
                          {getStatusIcon(export_.status)}
                          <span className="ml-1">{export_.status}</span>
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Format:</span> {export_.format}
                        </div>
                        <div>
                          <span className="font-medium">Size:</span> {export_.size}
                        </div>
                        <div>
                          <span className="font-medium">Records:</span> {export_.records.toLocaleString()}
                        </div>
                        <div>
                          <span className="font-medium">Date:</span> {export_.date}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      {export_.status === 'Completed' && (
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      )}
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Scheduled Exports</h2>
            <Button>
              <Calendar className="h-4 w-4 mr-2" />
              New Schedule
            </Button>
          </div>

          <div className="space-y-3">
            {scheduledExports.map((schedule, index) => (
              <Card key={index}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="font-medium">{schedule.name}</h3>
                        <Badge className={getStatusColor(schedule.status)}>
                          {getStatusIcon(schedule.status)}
                          <span className="ml-1">{schedule.status}</span>
                        </Badge>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium">Format:</span> {schedule.format}
                        </div>
                        <div>
                          <span className="font-medium">Schedule:</span> {schedule.schedule}
                        </div>
                        <div>
                          <span className="font-medium">Next Run:</span> {schedule.nextRun}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                      <Button variant="outline" size="sm">
                        {schedule.status === 'Active' ? 'Pause' : 'Resume'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Export Settings</span>
              </CardTitle>
              <CardDescription>
                Configure default export settings and preferences
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div>
                  <Label className="text-base font-medium">Default Export Format</Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-2">
                    {exportFormats.map((format) => (
                      <Button
                        key={format.id}
                        variant="outline"
                        size="sm"
                        className="justify-start"
                      >
                        <format.icon className="h-4 w-4 mr-2" />
                        {format.name}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="space-y-3">
                  <Label className="text-base font-medium">Export Options</Label>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Checkbox id="include-headers" defaultChecked />
                      <Label htmlFor="include-headers">Include column headers</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="compress-files" />
                      <Label htmlFor="compress-files">Compress large files</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Checkbox id="email-notification" defaultChecked />
                      <Label htmlFor="email-notification">Email notification on completion</Label>
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max-records">Maximum records per export</Label>
                  <Input
                    id="max-records"
                    type="number"
                    placeholder="100000"
                  />
                </div>
              </div>

              <Button>Save Settings</Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default DataExport;
