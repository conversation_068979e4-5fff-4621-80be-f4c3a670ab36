-- Create SEO-related tables for the SEOTools component
-- Run this in your Supabase SQL Editor to create the missing SEO tables

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- SEO Analyses Table (plural - what SEOTools component expects)
-- This is separate from the existing seo_analysis table and stores complete SEO analysis results
CREATE TABLE IF NOT EXISTS seo_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL,
    overall_score INTEGER NOT NULL DEFAULT 0,
    page_speed INTEGER DEFAULT 0,
    mobile_friendly INTEGER DEFAULT 0,
    security INTEGER DEFAULT 0,
    accessibility INTEGER DEFAULT 0,
    seo_score INTEGER DEFAULT 0,
    issues JSONB DEFAULT '[]',
    recommendations JSONB DEFAULT '[]',
    meta_data JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_seo_url CHECK (url ~ '^https?://'),
    CONSTRAINT valid_overall_score CHECK (overall_score >= 0 AND overall_score <= 100),
    CONSTRAINT valid_page_speed CHECK (page_speed >= 0 AND page_speed <= 100),
    CONSTRAINT valid_mobile_friendly CHECK (mobile_friendly >= 0 AND mobile_friendly <= 100),
    CONSTRAINT valid_security CHECK (security >= 0 AND security <= 100),
    CONSTRAINT valid_accessibility CHECK (accessibility >= 0 AND accessibility <= 100),
    CONSTRAINT valid_seo_score CHECK (seo_score >= 0 AND seo_score <= 100)
);

-- Keyword Data Table
-- Stores keyword research data with volume, difficulty, competition metrics
CREATE TABLE IF NOT EXISTS keyword_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    keyword TEXT NOT NULL,
    volume TEXT,
    difficulty TEXT CHECK (difficulty IN ('Low', 'Medium', 'High')),
    cpc TEXT,
    competition DECIMAL(3,2) DEFAULT 0.0,
    trend TEXT CHECK (trend IN ('up', 'down', 'stable')),
    related_keywords JSONB DEFAULT '[]',
    questions JSONB DEFAULT '[]',
    serp_features JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_competition CHECK (competition >= 0.0 AND competition <= 1.0)
);

-- Competitor Analyses Table
-- Stores competitor analysis results with domain metrics and insights
CREATE TABLE IF NOT EXISTS competitor_analyses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    competitor_url TEXT NOT NULL,
    domain_authority INTEGER DEFAULT 0,
    organic_keywords INTEGER DEFAULT 0,
    organic_traffic TEXT,
    backlinks INTEGER DEFAULT 0,
    top_keywords JSONB DEFAULT '[]',
    content_gaps JSONB DEFAULT '[]',
    strengths JSONB DEFAULT '[]',
    weaknesses JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_competitor_url CHECK (competitor_url ~ '^https?://'),
    CONSTRAINT valid_domain_authority CHECK (domain_authority >= 0 AND domain_authority <= 100),
    CONSTRAINT valid_organic_keywords CHECK (organic_keywords >= 0),
    CONSTRAINT valid_backlinks CHECK (backlinks >= 0)
);

-- Backlinks Table
-- Stores backlink data with source, target, and quality metrics
CREATE TABLE IF NOT EXISTS backlinks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    source_url TEXT NOT NULL,
    target_url TEXT NOT NULL,
    anchor_text TEXT,
    link_type TEXT DEFAULT 'dofollow' CHECK (link_type IN ('dofollow', 'nofollow')),
    domain_authority INTEGER DEFAULT 0,
    page_authority INTEGER DEFAULT 0,
    spam_score INTEGER DEFAULT 0,
    first_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_seen TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_source_url CHECK (source_url ~ '^https?://'),
    CONSTRAINT valid_target_url CHECK (target_url ~ '^https?://'),
    CONSTRAINT valid_domain_authority_bl CHECK (domain_authority >= 0 AND domain_authority <= 100),
    CONSTRAINT valid_page_authority CHECK (page_authority >= 0 AND page_authority <= 100),
    CONSTRAINT valid_spam_score CHECK (spam_score >= 0 AND spam_score <= 100),
    
    -- Prevent duplicate backlinks
    UNIQUE(source_url, target_url)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_seo_analyses_url ON seo_analyses(url);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_created_at ON seo_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_seo_analyses_overall_score ON seo_analyses(overall_score DESC);

CREATE INDEX IF NOT EXISTS idx_keyword_data_keyword ON keyword_data(keyword);
CREATE INDEX IF NOT EXISTS idx_keyword_data_created_at ON keyword_data(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_keyword_data_difficulty ON keyword_data(difficulty);

CREATE INDEX IF NOT EXISTS idx_competitor_analyses_url ON competitor_analyses(competitor_url);
CREATE INDEX IF NOT EXISTS idx_competitor_analyses_created_at ON competitor_analyses(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_competitor_analyses_domain_authority ON competitor_analyses(domain_authority DESC);

CREATE INDEX IF NOT EXISTS idx_backlinks_source_url ON backlinks(source_url);
CREATE INDEX IF NOT EXISTS idx_backlinks_target_url ON backlinks(target_url);
CREATE INDEX IF NOT EXISTS idx_backlinks_last_seen ON backlinks(last_seen DESC);
CREATE INDEX IF NOT EXISTS idx_backlinks_is_active ON backlinks(is_active);

-- Enable Row Level Security (RLS)
ALTER TABLE seo_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE keyword_data ENABLE ROW LEVEL SECURITY;
ALTER TABLE competitor_analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE backlinks ENABLE ROW LEVEL SECURITY;

-- Create permissive RLS policies for demo (replace with proper user-based policies in production)
CREATE POLICY "Allow all operations on seo_analyses" ON seo_analyses FOR ALL USING (true);
CREATE POLICY "Allow all operations on keyword_data" ON keyword_data FOR ALL USING (true);
CREATE POLICY "Allow all operations on competitor_analyses" ON competitor_analyses FOR ALL USING (true);
CREATE POLICY "Allow all operations on backlinks" ON backlinks FOR ALL USING (true);

-- Insert some sample data for testing
INSERT INTO seo_analyses (url, overall_score, page_speed, mobile_friendly, security, accessibility, seo_score, issues, recommendations, meta_data) VALUES 
(
    'https://example.com',
    85,
    78,
    92,
    95,
    80,
    88,
    '[{"type": "warning", "category": "performance", "title": "Page Speed", "description": "Page could load faster", "impact": "medium", "fix_suggestion": "Optimize images and minify CSS"}]',
    '["Optimize page loading speed", "Add missing meta descriptions", "Improve mobile responsiveness"]',
    '{"title": "Example Website", "description": "A sample website for testing", "keywords": ["example", "test", "website"], "h1_tags": ["Welcome to Example"], "images_without_alt": 2, "internal_links": 15, "external_links": 8}'
),
(
    'https://test.com',
    72,
    65,
    88,
    90,
    75,
    70,
    '[{"type": "error", "category": "technical", "title": "Missing Meta Description", "description": "Page lacks meta description", "impact": "high", "fix_suggestion": "Add descriptive meta description"}]',
    '["Add meta descriptions", "Improve page speed", "Fix broken links"]',
    '{"title": "Test Site", "description": "", "keywords": ["test"], "h1_tags": ["Test Page"], "images_without_alt": 5, "internal_links": 8, "external_links": 3}'
)
ON CONFLICT DO NOTHING;

INSERT INTO keyword_data (keyword, volume, difficulty, cpc, competition, trend, related_keywords, questions, serp_features) VALUES 
(
    'web scraping',
    '12K',
    'Medium',
    '$2.50',
    0.65,
    'up',
    '["data extraction", "web crawling", "automated scraping"]',
    '["What is web scraping?", "How to scrape websites?", "Is web scraping legal?"]',
    '["Featured Snippet", "People Also Ask", "Related Searches"]'
),
(
    'SEO analysis',
    '8.5K',
    'High',
    '$3.20',
    0.78,
    'stable',
    '["SEO audit", "website analysis", "SEO tools"]',
    '["How to do SEO analysis?", "Best SEO analysis tools", "Free SEO analysis"]',
    '["Featured Snippet", "Video Results", "Related Searches"]'
)
ON CONFLICT DO NOTHING;

INSERT INTO competitor_analyses (competitor_url, domain_authority, organic_keywords, organic_traffic, backlinks, top_keywords, content_gaps, strengths, weaknesses) VALUES 
(
    'https://competitor1.com',
    75,
    15420,
    '125K',
    8500,
    '[{"keyword": "digital marketing", "volume": "45K", "position": 3}, {"keyword": "SEO tools", "volume": "12K", "position": 5}]',
    '["Content about local SEO", "Mobile optimization guides", "Technical SEO tutorials"]',
    '["Strong domain authority", "High-quality backlinks", "Regular content updates"]',
    '["Slow page speed", "Limited mobile optimization", "Weak social media presence"]'
),
(
    'https://competitor2.com',
    68,
    9800,
    '85K',
    4200,
    '[{"keyword": "content marketing", "volume": "32K", "position": 2}, {"keyword": "social media", "volume": "28K", "position": 4}]',
    '["Advanced analytics content", "Automation tutorials", "Industry case studies"]',
    '["Excellent content quality", "Strong social presence", "Good user engagement"]',
    '["Lower domain authority", "Fewer backlinks", "Limited technical content"]'
)
ON CONFLICT DO NOTHING;

INSERT INTO backlinks (source_url, target_url, anchor_text, link_type, domain_authority, page_authority, spam_score) VALUES 
(
    'https://authoritysite.com/article1',
    'https://example.com',
    'comprehensive SEO guide',
    'dofollow',
    85,
    72,
    5
),
(
    'https://blog.example.org/post2',
    'https://example.com/tools',
    'SEO analysis tools',
    'dofollow',
    68,
    55,
    12
),
(
    'https://forum.marketing.com/thread123',
    'https://example.com',
    'check this out',
    'nofollow',
    45,
    38,
    25
)
ON CONFLICT DO NOTHING;
