// Data export utilities for various formats

import { ScrapedData, SEOAnalysis, Keyword, Lead, ContentIdea, SocialPost } from '@/types'

export type ExportFormat = 'csv' | 'json' | 'xlsx' | 'pdf'

export interface ExportOptions {
  format: ExportFormat
  filename?: string
  includeMetadata?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  fields?: string[]
  maxRecords?: number
}

export interface ExportResult {
  success: boolean
  filename: string
  size: number
  recordCount: number
  error?: string
}

/**
 * Data export service
 */
export class DataExporter {
  private static instance: DataExporter

  static getInstance(): DataExporter {
    if (!DataExporter.instance) {
      DataExporter.instance = new DataExporter()
    }
    return DataExporter.instance
  }

  /**
   * Export scraped data
   */
  async exportScrapedData(
    data: ScrapedData[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const filteredData = this.filterData(data, options)
      const processedData = this.processScrapedData(filteredData, options)

      switch (options.format) {
        case 'csv':
          return await this.exportToCSV(processedData, options)
        case 'json':
          return await this.exportToJSON(processedData, options)
        case 'xlsx':
          return await this.exportToExcel(processedData, options)
        case 'pdf':
          return await this.exportToPDF(processedData, options)
        default:
          throw new Error(`Unsupported export format: ${options.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        size: 0,
        recordCount: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }
    }
  }

  /**
   * Export SEO analysis data
   */
  async exportSEOAnalysis(
    data: SEOAnalysis[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const filteredData = this.filterData(data, options)
      const processedData = this.processSEOData(filteredData, options)

      switch (options.format) {
        case 'csv':
          return await this.exportToCSV(processedData, options)
        case 'json':
          return await this.exportToJSON(processedData, options)
        case 'xlsx':
          return await this.exportToExcel(processedData, options)
        case 'pdf':
          return await this.exportToPDF(processedData, options)
        default:
          throw new Error(`Unsupported export format: ${options.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        size: 0,
        recordCount: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }
    }
  }

  /**
   * Export keywords data
   */
  async exportKeywords(
    data: Keyword[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const filteredData = this.filterData(data, options)
      const processedData = this.processKeywordsData(filteredData, options)

      switch (options.format) {
        case 'csv':
          return await this.exportToCSV(processedData, options)
        case 'json':
          return await this.exportToJSON(processedData, options)
        case 'xlsx':
          return await this.exportToExcel(processedData, options)
        default:
          throw new Error(`Unsupported export format: ${options.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        size: 0,
        recordCount: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }
    }
  }

  /**
   * Export leads data
   */
  async exportLeads(
    data: Lead[],
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const filteredData = this.filterData(data, options)
      const processedData = this.processLeadsData(filteredData, options)

      switch (options.format) {
        case 'csv':
          return await this.exportToCSV(processedData, options)
        case 'json':
          return await this.exportToJSON(processedData, options)
        case 'xlsx':
          return await this.exportToExcel(processedData, options)
        default:
          throw new Error(`Unsupported export format: ${options.format}`)
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        size: 0,
        recordCount: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }
    }
  }

  /**
   * Export comprehensive report
   */
  async exportComprehensiveReport(
    data: {
      scrapedData: ScrapedData[]
      seoAnalysis: SEOAnalysis[]
      keywords: Keyword[]
      leads: Lead[]
      contentIdeas: ContentIdea[]
      socialPosts: SocialPost[]
    },
    options: ExportOptions
  ): Promise<ExportResult> {
    try {
      const report = this.generateComprehensiveReport(data, options)

      switch (options.format) {
        case 'json':
          return await this.exportToJSON(report, options)
        case 'xlsx':
          return await this.exportToExcel(report, options)
        case 'pdf':
          return await this.exportToPDF(report, options)
        default:
          throw new Error(`Format ${options.format} not supported for comprehensive reports`)
      }
    } catch (error) {
      return {
        success: false,
        filename: '',
        size: 0,
        recordCount: 0,
        error: error instanceof Error ? error.message : 'Export failed'
      }
    }
  }

  /**
   * Filter data based on options
   */
  private filterData<T extends { created_at?: string; scraped_at?: string; analyzed_at?: string }>(
    data: T[],
    options: ExportOptions
  ): T[] {
    let filtered = [...data]

    // Apply date range filter
    if (options.dateRange) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(
          item.created_at || item.scraped_at || item.analyzed_at || ''
        )
        return itemDate >= options.dateRange!.start && itemDate <= options.dateRange!.end
      })
    }

    // Apply max records limit
    if (options.maxRecords) {
      filtered = filtered.slice(0, options.maxRecords)
    }

    return filtered
  }

  /**
   * Process scraped data for export
   */
  private processScrapedData(data: ScrapedData[], options: ExportOptions): any[] {
    return data.map(item => {
      const processed: any = {
        id: item.id,
        url: item.url,
        title: item.title,
        content_preview: item.content?.substring(0, 200) + '...',
        word_count: item.word_count,
        scraped_at: item.scraped_at,
        status: item.scraping_jobs?.status || 'unknown'
      }

      if (options.includeMetadata && item.metadata) {
        processed.headings_count = item.metadata.headings?.length || 0
        processed.links_count = item.metadata.links?.length || 0
        processed.images_count = item.metadata.images?.length || 0
      }

      // Include only specified fields if provided
      if (options.fields) {
        const filtered: any = {}
        options.fields.forEach(field => {
          if (processed[field] !== undefined) {
            filtered[field] = processed[field]
          }
        })
        return filtered
      }

      return processed
    })
  }

  /**
   * Process SEO data for export
   */
  private processSEOData(data: SEOAnalysis[], options: ExportOptions): any[] {
    return data.map(item => ({
      id: item.id,
      url: item.scraped_data?.url || 'N/A',
      overall_score: item.overall_score,
      title_score: item.title_score,
      content_score: item.content_score,
      technical_score: item.technical_score,
      keyword_score: item.keyword_score,
      analyzed_at: item.analyzed_at,
      recommendations_count: item.recommendations?.length || 0,
      issues_count: item.issues?.length || 0
    }))
  }

  /**
   * Process keywords data for export
   */
  private processKeywordsData(data: Keyword[], options: ExportOptions): any[] {
    return data.map(item => ({
      keyword: item.keyword,
      frequency: item.frequency,
      density: item.density,
      context: item.context?.substring(0, 100) + '...',
      is_title_keyword: item.is_title_keyword,
      is_heading_keyword: item.is_heading_keyword,
      created_at: item.created_at
    }))
  }

  /**
   * Process leads data for export
   */
  private processLeadsData(data: Lead[], options: ExportOptions): any[] {
    return data.map(item => ({
      email: item.email,
      name: item.name,
      company: item.company,
      phone: item.phone,
      confidence_score: item.confidence_score,
      is_verified: item.is_verified,
      context: item.context?.substring(0, 100) + '...',
      created_at: item.created_at
    }))
  }

  /**
   * Generate comprehensive report
   */
  private generateComprehensiveReport(data: any, options: ExportOptions): any {
    const report = {
      metadata: {
        generated_at: new Date().toISOString(),
        total_scraped_pages: data.scrapedData.length,
        total_seo_analyses: data.seoAnalysis.length,
        total_keywords: data.keywords.length,
        total_leads: data.leads.length,
        total_content_ideas: data.contentIdeas.length,
        total_social_posts: data.socialPosts.length,
        date_range: options.dateRange
      },
      summary: {
        avg_seo_score: data.seoAnalysis.reduce((sum: number, item: any) => sum + item.overall_score, 0) / data.seoAnalysis.length || 0,
        top_keywords: data.keywords
          .sort((a: any, b: any) => b.frequency - a.frequency)
          .slice(0, 10)
          .map((k: any) => ({ keyword: k.keyword, frequency: k.frequency })),
        verified_leads: data.leads.filter((l: any) => l.is_verified).length,
        content_ideas_by_type: this.groupBy(data.contentIdeas, 'content_type')
      },
      data: {
        scraped_data: this.processScrapedData(data.scrapedData, options),
        seo_analysis: this.processSEOData(data.seoAnalysis, options),
        keywords: this.processKeywordsData(data.keywords, options),
        leads: this.processLeadsData(data.leads, options),
        content_ideas: data.contentIdeas,
        social_posts: data.socialPosts
      }
    }

    return report
  }

  /**
   * Export to CSV format
   */
  private async exportToCSV(data: any[], options: ExportOptions): Promise<ExportResult> {
    if (data.length === 0) {
      throw new Error('No data to export')
    }

    const headers = Object.keys(data[0])
    const csvContent = [
      headers.join(','),
      ...data.map(row => 
        headers.map(header => {
          const value = row[header]
          // Escape commas and quotes in CSV
          if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
            return `"${value.replace(/"/g, '""')}"`
          }
          return value
        }).join(',')
      )
    ].join('\n')

    const filename = options.filename || `export_${Date.now()}.csv`
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    
    this.downloadBlob(blob, filename)

    return {
      success: true,
      filename,
      size: blob.size,
      recordCount: data.length
    }
  }

  /**
   * Export to JSON format
   */
  private async exportToJSON(data: any, options: ExportOptions): Promise<ExportResult> {
    const jsonContent = JSON.stringify(data, null, 2)
    const filename = options.filename || `export_${Date.now()}.json`
    const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' })
    
    this.downloadBlob(blob, filename)

    return {
      success: true,
      filename,
      size: blob.size,
      recordCount: Array.isArray(data) ? data.length : 1
    }
  }

  /**
   * Export to Excel format (simplified - would need a library like xlsx for full support)
   */
  private async exportToExcel(data: any[], options: ExportOptions): Promise<ExportResult> {
    // This is a simplified implementation
    // In a real application, you would use a library like 'xlsx' or 'exceljs'
    
    const csvContent = await this.exportToCSV(data, { ...options, format: 'csv' })
    const filename = options.filename?.replace('.xlsx', '.csv') || `export_${Date.now()}.csv`
    
    return {
      ...csvContent,
      filename
    }
  }

  /**
   * Export to PDF format (simplified - would need a library like jsPDF)
   */
  private async exportToPDF(data: any, options: ExportOptions): Promise<ExportResult> {
    // This is a simplified implementation
    // In a real application, you would use a library like 'jsPDF' or 'puppeteer'
    
    const content = typeof data === 'object' ? JSON.stringify(data, null, 2) : data
    const filename = options.filename || `export_${Date.now()}.txt`
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8;' })
    
    this.downloadBlob(blob, filename)

    return {
      success: true,
      filename,
      size: blob.size,
      recordCount: Array.isArray(data) ? data.length : 1
    }
  }

  /**
   * Download blob as file
   */
  private downloadBlob(blob: Blob, filename: string): void {
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  /**
   * Group array by property
   */
  private groupBy<T>(array: T[], key: keyof T): Record<string, number> {
    return array.reduce((groups: Record<string, number>, item) => {
      const value = String(item[key])
      groups[value] = (groups[value] || 0) + 1
      return groups
    }, {})
  }
}

// Export singleton instance
export const dataExporter = DataExporter.getInstance()

// Export utility functions
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export function getExportFormatIcon(format: ExportFormat): string {
  const icons = {
    csv: '📊',
    json: '📄',
    xlsx: '📈',
    pdf: '📋'
  }
  return icons[format] || '📄'
}

export function getExportFormatDescription(format: ExportFormat): string {
  const descriptions = {
    csv: 'Comma-separated values - Compatible with Excel and Google Sheets',
    json: 'JavaScript Object Notation - Structured data format',
    xlsx: 'Excel spreadsheet - Native Excel format with formatting',
    pdf: 'Portable Document Format - Print-ready document'
  }
  return descriptions[format] || 'Unknown format'
}
