// Test script to verify database tables are working
// Copy and paste this into your browser console (F12) while on localhost:5173

const testDatabaseTables = async () => {
  console.log('🔍 Testing database table connections...');
  
  try {
    // Import Supabase client
    const { supabase } = await import('./src/lib/supabase.js');
    
    const tables = [
      'notification_settings',
      'privacy_settings', 
      'security_settings',
      'user_settings'
    ];
    
    const results = {};
    
    for (const table of tables) {
      try {
        console.log(`Testing ${table}...`);
        
        // Test basic connection with count query
        const { data, error, count } = await supabase
          .from(table)
          .select('*', { count: 'exact', head: true });
        
        if (error) {
          results[table] = {
            status: 'ERROR',
            message: error.message,
            code: error.code
          };
          console.log(`❌ ${table}: ${error.message}`);
        } else {
          results[table] = {
            status: 'SUCCESS',
            count: count || 0
          };
          console.log(`✅ ${table}: Connected successfully (${count || 0} rows)`);
        }
      } catch (err) {
        results[table] = {
          status: 'EXCEPTION',
          message: err.message
        };
        console.log(`💥 ${table}: Exception - ${err.message}`);
      }
    }
    
    console.log('\n📊 Summary:');
    console.table(results);
    
    // Test if we can insert/read data
    console.log('\n🧪 Testing data operations...');
    
    try {
      // Get current user
      const { data: { user }, error: authError } = await supabase.auth.getUser();
      
      if (authError || !user) {
        console.log('⚠️ User not authenticated - skipping data operations test');
        return results;
      }
      
      console.log(`👤 Testing with user: ${user.email}`);
      
      // Test inserting default settings
      const testInsert = await supabase
        .from('user_settings')
        .upsert([{
          user_id: user.id,
          theme: 'system',
          language: 'en',
          timezone: 'America/New_York'
        }])
        .select();
      
      if (testInsert.error) {
        console.log(`❌ Insert test failed: ${testInsert.error.message}`);
      } else {
        console.log('✅ Insert/Update test successful');
      }
      
    } catch (err) {
      console.log(`💥 Data operations test failed: ${err.message}`);
    }
    
    return results;
    
  } catch (err) {
    console.error('💥 Failed to run database test:', err);
    return { error: err.message };
  }
};

// Run the test
testDatabaseTables().then(results => {
  console.log('\n🎉 Database test completed!');
  if (Object.values(results).every(r => r.status === 'SUCCESS')) {
    console.log('🎊 All tables are working correctly!');
  } else {
    console.log('⚠️ Some issues found - check the results above');
  }
});
