import { describe, it, expect, vi, beforeEach } from 'vitest'
import { WebScraper } from '../scraping'

describe('WebScraper', () => {
  let scraper: WebScraper
  const mockFetch = vi.fn()

  beforeEach(() => {
    scraper = new WebScraper()
    vi.clearAllMocks()
    // Reset fetch mock
    global.fetch = mockFetch
    mockFetch.mockClear()
  })

  describe('URL validation', () => {
    it('accepts valid HTTP URLs', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve('<html><head><title>Test</title></head><body><p>Content</p></body></html>'),
      } as Response)

      const result = await scraper.scrapeUrl('http://example.com')
      expect(result.url).toBe('http://example.com')
    })

    it('accepts valid HTTPS URLs', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve('<html><head><title>Test</title></head><body><p>Content</p></body></html>'),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com')
      expect(result.url).toBe('https://example.com')
    })

    it('rejects invalid protocols', async () => {
      const result = await scraper.scrapeUrl('ftp://example.com', { testMode: true })
      expect(result.error).toBeDefined()
      expect(result.error).toContain('Only HTTP and HTTPS URLs are supported')
    })

    it('rejects malformed URLs', async () => {
      const result = await scraper.scrapeUrl('not-a-url', { testMode: true })
      expect(result.error).toBeDefined()
    })
  })

  describe('HTML parsing', () => {
    it('extracts title correctly', async () => {
      const html = '<html><head><title>Test Page Title</title></head><body></body></html>'
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com')
      expect(result.title).toBe('Test Page Title')
    })

    it('extracts meta description', async () => {
      const html = `
        <html>
          <head>
            <title>Test</title>
            <meta name="description" content="Test description">
          </head>
          <body></body>
        </html>
      `
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com')
      expect(result.description).toBe('Test description')
    })

    it('extracts headings', async () => {
      const html = `
        <html>
          <head><title>Test</title></head>
          <body>
            <h1>Heading 1</h1>
            <h2>Heading 2</h2>
            <h3>Heading 3</h3>
          </body>
        </html>
      `
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com', { extractHeadings: true })
      expect(result.headings).toEqual(['Heading 1', 'Heading 2', 'Heading 3'])
    })

    it('extracts links', async () => {
      const html = `
        <html>
          <head><title>Test</title></head>
          <body>
            <a href="https://example.com/page1">Link 1</a>
            <a href="https://example.com/page2">Link 2</a>
          </body>
        </html>
      `
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com', { extractLinks: true })
      expect(result.links).toEqual(['https://example.com/page1', 'https://example.com/page2'])
    })

    it('extracts images', async () => {
      const html = `
        <html>
          <head><title>Test</title></head>
          <body>
            <img src="https://example.com/image1.jpg" alt="Image 1">
            <img src="https://example.com/image2.png" alt="Image 2">
          </body>
        </html>
      `
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com', { extractImages: true })
      expect(result.images).toEqual(['https://example.com/image1.jpg', 'https://example.com/image2.png'])
    })

    it('calculates word count', async () => {
      const html = `
        <html>
          <head><title>Test</title></head>
          <body>
            <p>This is a test paragraph with exactly ten words here.</p>
          </body>
        </html>
      `
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com')
      expect(result.wordCount).toBe(10)
    })

    it('extracts text content', async () => {
      const html = `
        <html>
          <head><title>Test</title></head>
          <body>
            <p>First paragraph.</p>
            <p>Second paragraph.</p>
          </body>
        </html>
      `
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com')
      expect(result.content).toContain('First paragraph.')
      expect(result.content).toContain('Second paragraph.')
    })
  })

  describe('error handling', () => {
    it('handles network errors', async () => {
      mockFetch.mockRejectedValueOnce(new Error('Network error'))

      const result = await scraper.scrapeUrl('https://example.com', { testMode: true })
      expect(result.error).toBeDefined()
    })

    it('handles HTTP errors', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com', { testMode: true })
      expect(result.error).toBeDefined()
      expect(result.error).toContain('404')
    })

    it('handles empty responses', async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(''),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com')
      expect(result.content).toBe('')
    })
  })

  describe('content length limits', () => {
    it('respects maxContentLength option', async () => {
      const longContent = 'a'.repeat(1000)
      const html = `<html><head><title>Test</title></head><body><p>${longContent}</p></body></html>`

      mockFetch.mockResolvedValueOnce({
        ok: true,
        text: () => Promise.resolve(html),
      } as Response)

      const result = await scraper.scrapeUrl('https://example.com', { maxContentLength: 100 })
      expect(result.content!.length).toBeLessThanOrEqual(100)
    })
  })

  describe('demo mode', () => {
    it('returns demo data for demo URLs', async () => {
      const result = await scraper.scrapeUrl('https://demo.example.com')
      expect(result.title).toContain('Demo')
      expect(result.content).toBeDefined()
      expect(result.error).toBeUndefined()
    })

    it('returns demo data for test URLs', async () => {
      const result = await scraper.scrapeUrl('https://test.example.com')
      expect(result.title).toContain('Demo')
      expect(result.content).toBeDefined()
      expect(result.error).toBeUndefined()
    })
  })

  describe('batch scraping', () => {
    it('scrapes multiple URLs', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve('<html><head><title>Test</title></head><body><p>Content</p></body></html>'),
      } as Response)

      const urls = ['https://example1.com', 'https://example2.com']
      const results = await scraper.scrapeUrls(urls)

      expect(results).toHaveLength(2)
      expect(results[0].url).toBe('https://example1.com')
      expect(results[1].url).toBe('https://example2.com')
    })

    it('handles mixed success and failure in batch', async () => {
      // Mock first call to succeed
      mockFetch
        .mockResolvedValueOnce({
          ok: true,
          text: () => Promise.resolve('<html><head><title>Success</title></head><body></body></html>'),
        } as Response)

      // Mock all subsequent calls (including proxy fallbacks) to fail
      for (let i = 0; i < 10; i++) {
        mockFetch.mockRejectedValueOnce(new Error('Network error'))
      }

      const urls = ['https://success.com', 'https://failure.com']
      const results = await scraper.scrapeUrls(urls, { testMode: true })

      expect(results).toHaveLength(2)
      expect(results[0].title).toBe('Success')
      expect(results[1].error).toBeDefined()
      expect(results[1].url).toBe('https://failure.com')
    })
  })
})
