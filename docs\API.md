# API Documentation

This document describes the API endpoints and integration capabilities of the MarketCrawler Pro application.

## Overview

MarketCrawler Pro provides a comprehensive REST API for programmatic access to web scraping, SEO analysis, and marketing tools. The API is built on Supabase and follows RESTful conventions.

## Authentication

### API Key Authentication

Premium users can generate API keys for programmatic access:

```bash
# Include API key in headers
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "Content-Type: application/json" \
     https://your-project.supabase.co/rest/v1/scraping_jobs
```

### JWT Authentication

For web applications, use Supabase JWT tokens:

```javascript
// Include JWT token
const { data, error } = await supabase
  .from('scraping_jobs')
  .select('*')
  .eq('user_id', user.id)
```

## Base URL

```
https://your-project.supabase.co/rest/v1/
```

## Rate Limits

- **Free Tier**: 100 requests per hour
- **Premium**: 1,000 requests per hour
- **Enterprise**: 10,000 requests per hour

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per hour
- `X-RateLimit-Remaining`: Remaining requests
- `X-RateLimit-Reset`: Reset time (Unix timestamp)

## Endpoints

### Scraping Jobs

#### Create Scraping Job

```http
POST /scraping_jobs
```

**Request Body:**
```json
{
  "url": "https://example.com",
  "user_agent": "MarketCrawler-Pro/1.0",
  "timeout_seconds": 30,
  "max_retries": 3,
  "priority": 0
}
```

**Response:**
```json
{
  "id": "uuid",
  "url": "https://example.com",
  "status": "pending",
  "created_at": "2024-01-01T00:00:00Z",
  "user_agent": "MarketCrawler-Pro/1.0",
  "timeout_seconds": 30,
  "max_retries": 3,
  "priority": 0
}
```

#### Get Scraping Job

```http
GET /scraping_jobs/{id}
```

**Response:**
```json
{
  "id": "uuid",
  "url": "https://example.com",
  "status": "completed",
  "created_at": "2024-01-01T00:00:00Z",
  "completed_at": "2024-01-01T00:01:00Z",
  "results": {
    "title": "Example Page",
    "content": "Page content...",
    "word_count": 150,
    "metadata": {}
  },
  "error_message": null,
  "retry_count": 0
}
```

#### List Scraping Jobs

```http
GET /scraping_jobs?status=eq.completed&order=created_at.desc&limit=10
```

**Query Parameters:**
- `status`: Filter by status (`pending`, `running`, `completed`, `failed`)
- `order`: Sort order (e.g., `created_at.desc`)
- `limit`: Number of results (max 100)
- `offset`: Pagination offset

### Scraped Data

#### Get Scraped Data

```http
GET /scraped_data/{id}
```

**Response:**
```json
{
  "id": "uuid",
  "job_id": "uuid",
  "url": "https://example.com",
  "title": "Example Page",
  "content": "Full page content...",
  "description": "Meta description",
  "metadata": {
    "headings": ["H1", "H2"],
    "links": ["https://link1.com"],
    "images": ["https://image1.jpg"],
    "word_count": 150
  },
  "scraped_at": "2024-01-01T00:01:00Z",
  "content_hash": "abc123",
  "word_count": 150,
  "language": "en"
}
```

#### Search Scraped Data

```http
GET /scraped_data?content=ilike.*keyword*&order=scraped_at.desc
```

### SEO Analysis

#### Get SEO Analysis

```http
GET /seo_analysis/{id}
```

**Response:**
```json
{
  "id": "uuid",
  "scraped_data_id": "uuid",
  "overall_score": 75,
  "title_score": 80,
  "content_score": 70,
  "technical_score": 75,
  "keyword_score": 80,
  "analysis_details": {
    "titleAnalysis": {
      "score": 80,
      "issues": [],
      "recommendations": ["Add more descriptive keywords"]
    },
    "contentAnalysis": {
      "score": 70,
      "wordCount": 150,
      "readabilityScore": 75,
      "issues": ["Content could be longer"],
      "recommendations": ["Add more detailed content"]
    }
  },
  "recommendations": ["Improve content length"],
  "issues": ["Content is too short"],
  "analyzed_at": "2024-01-01T00:02:00Z"
}
```

#### Trigger SEO Analysis

```http
POST /seo_analysis
```

**Request Body:**
```json
{
  "scraped_data_id": "uuid"
}
```

### Keywords

#### Get Keywords

```http
GET /keywords?scraped_data_id=eq.uuid&order=frequency.desc
```

**Response:**
```json
[
  {
    "id": "uuid",
    "scraped_data_id": "uuid",
    "keyword": "example",
    "frequency": 5,
    "density": 2.5,
    "context": "This is an example keyword in context",
    "position_first": 10,
    "position_last": 100,
    "is_title_keyword": true,
    "is_heading_keyword": false,
    "created_at": "2024-01-01T00:02:00Z"
  }
]
```

### Leads

#### Get Leads

```http
GET /leads?scraped_data_id=eq.uuid&is_verified=eq.true
```

**Response:**
```json
[
  {
    "id": "uuid",
    "scraped_data_id": "uuid",
    "email": "<EMAIL>",
    "name": "John Doe",
    "company": "Example Corp",
    "phone": "+1234567890",
    "social_profiles": {
      "linkedin": "https://linkedin.com/in/johndoe",
      "twitter": "@johndoe"
    },
    "context": "Found in contact section",
    "confidence_score": 0.95,
    "is_verified": true,
    "created_at": "2024-01-01T00:02:00Z"
  }
]
```

### Export Jobs

#### Create Export Job

```http
POST /export_jobs
```

**Request Body:**
```json
{
  "export_type": "scraped_data",
  "format": "csv",
  "filters": {
    "date_range": {
      "start": "2024-01-01",
      "end": "2024-01-31"
    },
    "min_seo_score": 70
  }
}
```

**Response:**
```json
{
  "id": "uuid",
  "export_type": "scraped_data",
  "format": "csv",
  "status": "pending",
  "created_at": "2024-01-01T00:00:00Z",
  "expires_at": "2024-01-08T00:00:00Z"
}
```

#### Get Export Job Status

```http
GET /export_jobs/{id}
```

**Response:**
```json
{
  "id": "uuid",
  "export_type": "scraped_data",
  "format": "csv",
  "status": "completed",
  "file_url": "https://storage.example.com/exports/file.csv",
  "file_size": 1024000,
  "record_count": 500,
  "created_at": "2024-01-01T00:00:00Z",
  "completed_at": "2024-01-01T00:05:00Z",
  "expires_at": "2024-01-08T00:00:00Z"
}
```

## Webhooks

### Webhook Events

MarketCrawler Pro can send webhooks for the following events:

- `scraping_job.completed`
- `scraping_job.failed`
- `seo_analysis.completed`
- `export_job.completed`
- `lead.discovered`

### Webhook Payload

```json
{
  "event": "scraping_job.completed",
  "timestamp": "2024-01-01T00:01:00Z",
  "data": {
    "id": "uuid",
    "url": "https://example.com",
    "status": "completed",
    "results": {}
  },
  "user_id": "uuid"
}
```

### Webhook Security

Webhooks are signed with HMAC-SHA256. Verify the signature using the webhook secret:

```javascript
const crypto = require('crypto')

function verifyWebhook(payload, signature, secret) {
  const expectedSignature = crypto
    .createHmac('sha256', secret)
    .update(payload)
    .digest('hex')
  
  return signature === `sha256=${expectedSignature}`
}
```

## SDKs and Libraries

### JavaScript/TypeScript

```bash
npm install @marketcrawler/sdk
```

```javascript
import { MarketCrawlerClient } from '@marketcrawler/sdk'

const client = new MarketCrawlerClient({
  apiKey: 'your-api-key',
  baseUrl: 'https://your-project.supabase.co'
})

// Create scraping job
const job = await client.scraping.create({
  url: 'https://example.com'
})

// Get results
const data = await client.scraping.getResults(job.id)
```

### Python

```bash
pip install marketcrawler-sdk
```

```python
from marketcrawler import MarketCrawlerClient

client = MarketCrawlerClient(
    api_key='your-api-key',
    base_url='https://your-project.supabase.co'
)

# Create scraping job
job = client.scraping.create(url='https://example.com')

# Get results
data = client.scraping.get_results(job.id)
```

### cURL Examples

#### Scrape a Website

```bash
curl -X POST \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com"}' \
  https://your-project.supabase.co/rest/v1/scraping_jobs
```

#### Get SEO Analysis

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
  https://your-project.supabase.co/rest/v1/seo_analysis?scraped_data_id=eq.UUID
```

## Error Handling

### Error Response Format

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid URL format",
    "details": {
      "field": "url",
      "value": "invalid-url"
    }
  }
}
```

### Common Error Codes

- `AUTHENTICATION_ERROR`: Invalid or missing API key
- `AUTHORIZATION_ERROR`: Insufficient permissions
- `VALIDATION_ERROR`: Invalid request data
- `RATE_LIMIT_EXCEEDED`: Too many requests
- `RESOURCE_NOT_FOUND`: Requested resource doesn't exist
- `INTERNAL_ERROR`: Server error

## Best Practices

### 1. Use Appropriate Timeouts

```javascript
// Set reasonable timeouts
const job = await client.scraping.create({
  url: 'https://example.com',
  timeout_seconds: 30
})
```

### 2. Handle Rate Limits

```javascript
// Check rate limit headers
const response = await fetch('/api/endpoint')
const remaining = response.headers.get('X-RateLimit-Remaining')

if (remaining < 10) {
  // Slow down requests
  await new Promise(resolve => setTimeout(resolve, 1000))
}
```

### 3. Use Webhooks for Long-Running Operations

```javascript
// Instead of polling
const job = await client.scraping.create({
  url: 'https://example.com',
  webhook_url: 'https://your-app.com/webhooks/scraping'
})
```

### 4. Implement Proper Error Handling

```javascript
try {
  const result = await client.scraping.create({ url })
} catch (error) {
  if (error.code === 'RATE_LIMIT_EXCEEDED') {
    // Wait and retry
    await new Promise(resolve => setTimeout(resolve, error.retryAfter * 1000))
    return client.scraping.create({ url })
  }
  throw error
}
```

## Support

- **Documentation**: [https://docs.marketcrawler.com](https://docs.marketcrawler.com)
- **API Status**: [https://status.marketcrawler.com](https://status.marketcrawler.com)
- **Support**: [<EMAIL>](mailto:<EMAIL>)
- **Community**: [https://community.marketcrawler.com](https://community.marketcrawler.com)
