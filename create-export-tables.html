<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Create Export Tables - Fix keyword_searches 404 Error</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 10px;
        }
        .alert {
            padding: 15px;
            margin: 20px 0;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
            background: #f0f8f0;
        }
        .error {
            border-left-color: #f44336;
            background: #fff5f5;
        }
        .warning {
            border-left-color: #ff9800;
            background: #fff8f0;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #45a049;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .sql-preview {
            background: #f8f8f8;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
        }
        .success { color: #4CAF50; }
        .error-text { color: #f44336; }
        .warning-text { color: #ff9800; }
        .info { color: #2196F3; }
        input {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .input-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Create Export Tables - Fix keyword_searches 404 Error</h1>
        
        <div class="alert">
            <strong>Purpose:</strong> This tool creates the missing <code>keyword_searches</code> table that's causing 404 errors in your KeywordResearch component.
        </div>

        <div class="alert warning">
            <strong>⚠️ Important:</strong> Make sure you have the correct Supabase credentials below. The default values are from your existing configuration.
        </div>

        <div class="input-group">
            <label for="supabaseUrl">Supabase URL:</label>
            <input type="text" id="supabaseUrl" value="https://rclikclltlyzyojjttqv.supabase.co" placeholder="https://your-project.supabase.co">
        </div>

        <div class="input-group">
            <label for="supabaseKey">Supabase Anon Key:</label>
            <input type="password" id="supabaseKey" value="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NjE1NzQsImV4cCI6MjA1MDUzNzU3NH0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw" placeholder="Your anon key">
        </div>

        <div style="margin: 20px 0;">
            <button onclick="createExportTables()">🚀 Create Export Tables</button>
            <button onclick="testKeywordSearches()">🧪 Test keyword_searches Table</button>
            <button onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <h3>📋 SQL Preview</h3>
        <div class="sql-preview">
-- Keyword Searches Table
-- Stores search history for keyword research functionality
CREATE TABLE IF NOT EXISTS keyword_searches (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    search_term TEXT NOT NULL,
    result_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_result_count CHECK (result_count >= 0)
);

-- Export Jobs Table
-- Tracks export operations and their status
CREATE TABLE IF NOT EXISTS export_jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    export_type VARCHAR(50) NOT NULL CHECK (export_type IN ('csv', 'json', 'xlsx', 'pdf')),
    table_name VARCHAR(100) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
    file_path TEXT,
    file_size BIGINT,
    row_count INTEGER DEFAULT 0,
    filters JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_by UUID,
    
    -- Constraints
    CONSTRAINT valid_file_size CHECK (file_size >= 0),
    CONSTRAINT valid_row_count CHECK (row_count >= 0)
);

-- Export Data Table
-- Stores metadata about exported files
CREATE TABLE IF NOT EXISTS export_data (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    export_job_id UUID NOT NULL REFERENCES export_jobs(id) ON DELETE CASCADE,
    original_table VARCHAR(100) NOT NULL,
    exported_rows INTEGER DEFAULT 0,
    file_format VARCHAR(10) NOT NULL,
    compression_used BOOLEAN DEFAULT FALSE,
    download_count INTEGER DEFAULT 0,
    last_downloaded_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_exported_rows CHECK (exported_rows >= 0),
    CONSTRAINT valid_download_count CHECK (download_count >= 0)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_keyword_searches_search_term ON keyword_searches(search_term);
CREATE INDEX IF NOT EXISTS idx_keyword_searches_created_at ON keyword_searches(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_export_jobs_status ON export_jobs(status);
CREATE INDEX IF NOT EXISTS idx_export_jobs_created_at ON export_jobs(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_export_data_export_job_id ON export_data(export_job_id);
        </div>

        <h3>📊 Execution Log</h3>
        <div id="log" class="log">
Ready to create export tables...<br>
        </div>
    </div>

    <script type="module">
        // Import Supabase
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        let supabase = null;

        // Make functions available globally
        window.createExportTables = createExportTables;
        window.testKeywordSearches = testKeywordSearches;
        window.clearLog = clearLog;

        function log(message, type = 'info') {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error-text' : 
                             type === 'success' ? 'success' : 
                             type === 'warning' ? 'warning-text' : 'info';
            logElement.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span><br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = 'Log cleared...<br>';
        }

        function initSupabase() {
            const url = document.getElementById('supabaseUrl').value.trim();
            const key = document.getElementById('supabaseKey').value.trim();

            if (!url || !key) {
                log('❌ Please enter both Supabase URL and key', 'error');
                return false;
            }

            try {
                supabase = createClient(url, key);
                log('✅ Supabase client initialized', 'success');
                return true;
            } catch (error) {
                log(`❌ Failed to initialize Supabase: ${error.message}`, 'error');
                return false;
            }
        }

        async function createExportTables() {
            if (!initSupabase()) return;

            log('🔍 Creating export tables...', 'info');

            const sqlStatements = [
                `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`,
                
                `CREATE TABLE IF NOT EXISTS keyword_searches (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    search_term TEXT NOT NULL,
                    result_count INTEGER DEFAULT 0,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    CONSTRAINT valid_result_count CHECK (result_count >= 0)
                );`,
                
                `CREATE TABLE IF NOT EXISTS export_jobs (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    export_type VARCHAR(50) NOT NULL CHECK (export_type IN ('csv', 'json', 'xlsx', 'pdf')),
                    table_name VARCHAR(100) NOT NULL,
                    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
                    file_path TEXT,
                    file_size BIGINT,
                    row_count INTEGER DEFAULT 0,
                    filters JSONB DEFAULT '{}',
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    started_at TIMESTAMP WITH TIME ZONE,
                    completed_at TIMESTAMP WITH TIME ZONE,
                    error_message TEXT,
                    created_by UUID,
                    CONSTRAINT valid_file_size CHECK (file_size >= 0),
                    CONSTRAINT valid_row_count CHECK (row_count >= 0)
                );`,
                
                `CREATE TABLE IF NOT EXISTS export_data (
                    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
                    export_job_id UUID NOT NULL REFERENCES export_jobs(id) ON DELETE CASCADE,
                    original_table VARCHAR(100) NOT NULL,
                    exported_rows INTEGER DEFAULT 0,
                    file_format VARCHAR(10) NOT NULL,
                    compression_used BOOLEAN DEFAULT FALSE,
                    download_count INTEGER DEFAULT 0,
                    last_downloaded_at TIMESTAMP WITH TIME ZONE,
                    expires_at TIMESTAMP WITH TIME ZONE,
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    CONSTRAINT valid_exported_rows CHECK (exported_rows >= 0),
                    CONSTRAINT valid_download_count CHECK (download_count >= 0)
                );`,
                
                `CREATE INDEX IF NOT EXISTS idx_keyword_searches_search_term ON keyword_searches(search_term);`,
                `CREATE INDEX IF NOT EXISTS idx_keyword_searches_created_at ON keyword_searches(created_at DESC);`,
                `CREATE INDEX IF NOT EXISTS idx_export_jobs_status ON export_jobs(status);`,
                `CREATE INDEX IF NOT EXISTS idx_export_jobs_created_at ON export_jobs(created_at DESC);`,
                `CREATE INDEX IF NOT EXISTS idx_export_data_export_job_id ON export_data(export_job_id);`
            ];

            let successCount = 0;
            let errorCount = 0;

            for (const [index, sql] of sqlStatements.entries()) {
                try {
                    log(`📝 Executing statement ${index + 1}/${sqlStatements.length}...`);
                    const { error } = await supabase.rpc('exec_sql', { sql });
                    
                    if (error) {
                        log(`❌ Error in statement ${index + 1}: ${error.message}`, 'error');
                        errorCount++;
                    } else {
                        log(`✅ Statement ${index + 1} executed successfully`, 'success');
                        successCount++;
                    }
                } catch (error) {
                    log(`❌ Exception in statement ${index + 1}: ${error.message}`, 'error');
                    errorCount++;
                }
            }

            log(`🎯 Summary: ${successCount} successful, ${errorCount} errors`, successCount > errorCount ? 'success' : 'warning');
            
            if (successCount > 0) {
                log('🧪 Testing keyword_searches table...', 'info');
                await testKeywordSearches();
            }
        }

        async function testKeywordSearches() {
            if (!initSupabase()) return;

            try {
                log('🔍 Testing keyword_searches table...', 'info');
                
                // Test basic select
                const { data, error } = await supabase
                    .from('keyword_searches')
                    .select('*')
                    .limit(1);

                if (error) {
                    log(`❌ Error testing keyword_searches: ${error.message}`, 'error');
                    return;
                }

                log('✅ keyword_searches table is accessible!', 'success');
                
                // Test insert
                const testData = {
                    search_term: 'test keyword',
                    result_count: 5,
                    created_at: new Date().toISOString()
                };

                const { data: insertData, error: insertError } = await supabase
                    .from('keyword_searches')
                    .insert([testData])
                    .select();

                if (insertError) {
                    log(`❌ Error inserting test data: ${insertError.message}`, 'error');
                } else {
                    log('✅ Test insert successful!', 'success');
                    log(`📊 Inserted record: ${JSON.stringify(insertData[0])}`, 'info');
                    
                    // Clean up test data
                    await supabase
                        .from('keyword_searches')
                        .delete()
                        .eq('search_term', 'test keyword');
                    
                    log('🧹 Test data cleaned up', 'info');
                }

                log('🎉 keyword_searches table is fully functional!', 'success');
                log('💡 Your KeywordResearch component should now work without 404 errors.', 'success');

            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        // Initialize on page load
        log('🚀 Export Tables Creator loaded. Ready to fix keyword_searches 404 error!', 'success');
    </script>
</body>
</html>
