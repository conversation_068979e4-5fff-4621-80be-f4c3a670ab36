import { test, expect } from '@playwright/test'

test.describe('Scraping Analysis Marketing App', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/')
  })

  test('should load the homepage', async ({ page }) => {
    // Check if the main heading is visible
    await expect(page.getByText('MarketCrawler Pro - Web Intelligence Suite')).toBeVisible()
    
    // Check if the main navigation tabs are present
    await expect(page.getByRole('tab', { name: 'Web Scraper' })).toBeVisible()
    await expect(page.getByRole('tab', { name: 'Analytics' })).toBeVisible()
    await expect(page.getByRole('tab', { name: 'Data Results' })).toBeVisible()
    await expect(page.getByRole('tab', { name: 'Marketing Tools' })).toBeVisible()
  })

  test('should navigate between tabs', async ({ page }) => {
    // Click on Analytics tab
    await page.getByRole('tab', { name: 'Analytics' }).click()
    await expect(page.getByText('Analytics Dashboard')).toBeVisible()

    // Click on Data Results tab
    await page.getByRole('tab', { name: 'Data Results' }).click()
    await expect(page.getByText('Scraped Data Results')).toBeVisible()

    // Click on Marketing Tools tab
    await page.getByRole('tab', { name: 'Marketing Tools' }).click()
    await expect(page.getByText('Marketing Tools')).toBeVisible()

    // Go back to Web Scraper tab
    await page.getByRole('tab', { name: 'Web Scraper' }).click()
    await expect(page.getByText('Web Scraper')).toBeVisible()
  })

  test('should display scraper form', async ({ page }) => {
    // Check if scraper form elements are present
    await expect(page.getByPlaceholder(/Enter URLs to scrape/)).toBeVisible()
    await expect(page.getByRole('button', { name: /Start Scraping/i })).toBeVisible()
  })

  test('should validate URL input', async ({ page }) => {
    const urlInput = page.getByPlaceholder(/Enter URLs to scrape/)
    const submitButton = page.getByRole('button', { name: /Start Scraping/i })

    // Try to submit without any URLs
    await submitButton.click()
    await expect(page.getByText(/Please enter at least one URL/)).toBeVisible()

    // Try to submit with invalid URL
    await urlInput.fill('invalid-url')
    await submitButton.click()
    await expect(page.getByText(/Please enter valid URLs/)).toBeVisible()
  })

  test('should accept valid URLs', async ({ page }) => {
    const urlInput = page.getByPlaceholder(/Enter URLs to scrape/)
    const submitButton = page.getByRole('button', { name: /Start Scraping/i })

    // Enter a valid URL
    await urlInput.fill('https://example.com')
    await submitButton.click()

    // Should show scraping in progress
    await expect(page.getByText(/Scraping in progress/)).toBeVisible()
  })

  test('should display analytics dashboard', async ({ page }) => {
    await page.getByRole('tab', { name: 'Analytics' }).click()

    // Check for analytics cards
    await expect(page.getByText('Total Jobs')).toBeVisible()
    await expect(page.getByText('Completed Jobs')).toBeVisible()
    await expect(page.getByText('Total Data')).toBeVisible()
    await expect(page.getByText('Avg Word Count')).toBeVisible()

    // Check for SEO analysis section
    await expect(page.getByText('SEO Analysis')).toBeVisible()
  })

  test('should display data table', async ({ page }) => {
    await page.getByRole('tab', { name: 'Data Results' }).click()

    // Check for data table elements
    await expect(page.getByText('Scraped Data Results')).toBeVisible()
    await expect(page.getByPlaceholder('Search URLs, titles...')).toBeVisible()
    
    // Check for table headers
    await expect(page.getByText('URL')).toBeVisible()
    await expect(page.getByText('Title')).toBeVisible()
    await expect(page.getByText('Status')).toBeVisible()
  })

  test('should display marketing tools', async ({ page }) => {
    await page.getByRole('tab', { name: 'Marketing Tools' }).click()

    // Check for marketing tool tabs
    await expect(page.getByText('SEO Keywords')).toBeVisible()
    await expect(page.getByText('Lead Generation')).toBeVisible()
    await expect(page.getByText('Content Ideas')).toBeVisible()
    await expect(page.getByText('Social Posts')).toBeVisible()
  })

  test('should be responsive on mobile', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })

    // Check if the page still loads correctly
    await expect(page.getByText('MarketCrawler Pro')).toBeVisible()
    
    // Check if navigation is accessible (might be in a mobile menu)
    await expect(page.getByRole('tab', { name: 'Web Scraper' })).toBeVisible()
  })

  test('should handle theme switching', async ({ page }) => {
    // Look for theme toggle button (if implemented)
    const themeToggle = page.locator('[data-testid="theme-toggle"]')
    
    if (await themeToggle.isVisible()) {
      await themeToggle.click()
      
      // Check if theme changed (this would depend on your implementation)
      const body = page.locator('body')
      await expect(body).toHaveClass(/dark/)
    }
  })

  test('should display error boundaries correctly', async ({ page }) => {
    // This test would need to trigger an error condition
    // For now, just check that the page loads without errors
    const errors = []
    page.on('pageerror', error => errors.push(error))
    
    await page.reload()
    
    // Should not have any uncaught errors
    expect(errors).toHaveLength(0)
  })

  test('should have proper accessibility', async ({ page }) => {
    // Check for basic accessibility features
    await expect(page.locator('main')).toBeVisible()
    
    // Check for proper heading hierarchy
    await expect(page.locator('h1, h2, h3')).toHaveCount(1) // At least one heading
    
    // Check for proper form labels
    const urlInput = page.getByPlaceholder(/Enter URLs to scrape/)
    await expect(urlInput).toBeVisible()
  })
})
