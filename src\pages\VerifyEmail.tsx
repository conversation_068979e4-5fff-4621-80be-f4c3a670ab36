import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Mail } from 'lucide-react';
import { supabase } from '@/lib/supabase';

const VerifyEmail: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [verificationStatus, setVerificationStatus] = useState<'pending' | 'success' | 'error'>('pending');

  useEffect(() => {
    const handleEmailVerification = async () => {
      try {
        const accessToken = searchParams.get('access_token');
        const refreshToken = searchParams.get('refresh_token');
        const type = searchParams.get('type');
        const tokenHash = searchParams.get('token_hash');

        // Handle different verification types
        if (type === 'signup' || type === 'email_change') {
          if (accessToken && refreshToken) {
            // Set the session with the tokens
            const { error } = await supabase.auth.setSession({
              access_token: accessToken,
              refresh_token: refreshToken
            });

            if (error) {
              setError('Email verification failed. The link may be invalid or expired.');
              setVerificationStatus('error');
            } else {
              setSuccess(true);
              setVerificationStatus('success');
              
              // Redirect to main app after successful verification
              setTimeout(() => {
                navigate('/', { replace: true });
              }, 3000);
            }
          } else if (tokenHash) {
            // Handle token hash verification
            const { error } = await supabase.auth.verifyOtp({
              token_hash: tokenHash,
              type: type as 'signup' | 'email_change'
            });

            if (error) {
              setError('Email verification failed. The link may be invalid or expired.');
              setVerificationStatus('error');
            } else {
              setSuccess(true);
              setVerificationStatus('success');
              
              setTimeout(() => {
                navigate('/', { replace: true });
              }, 3000);
            }
          } else {
            setError('Invalid verification link. Missing required parameters.');
            setVerificationStatus('error');
          }
        } else {
          setError('Invalid verification type.');
          setVerificationStatus('error');
        }
      } catch (err) {
        setError('An error occurred during email verification.');
        setVerificationStatus('error');
      } finally {
        setLoading(false);
      }
    };

    // Only process if we have URL parameters
    if (searchParams.toString()) {
      handleEmailVerification();
    } else {
      setLoading(false);
      setVerificationStatus('error');
      setError('No verification parameters found in URL.');
    }
  }, [searchParams, navigate]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
              <Mail className="h-6 w-6 text-blue-600" />
            </div>
            <CardTitle className="text-xl">Verifying Email</CardTitle>
            <CardDescription>
              Please wait while we verify your email address
            </CardDescription>
          </CardHeader>
          <CardContent className="flex items-center justify-center py-8">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Verifying your email...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (verificationStatus === 'success') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <CardTitle className="text-xl">Email Verified</CardTitle>
            <CardDescription>
              Your email has been successfully verified
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert className="mb-4 border-green-200 bg-green-50">
              <AlertDescription className="text-green-700">
                Welcome! Your account is now active. You'll be redirected to the application shortly.
              </AlertDescription>
            </Alert>
            <div className="text-center">
              <Button
                onClick={() => navigate('/', { replace: true })}
                className="w-full"
              >
                Continue to Application
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
            <XCircle className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Verification Failed</CardTitle>
          <CardDescription>
            We couldn't verify your email address
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert className="mb-4 border-red-200 bg-red-50">
            <AlertDescription className="text-red-700">
              {error || 'The verification link is invalid or has expired.'}
            </AlertDescription>
          </Alert>
          
          <div className="space-y-3">
            <Button
              onClick={() => navigate('/', { replace: true })}
              className="w-full"
            >
              Return to Sign In
            </Button>
            
            <div className="text-center text-sm text-gray-600">
              <p>Need help? Contact support or try signing up again.</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default VerifyEmail;
