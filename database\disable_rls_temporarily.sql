-- Temporary fix: Disable <PERSON><PERSON> to test if that's causing the 406 errors
-- Run this in Supabase SQL Editor to temporarily disable Row Level Security

-- Disable <PERSON><PERSON> temporarily for testing
ALTER TABLE user_profiles DISABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE notification_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE security_settings DISABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys DISABLE ROW LEVEL SECURITY;
ALTER TABLE notifications DISABLE ROW LEVEL SECURITY;
ALTER TABLE saved_keywords DISABLE ROW LEVEL SECURITY;

-- Insert some test data to ensure tables have records
INSERT INTO user_profiles (first_name, last_name, email) VALUES 
('Test', 'User', '<EMAIL>')
ON CONFLICT (email) DO NOTHING;

-- Get the user ID for test data
DO $$
DECLARE
    test_user_id UUID;
BEGIN
    SELECT id INTO test_user_id FROM user_profiles WHERE email = '<EMAIL>' LIMIT 1;
    
    IF test_user_id IS NOT NULL THEN
        -- Insert test settings
        INSERT INTO user_settings (user_id) VALUES (test_user_id)
        ON CONFLICT (user_id) DO NOTHING;
        
        INSERT INTO notification_settings (user_id) VALUES (test_user_id)
        ON CONFLICT (user_id) DO NOTHING;
        
        INSERT INTO privacy_settings (user_id) VALUES (test_user_id)
        ON CONFLICT (user_id) DO NOTHING;
        
        INSERT INTO security_settings (user_id) VALUES (test_user_id)
        ON CONFLICT (user_id) DO NOTHING;
    END IF;
END $$;
