<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced 406 Fix - Supabase Diagnostics</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            border: 1px solid rgba(255,255,255,0.2);
        }
        .button {
            background: linear-gradient(45deg, #4CAF50, #45a049);
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
        }
        .button.danger {
            background: linear-gradient(45deg, #f44336, #da190b);
            box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
        }
        .button.info {
            background: linear-gradient(45deg, #2196F3, #1976D2);
            box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
        }
        .result {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            max-height: 500px;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.5;
        }
        .success {
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background: linear-gradient(135deg, #f8d7da, #f5c6cb);
            border-color: #dc3545;
            color: #721c24;
        }
        .warning {
            background: linear-gradient(135deg, #fff3cd, #ffeaa7);
            border-color: #ffc107;
            color: #856404;
        }
        .status {
            font-weight: bold;
            padding: 8px 16px;
            border-radius: 20px;
            display: inline-block;
            margin: 8px 0;
            font-size: 14px;
        }
        .status.connected {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }
        .status.disconnected {
            background: linear-gradient(45deg, #dc3545, #e74c3c);
            color: white;
        }
        .diagnostic-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .diagnostic-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <h1>🔧 Enhanced 406 Fix - Supabase Diagnostics</h1>
    
    <div class="container">
        <h2>🔐 Authentication & Connection Status</h2>
        <div id="authStatus" class="status disconnected">Not Connected</div>
        <div>
            <button class="button" onclick="testConnection()">🔍 Test Connection</button>
            <button class="button" onclick="signInAnonymously()">👤 Sign In Anonymously</button>
            <button class="button info" onclick="testHeaders()">📋 Test Headers</button>
            <button class="button danger" onclick="signOut()">🚪 Sign Out</button>
        </div>
        <div id="authResult" class="result" style="display: none;"></div>
    </div>

    <div class="diagnostic-grid">
        <div class="diagnostic-card">
            <h3>🧪 User Settings Tests</h3>
            <button class="button" onclick="testUserSettings()">Test User Settings</button>
            <button class="button" onclick="createTestSettings()">Create Settings</button>
            <button class="button info" onclick="testWithDifferentHeaders()">Test Headers Fix</button>
            <div id="settingsResult" class="result" style="display: none;"></div>
        </div>

        <div class="diagnostic-card">
            <h3>🗄️ Database Structure</h3>
            <button class="button" onclick="checkTableStructure()">Check Tables</button>
            <button class="button" onclick="testRLSPolicies()">Test RLS</button>
            <button class="button info" onclick="checkForeignKeys()">Check FK</button>
            <div id="dbResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <h2>🛠️ Advanced Diagnostics</h2>
        <div>
            <button class="button info" onclick="runFullDiagnostic()">🔍 Full Diagnostic</button>
            <button class="button" onclick="testAllEndpoints()">🌐 Test All Endpoints</button>
            <button class="button" onclick="clearAllResults()">🧹 Clear All</button>
        </div>
        <div id="diagnosticResult" class="result" style="display: none;"></div>
    </div>

    <script type="module">
        // Import Supabase with enhanced configuration
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Initialize Supabase client with enhanced headers to fix 406 errors
        const supabaseUrl = 'https://rclikclltlyzyojjttqv.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTYzOTIsImV4cCI6MjA2NTk3MjM5Mn0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw';
        
        const supabase = createClient(supabaseUrl, supabaseKey, {
            auth: {
                autoRefreshToken: true,
                persistSession: true,
                detectSessionInUrl: true
            },
            global: {
                headers: {
                    'Accept': 'application/json, application/vnd.pgrst.object+json',
                    'Content-Type': 'application/json',
                    'Prefer': 'return=representation'
                },
            },
            db: {
                schema: 'public'
            }
        });

        // Helper function to show loading state
        function showLoading(elementId, message = 'Loading...') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = `<div class="loading"></div>${message}`;
            element.className = 'result';
        }

        // Helper function to show result
        function showResult(elementId, message, type = 'success') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // Global functions
        window.testConnection = async function() {
            const authStatus = document.getElementById('authStatus');
            
            try {
                showLoading('authResult', 'Testing connection...');
                
                const { data, error } = await supabase.auth.getSession();
                
                if (error) {
                    throw error;
                }
                
                authStatus.textContent = data.session ? 'Connected & Authenticated' : 'Connected (Not Authenticated)';
                authStatus.className = data.session ? 'status connected' : 'status disconnected';
                
                const result = `✅ Connection successful!
Session: ${data.session ? 'Active' : 'None'}
User: ${data.session?.user?.email || 'None'}
User ID: ${data.session?.user?.id || 'None'}
Timestamp: ${new Date().toISOString()}`;
                
                showResult('authResult', result, 'success');
                
            } catch (error) {
                authStatus.textContent = 'Connection Failed';
                authStatus.className = 'status disconnected';
                showResult('authResult', `❌ Connection failed: ${error.message}`, 'error');
            }
        };

        window.signInAnonymously = async function() {
            try {
                showLoading('authResult', 'Signing in anonymously...');
                
                const { data, error } = await supabase.auth.signInAnonymously();
                
                if (error) {
                    throw error;
                }
                
                document.getElementById('authStatus').textContent = 'Connected & Authenticated (Anonymous)';
                document.getElementById('authStatus').className = 'status connected';
                
                const result = `✅ Anonymous sign-in successful!
User ID: ${data.user.id}
Email: ${data.user.email || 'Anonymous'}
Created: ${data.user.created_at}`;
                
                showResult('authResult', result, 'success');
                
            } catch (error) {
                showResult('authResult', `❌ Anonymous sign-in failed: ${error.message}`, 'error');
            }
        };

        window.signOut = async function() {
            try {
                showLoading('authResult', 'Signing out...');
                
                const { error } = await supabase.auth.signOut();
                
                if (error) {
                    throw error;
                }
                
                document.getElementById('authStatus').textContent = 'Not Connected';
                document.getElementById('authStatus').className = 'status disconnected';
                
                showResult('authResult', '✅ Signed out successfully!', 'success');
                
            } catch (error) {
                showResult('authResult', `❌ Sign out failed: ${error.message}`, 'error');
            }
        };

        window.testHeaders = async function() {
            try {
                showLoading('authResult', 'Testing headers configuration...');
                
                // Test with different header combinations
                const tests = [
                    {
                        name: 'Standard JSON',
                        headers: { 'Accept': 'application/json' }
                    },
                    {
                        name: 'PostgREST Object',
                        headers: { 'Accept': 'application/vnd.pgrst.object+json' }
                    },
                    {
                        name: 'Combined Accept',
                        headers: { 'Accept': 'application/json, application/vnd.pgrst.object+json' }
                    }
                ];
                
                let results = 'Header Test Results:\n\n';
                
                for (const test of tests) {
                    try {
                        const response = await fetch(`${supabaseUrl}/rest/v1/user_settings?select=count`, {
                            headers: {
                                ...test.headers,
                                'apikey': supabaseKey,
                                'Authorization': `Bearer ${supabaseKey}`
                            }
                        });
                        
                        results += `${test.name}: ${response.status} ${response.statusText}\n`;
                    } catch (err) {
                        results += `${test.name}: Error - ${err.message}\n`;
                    }
                }
                
                showResult('authResult', results, 'success');
                
            } catch (error) {
                showResult('authResult', `❌ Header test failed: ${error.message}`, 'error');
            }
        };

        window.testUserSettings = async function() {
            try {
                showLoading('settingsResult', 'Testing user_settings query...');
                
                // Get current user
                const { data: { user }, error: authError } = await supabase.auth.getUser();
                
                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }
                
                // Test the exact query that was failing
                const { data, error } = await supabase
                    .from('user_settings')
                    .select('*')
                    .eq('user_id', user.id);
                
                if (error) {
                    throw error;
                }
                
                const result = `✅ User settings query successful!

User ID: ${user.id}
Settings found: ${data.length}
Timestamp: ${new Date().toISOString()}

Data:
${JSON.stringify(data, null, 2)}`;
                
                showResult('settingsResult', result, 'success');
                
            } catch (error) {
                const result = `❌ User settings query failed:

Error: ${error.message}
Code: ${error.code || 'N/A'}
Details: ${error.details || 'N/A'}
Hint: ${error.hint || 'N/A'}`;
                
                showResult('settingsResult', result, 'error');
            }
        };

        window.createTestSettings = async function() {
            try {
                showLoading('settingsResult', 'Creating test settings...');

                const { data: { user }, error: authError } = await supabase.auth.getUser();

                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }

                // Create test settings with enhanced data
                const { data, error } = await supabase
                    .from('user_settings')
                    .upsert([{
                        user_id: user.id,
                        theme: 'dark',
                        language: 'en',
                        timezone: 'America/New_York',
                        notifications: {
                            email: true,
                            push: false,
                            marketing: true
                        },
                        privacy: {
                            analytics: true,
                            data_collection: true
                        }
                    }])
                    .select();

                if (error) {
                    throw error;
                }

                const result = `✅ Test settings created successfully!

Data:
${JSON.stringify(data, null, 2)}`;

                showResult('settingsResult', result, 'success');

            } catch (error) {
                showResult('settingsResult', `❌ Create test settings failed: ${error.message}`, 'error');
            }
        };

        window.testWithDifferentHeaders = async function() {
            try {
                showLoading('settingsResult', 'Testing with different headers...');

                const { data: { user }, error: authError } = await supabase.auth.getUser();

                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }

                // Create a new client with different headers
                const testClient = createClient(supabaseUrl, supabaseKey, {
                    global: {
                        headers: {
                            'Accept': 'application/vnd.pgrst.object+json',
                            'Content-Type': 'application/json',
                            'Prefer': 'return=representation'
                        }
                    }
                });

                const { data, error } = await testClient
                    .from('user_settings')
                    .select('*')
                    .eq('user_id', user.id);

                if (error) {
                    throw error;
                }

                const result = `✅ Headers fix test successful!

Using PostgREST-specific headers resolved the 406 error.
Settings found: ${data.length}

Data:
${JSON.stringify(data, null, 2)}`;

                showResult('settingsResult', result, 'success');

            } catch (error) {
                showResult('settingsResult', `❌ Headers fix test failed: ${error.message}`, 'error');
            }
        };

        window.checkTableStructure = async function() {
            try {
                showLoading('dbResult', 'Checking table structure...');

                const tables = ['user_settings', 'notification_settings', 'privacy_settings', 'security_settings', 'user_profiles'];
                let result = 'Table Structure Check:\n\n';

                for (const table of tables) {
                    try {
                        const { data, error } = await supabase
                            .from(table)
                            .select('*')
                            .limit(0);

                        if (error) {
                            result += `❌ ${table}: ${error.message}\n`;
                        } else {
                            result += `✅ ${table}: Table exists and accessible\n`;
                        }
                    } catch (err) {
                        result += `❌ ${table}: ${err.message}\n`;
                    }
                }

                showResult('dbResult', result, 'success');

            } catch (error) {
                showResult('dbResult', `❌ Table structure check failed: ${error.message}`, 'error');
            }
        };

        window.testRLSPolicies = async function() {
            try {
                showLoading('dbResult', 'Testing RLS policies...');

                // Test without authentication first
                await supabase.auth.signOut();

                const { data: unauthData, error: unauthError } = await supabase
                    .from('user_settings')
                    .select('*')
                    .limit(1);

                let result = 'RLS Policy Test Results:\n\n';

                if (unauthError) {
                    result += `✅ Unauthenticated access blocked: ${unauthError.message}\n\n`;
                } else {
                    result += `❌ Unauthenticated access allowed (security issue!)\n\n`;
                }

                // Sign in and test authenticated access
                const { data: authData, error: authError } = await supabase.auth.signInAnonymously();

                if (authError) {
                    throw authError;
                }

                const { data: authUserData, error: authUserError } = await supabase
                    .from('user_settings')
                    .select('*')
                    .eq('user_id', authData.user.id);

                if (authUserError) {
                    result += `❌ Authenticated access failed: ${authUserError.message}`;
                } else {
                    result += `✅ Authenticated access successful: ${authUserData.length} records`;
                }

                showResult('dbResult', result, 'success');

            } catch (error) {
                showResult('dbResult', `❌ RLS policy test failed: ${error.message}`, 'error');
            }
        };

        window.checkForeignKeys = async function() {
            try {
                showLoading('dbResult', 'Checking foreign key constraints...');

                const { data: { user }, error: authError } = await supabase.auth.getUser();

                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }

                // Check if user profile exists
                const { data: profileData, error: profileError } = await supabase
                    .from('user_profiles')
                    .select('*')
                    .eq('id', user.id);

                let result = 'Foreign Key Constraint Check:\n\n';

                if (profileError) {
                    result += `❌ User profile check failed: ${profileError.message}\n`;
                } else if (profileData.length === 0) {
                    result += `⚠️ User profile missing for user ${user.id}\n`;
                    result += `This could cause foreign key constraint issues.\n\n`;

                    // Try to create user profile
                    const { data: createData, error: createError } = await supabase
                        .from('user_profiles')
                        .insert([{
                            id: user.id,
                            email: user.email || '<EMAIL>',
                            name: user.email || 'Anonymous User'
                        }])
                        .select();

                    if (createError) {
                        result += `❌ Failed to create user profile: ${createError.message}\n`;
                    } else {
                        result += `✅ User profile created successfully\n`;
                    }
                } else {
                    result += `✅ User profile exists: ${profileData[0].email}\n`;
                }

                showResult('dbResult', result, profileError || profileData.length === 0 ? 'warning' : 'success');

            } catch (error) {
                showResult('dbResult', `❌ Foreign key check failed: ${error.message}`, 'error');
            }
        };

        window.runFullDiagnostic = async function() {
            try {
                showLoading('diagnosticResult', 'Running full diagnostic...');

                let result = '🔍 FULL DIAGNOSTIC REPORT\n';
                result += '=' .repeat(50) + '\n\n';

                // 1. Connection test
                result += '1. CONNECTION TEST:\n';
                try {
                    const { data } = await supabase.auth.getSession();
                    result += `✅ Connection: OK\n`;
                    result += `✅ Session: ${data.session ? 'Active' : 'None'}\n`;
                } catch (err) {
                    result += `❌ Connection: Failed - ${err.message}\n`;
                }

                // 2. Authentication test
                result += '\n2. AUTHENTICATION TEST:\n';
                try {
                    const { data: authData, error } = await supabase.auth.signInAnonymously();
                    if (error) throw error;
                    result += `✅ Anonymous auth: OK\n`;
                    result += `✅ User ID: ${authData.user.id}\n`;
                } catch (err) {
                    result += `❌ Authentication: Failed - ${err.message}\n`;
                }

                // 3. Table access test
                result += '\n3. TABLE ACCESS TEST:\n';
                const tables = ['user_profiles', 'user_settings', 'notification_settings'];
                for (const table of tables) {
                    try {
                        const { error } = await supabase.from(table).select('*').limit(0);
                        if (error) throw error;
                        result += `✅ ${table}: Accessible\n`;
                    } catch (err) {
                        result += `❌ ${table}: ${err.message}\n`;
                    }
                }

                // 4. Headers test
                result += '\n4. HEADERS TEST:\n';
                try {
                    const response = await fetch(`${supabaseUrl}/rest/v1/user_settings?select=count`, {
                        headers: {
                            'Accept': 'application/vnd.pgrst.object+json',
                            'apikey': supabaseKey,
                            'Authorization': `Bearer ${supabaseKey}`
                        }
                    });
                    result += `✅ PostgREST headers: ${response.status} ${response.statusText}\n`;
                } catch (err) {
                    result += `❌ Headers test: ${err.message}\n`;
                }

                result += '\n' + '=' .repeat(50) + '\n';
                result += 'DIAGNOSTIC COMPLETE\n';
                result += `Timestamp: ${new Date().toISOString()}\n`;

                showResult('diagnosticResult', result, 'success');

            } catch (error) {
                showResult('diagnosticResult', `❌ Full diagnostic failed: ${error.message}`, 'error');
            }
        };

        window.testAllEndpoints = async function() {
            try {
                showLoading('diagnosticResult', 'Testing all endpoints...');

                const { data: { user }, error: authError } = await supabase.auth.getUser();

                if (authError || !user) {
                    throw new Error('User not authenticated. Please sign in first.');
                }

                const endpoints = [
                    { name: 'user_settings', table: 'user_settings' },
                    { name: 'user_profiles', table: 'user_profiles' },
                    { name: 'notification_settings', table: 'notification_settings' },
                    { name: 'privacy_settings', table: 'privacy_settings' },
                    { name: 'security_settings', table: 'security_settings' }
                ];

                let result = 'ENDPOINT TEST RESULTS:\n\n';

                for (const endpoint of endpoints) {
                    try {
                        const { data, error } = await supabase
                            .from(endpoint.table)
                            .select('*')
                            .eq('user_id', user.id);

                        if (error) {
                            result += `❌ ${endpoint.name}: ${error.message}\n`;
                        } else {
                            result += `✅ ${endpoint.name}: ${data.length} records\n`;
                        }
                    } catch (err) {
                        result += `❌ ${endpoint.name}: Exception - ${err.message}\n`;
                    }
                }

                showResult('diagnosticResult', result, 'success');

            } catch (error) {
                showResult('diagnosticResult', `❌ Endpoint test failed: ${error.message}`, 'error');
            }
        };

        window.clearAllResults = function() {
            const resultIds = ['authResult', 'settingsResult', 'dbResult', 'diagnosticResult'];
            resultIds.forEach(id => {
                document.getElementById(id).style.display = 'none';
            });
        };

        // Auto-test connection on load
        window.addEventListener('load', () => {
            setTimeout(testConnection, 1000);
        });
    </script>
</body>
</html>
