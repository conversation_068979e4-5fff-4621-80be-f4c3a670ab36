import { useState, useEffect, useCallback, useMemo } from 'react'
import { 
  URLValidator, 
  InputSanitizer, 
  RateLimiter, 
  SecurityAudit,
  XSSProtection 
} from '@/lib/security'

/**
 * Hook for URL validation with security checks
 */
export function useURLValidation() {
  const validateURL = useCallback((url: string) => {
    const isValid = URLValidator.isValidURL(url)
    
    if (!isValid) {
      SecurityAudit.log('Invalid URL blocked', { url }, 'medium')
    }
    
    return {
      isValid,
      sanitizedURL: isValid ? URLValidator.sanitizeURL(url) : null,
      error: isValid ? null : 'Invalid or potentially dangerous URL'
    }
  }, [])

  const validateURLs = useCallback((urls: string[]) => {
    const results = urls.map(url => ({
      url,
      ...validateURL(url)
    }))

    const validURLs = results.filter(result => result.isValid)
    const invalidURLs = results.filter(result => !result.isValid)

    if (invalidURLs.length > 0) {
      SecurityAudit.log('Multiple invalid URLs blocked', { 
        count: invalidURLs.length,
        urls: invalidURLs.map(r => r.url)
      }, 'medium')
    }

    return {
      validURLs: validURLs.map(r => r.sanitizedURL!),
      invalidURLs: invalidURLs.map(r => r.url),
      results
    }
  }, [validateURL])

  return { validateURL, validateURLs }
}

/**
 * Hook for input sanitization
 */
export function useInputSanitization() {
  const sanitizeHTML = useCallback((input: string) => {
    const sanitized = InputSanitizer.sanitizeHTML(input)
    
    if (sanitized !== input) {
      SecurityAudit.log('HTML content sanitized', { 
        original: input.substring(0, 100),
        sanitized: sanitized.substring(0, 100)
      }, 'low')
    }
    
    return sanitized
  }, [])

  const sanitizeText = useCallback((input: string) => {
    const sanitized = InputSanitizer.sanitizeText(input)
    
    if (sanitized !== input) {
      SecurityAudit.log('Text content sanitized', { 
        original: input.substring(0, 100),
        sanitized: sanitized.substring(0, 100)
      }, 'low')
    }
    
    return sanitized
  }, [])

  const validateEmail = useCallback((email: string) => {
    const isValid = InputSanitizer.validateEmail(email)
    
    if (!isValid) {
      SecurityAudit.log('Invalid email format', { email }, 'low')
    }
    
    return {
      isValid,
      sanitizedEmail: isValid ? email.toLowerCase().trim() : null,
      error: isValid ? null : 'Invalid email format'
    }
  }, [])

  return { sanitizeHTML, sanitizeText, validateEmail }
}

/**
 * Hook for rate limiting
 */
export function useRateLimit(
  key: string, 
  maxRequests: number, 
  windowMs: number
) {
  const rateLimiter = useMemo(
    () => RateLimiter.getInstance(key, maxRequests, windowMs),
    [key, maxRequests, windowMs]
  )

  const [remainingRequests, setRemainingRequests] = useState(maxRequests)
  const [resetTime, setResetTime] = useState(0)

  const checkLimit = useCallback(() => {
    const isAllowed = rateLimiter.isAllowed()
    
    if (!isAllowed) {
      SecurityAudit.log('Rate limit exceeded', { 
        key, 
        maxRequests, 
        windowMs 
      }, 'medium')
    }
    
    setRemainingRequests(rateLimiter.getRemainingRequests())
    setResetTime(rateLimiter.getResetTime())
    
    return isAllowed
  }, [rateLimiter, key, maxRequests, windowMs])

  const getRemainingTime = useCallback(() => {
    return Math.max(0, resetTime - Date.now())
  }, [resetTime])

  return {
    checkLimit,
    remainingRequests,
    getRemainingTime,
    isLimited: remainingRequests === 0
  }
}

/**
 * Hook for XSS protection
 */
export function useXSSProtection() {
  const escapeHTML = useCallback((text: string) => {
    return XSSProtection.escapeHTML(text)
  }, [])

  const sanitizeForAttribute = useCallback((value: string) => {
    return XSSProtection.sanitizeForAttribute(value)
  }, [])

  const validateJSON = useCallback((str: string) => {
    const isValid = XSSProtection.isValidJSON(str)
    
    if (!isValid) {
      SecurityAudit.log('Invalid JSON detected', { 
        content: str.substring(0, 100) 
      }, 'low')
    }
    
    return isValid
  }, [])

  return { escapeHTML, sanitizeForAttribute, validateJSON }
}

/**
 * Hook for security monitoring
 */
export function useSecurityMonitoring() {
  const [securityEvents, setSecurityEvents] = useState<Array<{
    timestamp: number
    event: string
    details: any
    severity: 'low' | 'medium' | 'high' | 'critical'
  }>>([])

  const [securityScore, setSecurityScore] = useState(100)

  useEffect(() => {
    const updateEvents = () => {
      const logs = SecurityAudit.getLogs()
      setSecurityEvents(logs.slice(-50)) // Keep last 50 events
      
      // Calculate security score based on recent events
      const recentEvents = logs.filter(log => 
        Date.now() - log.timestamp < 24 * 60 * 60 * 1000 // Last 24 hours
      )
      
      const severityWeights = { low: 1, medium: 5, high: 15, critical: 50 }
      const totalPenalty = recentEvents.reduce((sum, event) => 
        sum + severityWeights[event.severity], 0
      )
      
      const score = Math.max(0, 100 - totalPenalty)
      setSecurityScore(score)
    }

    updateEvents()
    const interval = setInterval(updateEvents, 5000) // Update every 5 seconds

    return () => clearInterval(interval)
  }, [])

  const logSecurityEvent = useCallback((
    event: string, 
    details: any, 
    severity: 'low' | 'medium' | 'high' | 'critical' = 'low'
  ) => {
    SecurityAudit.log(event, details, severity)
  }, [])

  const clearSecurityLogs = useCallback(() => {
    SecurityAudit.clearLogs()
    setSecurityEvents([])
    setSecurityScore(100)
  }, [])

  const exportSecurityLogs = useCallback(() => {
    return SecurityAudit.exportLogs()
  }, [])

  const getSecuritySummary = useCallback(() => {
    const events = SecurityAudit.getLogs()
    const last24h = events.filter(log => 
      Date.now() - log.timestamp < 24 * 60 * 60 * 1000
    )

    const summary = {
      totalEvents: events.length,
      last24h: last24h.length,
      bySeverity: {
        low: last24h.filter(e => e.severity === 'low').length,
        medium: last24h.filter(e => e.severity === 'medium').length,
        high: last24h.filter(e => e.severity === 'high').length,
        critical: last24h.filter(e => e.severity === 'critical').length,
      },
      score: securityScore
    }

    return summary
  }, [securityScore])

  return {
    securityEvents,
    securityScore,
    logSecurityEvent,
    clearSecurityLogs,
    exportSecurityLogs,
    getSecuritySummary
  }
}

/**
 * Hook for secure form validation
 */
export function useSecureForm<T extends Record<string, any>>(
  initialValues: T,
  validationRules: Record<keyof T, (value: any) => string | null>
) {
  const [values, setValues] = useState<T>(initialValues)
  const [errors, setErrors] = useState<Partial<Record<keyof T, string>>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { sanitizeHTML, sanitizeText } = useInputSanitization()

  const setValue = useCallback((field: keyof T, value: any) => {
    // Sanitize string values
    let sanitizedValue = value
    if (typeof value === 'string') {
      sanitizedValue = sanitizeText(value)
    }

    setValues(prev => ({ ...prev, [field]: sanitizedValue }))
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }, [sanitizeText, errors])

  const validate = useCallback(() => {
    const newErrors: Partial<Record<keyof T, string>> = {}
    
    Object.keys(validationRules).forEach(field => {
      const rule = validationRules[field as keyof T]
      const error = rule(values[field as keyof T])
      if (error) {
        newErrors[field as keyof T] = error
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [values, validationRules])

  const handleSubmit = useCallback(async (
    onSubmit: (values: T) => Promise<void> | void
  ) => {
    setIsSubmitting(true)
    
    try {
      if (validate()) {
        SecurityAudit.log('Secure form submitted', { 
          fields: Object.keys(values) 
        }, 'low')
        
        await onSubmit(values)
      } else {
        SecurityAudit.log('Form validation failed', { 
          errors: Object.keys(errors) 
        }, 'low')
      }
    } catch (error) {
      SecurityAudit.log('Form submission error', { 
        error: error instanceof Error ? error.message : 'Unknown error' 
      }, 'medium')
      throw error
    } finally {
      setIsSubmitting(false)
    }
  }, [values, validate, errors])

  const reset = useCallback(() => {
    setValues(initialValues)
    setErrors({})
    setIsSubmitting(false)
  }, [initialValues])

  return {
    values,
    errors,
    isSubmitting,
    setValue,
    validate,
    handleSubmit,
    reset
  }
}

/**
 * Hook for content security policy
 */
export function useContentSecurityPolicy() {
  const [nonce, setNonce] = useState<string>('')
  const [violations, setViolations] = useState<Array<{
    timestamp: number
    directive: string
    blockedURI: string
    violatedDirective: string
  }>>([])

  useEffect(() => {
    // Generate nonce for inline scripts
    const newNonce = Array.from(crypto.getRandomValues(new Uint8Array(16)))
      .map(b => b.toString(16).padStart(2, '0'))
      .join('')
    setNonce(newNonce)

    // Listen for CSP violations
    const handleViolation = (event: SecurityPolicyViolationEvent) => {
      const violation = {
        timestamp: Date.now(),
        directive: event.effectiveDirective,
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective
      }

      setViolations(prev => [...prev.slice(-49), violation]) // Keep last 50
      
      SecurityAudit.log('CSP violation detected', violation, 'high')
    }

    document.addEventListener('securitypolicyviolation', handleViolation)

    return () => {
      document.removeEventListener('securitypolicyviolation', handleViolation)
    }
  }, [])

  return { nonce, violations }
}
