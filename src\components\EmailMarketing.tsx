import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { useToast } from '@/hooks/use-toast';
import { useError<PERSON>and<PERSON> } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  Mail,
  Send,
  Users,
  BarChart3,
  Calendar,
  Eye,
  MousePointer,
  TrendingUp,
  Plus,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  Clock,
  CheckCircle,
  AlertCircle,
  RefreshCw,
  Filter,
  Search,
  Settings
} from 'lucide-react';

interface EmailCampaign {
  id: string;
  name: string;
  subject: string;
  content: string;
  status: 'Draft' | 'Scheduled' | 'Active' | 'Completed' | 'Paused';
  sent: number;
  opens: number;
  clicks: number;
  openRate: number;
  clickRate: number;
  created_at: string;
  scheduled_at?: string;
  sent_at?: string;
  recipient_count: number;
  template_id?: string;
}

interface EmailTemplate {
  id: string;
  name: string;
  category: string;
  description: string;
  content: string;
  subject: string;
  created_at: string;
  usage_count: number;
}

interface Contact {
  id: string;
  email: string;
  first_name?: string;
  last_name?: string;
  status: 'Active' | 'Unsubscribed' | 'Bounced';
  tags: string[];
  created_at: string;
  last_activity?: string;
}

interface ContactList {
  id: string;
  name: string;
  description: string;
  contact_count: number;
  created_at: string;
}

const EmailMarketing: React.FC = () => {
  const [campaignName, setCampaignName] = useState('');
  const [subject, setSubject] = useState('');
  const [emailContent, setEmailContent] = useState('');
  const [campaigns, setCampaigns] = useState<EmailCampaign[]>([]);
  const [templates, setTemplates] = useState<EmailTemplate[]>([]);
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [contactLists, setContactLists] = useState<ContactList[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [selectedContactList, setSelectedContactList] = useState<string>('');
  const [isCreatingCampaign, setIsCreatingCampaign] = useState(false);
  const [isSendingTest, setIsSendingTest] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [testEmail, setTestEmail] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Load data on component mount
  useEffect(() => {
    loadCampaigns();
    loadTemplates();
    loadContacts();
    loadContactLists();
  }, []);

  const loadCampaigns = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('email_campaigns')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setCampaigns(data);
      } else {
        // Set mock data if no campaigns exist
        const mockCampaigns: EmailCampaign[] = [
          {
            id: '1',
            name: 'Welcome Series',
            subject: 'Welcome to our platform!',
            content: 'Thank you for joining us...',
            status: 'Active',
            sent: 1250,
            opens: 892,
            clicks: 234,
            openRate: 71.4,
            clickRate: 18.7,
            created_at: new Date().toISOString(),
            recipient_count: 1250
          },
          {
            id: '2',
            name: 'Product Launch',
            subject: 'Exciting new product launch!',
            content: 'We are excited to announce...',
            status: 'Scheduled',
            sent: 0,
            opens: 0,
            clicks: 0,
            openRate: 0,
            clickRate: 0,
            created_at: new Date().toISOString(),
            recipient_count: 2500,
            scheduled_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
          }
        ];
        setCampaigns(mockCampaigns);
      }
    } catch (error) {
      console.error('Failed to load campaigns:', error);
      showError(error, 'Load campaigns');
    }
  }, [showError]);

  const loadTemplates = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('email_templates')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setTemplates(data);
      } else {
        // Set mock data if no templates exist
        const mockTemplates: EmailTemplate[] = [
          {
            id: '1',
            name: 'Welcome Email',
            category: 'Onboarding',
            description: 'Perfect for new subscribers',
            content: '<h1>Welcome!</h1><p>Thank you for joining us...</p>',
            subject: 'Welcome to {{company_name}}!',
            created_at: new Date().toISOString(),
            usage_count: 15
          },
          {
            id: '2',
            name: 'Product Announcement',
            category: 'Marketing',
            description: 'Showcase new products or features',
            content: '<h1>New Product Launch</h1><p>We are excited to announce...</p>',
            subject: 'Introducing {{product_name}}',
            created_at: new Date().toISOString(),
            usage_count: 8
          }
        ];
        setTemplates(mockTemplates);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
      showError(error, 'Load templates');
    }
  }, [showError]);

  const loadContacts = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('email_contacts')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(100);

      if (error) throw error;
      setContacts(data || []);
    } catch (error) {
      console.error('Failed to load contacts:', error);
    }
  }, []);

  const loadContactLists = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('contact_lists')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;
      setContactLists(data || []);
    } catch (error) {
      console.error('Failed to load contact lists:', error);
    }
  }, []);

  const createCampaign = useCallback(async () => {
    if (!campaignName.trim() || !subject.trim() || !emailContent.trim()) {
      showError(new Error('Please fill in all required fields'), 'Create campaign');
      return;
    }

    setIsCreatingCampaign(true);
    try {
      const newCampaign: Partial<EmailCampaign> = {
        name: campaignName,
        subject: subject,
        content: emailContent,
        status: 'Draft',
        sent: 0,
        opens: 0,
        clicks: 0,
        openRate: 0,
        clickRate: 0,
        recipient_count: selectedContactList ? contactLists.find(list => list.id === selectedContactList)?.contact_count || 0 : 0,
        template_id: selectedTemplate || undefined,
        created_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('email_campaigns')
        .insert([newCampaign])
        .select()
        .single();

      if (error) throw error;

      setCampaigns(prev => [data, ...prev]);

      // Reset form
      setCampaignName('');
      setSubject('');
      setEmailContent('');
      setSelectedTemplate('');
      setSelectedContactList('');

      showSuccess('Campaign created successfully');
    } catch (error) {
      showError(error, 'Create campaign');
    } finally {
      setIsCreatingCampaign(false);
    }
  }, [campaignName, subject, emailContent, selectedContactList, selectedTemplate, contactLists, showError, showSuccess]);

  const sendTestEmail = useCallback(async () => {
    if (!testEmail.trim()) {
      showError(new Error('Please enter a test email address'), 'Send test email');
      return;
    }

    setIsSendingTest(true);
    try {
      // Simulate sending test email
      await new Promise(resolve => setTimeout(resolve, 2000));

      showSuccess(`Test email sent to ${testEmail}`);
      setTestEmail('');
    } catch (error) {
      showError(error, 'Send test email');
    } finally {
      setIsSendingTest(false);
    }
  }, [testEmail, showError, showSuccess]);

  const scheduleCampaign = useCallback(async (campaignId: string) => {
    if (!scheduledDate || !scheduledTime) {
      showError(new Error('Please select date and time'), 'Schedule campaign');
      return;
    }

    try {
      const scheduledDateTime = new Date(`${scheduledDate}T${scheduledTime}`);

      const { error } = await supabase
        .from('email_campaigns')
        .update({
          status: 'Scheduled',
          scheduled_at: scheduledDateTime.toISOString()
        })
        .eq('id', campaignId);

      if (error) throw error;

      setCampaigns(prev => prev.map(campaign =>
        campaign.id === campaignId
          ? { ...campaign, status: 'Scheduled', scheduled_at: scheduledDateTime.toISOString() }
          : campaign
      ));

      showSuccess('Campaign scheduled successfully');
    } catch (error) {
      showError(error, 'Schedule campaign');
    }
  }, [scheduledDate, scheduledTime, showError, showSuccess]);

  const deleteCampaign = useCallback(async (campaignId: string) => {
    try {
      const { error } = await supabase
        .from('email_campaigns')
        .delete()
        .eq('id', campaignId);

      if (error) throw error;

      setCampaigns(prev => prev.filter(campaign => campaign.id !== campaignId));
      showSuccess('Campaign deleted successfully');
    } catch (error) {
      showError(error, 'Delete campaign');
    }
  }, [showError, showSuccess]);

  const useTemplate = useCallback((template: EmailTemplate) => {
    setSubject(template.subject);
    setEmailContent(template.content);
    setSelectedTemplate(template.id);
    showSuccess(`Template "${template.name}" applied`);
  }, [showSuccess]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Scheduled': return 'bg-blue-100 text-blue-800';
      case 'Completed': return 'bg-gray-100 text-gray-800';
      case 'Draft': return 'bg-yellow-100 text-yellow-800';
      case 'Paused': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Active': return <CheckCircle className="h-4 w-4" />;
      case 'Scheduled': return <Clock className="h-4 w-4" />;
      case 'Completed': return <CheckCircle className="h-4 w-4" />;
      case 'Draft': return <Edit className="h-4 w-4" />;
      case 'Paused': return <AlertCircle className="h-4 w-4" />;
      default: return <AlertCircle className="h-4 w-4" />;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Mail className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">Email Marketing</h1>
      </div>

      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="campaigns">Campaigns</TabsTrigger>
          <TabsTrigger value="create">Create Campaign</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Email Campaigns ({campaigns.length})</h2>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={loadCampaigns}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Campaign
              </Button>
            </div>
          </div>

          <div className="grid gap-4">
            {campaigns.map((campaign) => (
              <Card key={campaign.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium">{campaign.name}</h3>
                        <Badge className={getStatusColor(campaign.status)}>
                          {getStatusIcon(campaign.status)}
                          <span className="ml-1">{campaign.status}</span>
                        </Badge>
                      </div>

                      <p className="text-sm text-gray-600 mb-3">{campaign.subject}</p>

                      {campaign.scheduled_at && (
                        <p className="text-xs text-blue-600 mb-3">
                          Scheduled for: {new Date(campaign.scheduled_at).toLocaleString()}
                        </p>
                      )}

                      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                        <div className="text-center">
                          <div className="text-xl font-bold text-gray-900">{campaign.recipient_count.toLocaleString()}</div>
                          <div className="text-xs text-gray-600">Recipients</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-gray-900">{campaign.sent.toLocaleString()}</div>
                          <div className="text-xs text-gray-600">Sent</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-blue-600">{campaign.opens.toLocaleString()}</div>
                          <div className="text-xs text-gray-600">Opens</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-green-600">{campaign.clicks.toLocaleString()}</div>
                          <div className="text-xs text-gray-600">Clicks</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-purple-600">{campaign.openRate}%</div>
                          <div className="text-xs text-gray-600">Open Rate</div>
                        </div>
                        <div className="text-center">
                          <div className="text-xl font-bold text-orange-600">{campaign.clickRate}%</div>
                          <div className="text-xs text-gray-600">Click Rate</div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4 mr-2" />
                        Edit
                      </Button>
                      {campaign.status === 'Draft' && (
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button variant="outline" size="sm">
                              <Calendar className="h-4 w-4 mr-2" />
                              Schedule
                            </Button>
                          </DialogTrigger>
                          <DialogContent>
                            <DialogHeader>
                              <DialogTitle>Schedule Campaign</DialogTitle>
                              <DialogDescription>
                                Choose when to send "{campaign.name}"
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div className="grid grid-cols-2 gap-4">
                                <div className="space-y-2">
                                  <Label htmlFor="schedule-date">Date</Label>
                                  <Input
                                    id="schedule-date"
                                    type="date"
                                    value={scheduledDate}
                                    onChange={(e) => setScheduledDate(e.target.value)}
                                  />
                                </div>
                                <div className="space-y-2">
                                  <Label htmlFor="schedule-time">Time</Label>
                                  <Input
                                    id="schedule-time"
                                    type="time"
                                    value={scheduledTime}
                                    onChange={(e) => setScheduledTime(e.target.value)}
                                  />
                                </div>
                              </div>
                              <Button
                                onClick={() => scheduleCampaign(campaign.id)}
                                className="w-full"
                              >
                                Schedule Campaign
                              </Button>
                            </div>
                          </DialogContent>
                        </Dialog>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => deleteCampaign(campaign.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}

            {campaigns.length === 0 && (
              <Card className="border-dashed">
                <CardContent className="p-12 text-center">
                  <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No campaigns yet</h3>
                  <p className="text-gray-600 mb-4">
                    Create your first email campaign to start engaging with your audience
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Campaign
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Send className="h-5 w-5" />
                <span>Create New Campaign</span>
              </CardTitle>
              <CardDescription>
                Design and send targeted email campaigns to your audience
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="campaign-name">Campaign Name *</Label>
                  <Input
                    id="campaign-name"
                    placeholder="Enter campaign name..."
                    value={campaignName}
                    onChange={(e) => setCampaignName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contact-list">Contact List</Label>
                  <Select value={selectedContactList} onValueChange={setSelectedContactList}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select contact list" />
                    </SelectTrigger>
                    <SelectContent>
                      {contactLists.map((list) => (
                        <SelectItem key={list.id} value={list.id}>
                          {list.name} ({list.contact_count} contacts)
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject-line">Subject Line *</Label>
                <Input
                  id="subject-line"
                  placeholder="Enter email subject..."
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
                <p className="text-xs text-gray-500">
                  Tip: Keep it under 50 characters for better open rates
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="template-select">Use Template (Optional)</Label>
                <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a template" />
                  </SelectTrigger>
                  <SelectContent>
                    {templates.map((template) => (
                      <SelectItem key={template.id} value={template.id}>
                        {template.name} - {template.category}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email-content">Email Content *</Label>
                <Textarea
                  id="email-content"
                  placeholder="Write your email content here..."
                  rows={10}
                  value={emailContent}
                  onChange={(e) => setEmailContent(e.target.value)}
                />
                <p className="text-xs text-gray-500">
                  You can use HTML tags for formatting
                </p>
              </div>

              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={createCampaign}
                  disabled={isCreatingCampaign}
                >
                  {isCreatingCampaign ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Campaign
                    </>
                  )}
                </Button>

                <Dialog open={showPreview} onOpenChange={setShowPreview}>
                  <DialogTrigger asChild>
                    <Button variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Email Preview</DialogTitle>
                      <DialogDescription>
                        Preview how your email will look to recipients
                      </DialogDescription>
                    </DialogHeader>
                    <div className="border rounded-lg p-4 bg-white">
                      <div className="border-b pb-2 mb-4">
                        <p className="font-medium">Subject: {subject || 'No subject'}</p>
                      </div>
                      <div
                        className="prose max-w-none"
                        dangerouslySetInnerHTML={{
                          __html: emailContent || '<p>No content</p>'
                        }}
                      />
                    </div>
                  </DialogContent>
                </Dialog>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline">
                      <Send className="h-4 w-4 mr-2" />
                      Send Test
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Send Test Email</DialogTitle>
                      <DialogDescription>
                        Send a test email to verify how it looks
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="test-email">Test Email Address</Label>
                        <Input
                          id="test-email"
                          type="email"
                          placeholder="<EMAIL>"
                          value={testEmail}
                          onChange={(e) => setTestEmail(e.target.value)}
                        />
                      </div>
                      <Button
                        onClick={sendTestEmail}
                        disabled={isSendingTest}
                        className="w-full"
                      >
                        {isSendingTest ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Sending...
                          </>
                        ) : (
                          <>
                            <Send className="h-4 w-4 mr-2" />
                            Send Test Email
                          </>
                        )}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="templates" className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold">Email Templates ({templates.length})</h2>
            <div className="flex space-x-2">
              <Button variant="outline" onClick={loadTemplates}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Template
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map((template) => (
              <Card key={template.id} className="hover:shadow-md transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{template.name}</CardTitle>
                      <Badge variant="outline" className="mt-1">{template.category}</Badge>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-gray-500">Used {template.usage_count} times</div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{template.description}</p>
                  <div className="space-y-2">
                    <div className="text-xs text-gray-500">
                      <strong>Subject:</strong> {template.subject}
                    </div>
                    <div className="text-xs text-gray-500">
                      <strong>Created:</strong> {new Date(template.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex space-x-2 mt-4">
                    <Button
                      size="sm"
                      onClick={() => useTemplate(template)}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Use Template
                    </Button>
                    <Dialog>
                      <DialogTrigger asChild>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          Preview
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="max-w-2xl">
                        <DialogHeader>
                          <DialogTitle>{template.name} Preview</DialogTitle>
                          <DialogDescription>
                            {template.description}
                          </DialogDescription>
                        </DialogHeader>
                        <div className="border rounded-lg p-4 bg-white">
                          <div className="border-b pb-2 mb-4">
                            <p className="font-medium">Subject: {template.subject}</p>
                          </div>
                          <div
                            className="prose max-w-none"
                            dangerouslySetInnerHTML={{
                              __html: template.content
                            }}
                          />
                        </div>
                        <div className="flex space-x-2">
                          <Button
                            onClick={() => useTemplate(template)}
                            className="flex-1"
                          >
                            <Copy className="h-4 w-4 mr-2" />
                            Use This Template
                          </Button>
                        </div>
                      </DialogContent>
                    </Dialog>
                  </div>
                </CardContent>
              </Card>
            ))}

            {templates.length === 0 && (
              <Card className="border-dashed col-span-full">
                <CardContent className="p-12 text-center">
                  <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No templates yet</h3>
                  <p className="text-gray-600 mb-4">
                    Create reusable email templates to speed up your campaign creation
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Template
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium">Total Subscribers</span>
                </div>
                <div className="text-2xl font-bold mt-2">12,450</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% this month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Eye className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium">Avg. Open Rate</span>
                </div>
                <div className="text-2xl font-bold mt-2">68.5%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +3.2% vs industry
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <MousePointer className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">Avg. Click Rate</span>
                </div>
                <div className="text-2xl font-bold mt-2">19.8%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +1.5% vs industry
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5 text-orange-600" />
                  <span className="text-sm font-medium">Revenue</span>
                </div>
                <div className="text-2xl font-bold mt-2">$24,680</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +18% this month
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Campaign Performance</CardTitle>
              <CardDescription>
                Track the performance of your email campaigns over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Analytics</h3>
                <p className="text-gray-600">
                  Detailed analytics and insights coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default EmailMarketing;
