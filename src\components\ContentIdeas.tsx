import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { useToast } from '@/hooks/use-toast';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  MessageSquare,
  Lightbulb,
  TrendingUp,
  Search,
  BookOpen,
  Video,
  Mic,
  Image,
  Star,
  Clock,
  Eye,
  ThumbsUp,
  Plus,
  Edit,
  Trash2,
  Copy,
  Download,
  RefreshCw,
  Calendar,
  Target,
  BarChart3,
  Filter,
  Settings,
  Send,
  Save,
  Share2,
  Hash,
  Users,
  Zap,
  Brain,
  Sparkles
} from 'lucide-react';

interface ContentIdea {
  id: string;
  title: string;
  type: 'Blog Post' | 'Video' | 'Podcast' | 'Infographic' | 'Social Media' | 'Email' | 'Webinar' | 'Case Study';
  difficulty: 'Easy' | 'Medium' | 'Hard';
  estimatedTime: string;
  keywords: string[];
  trending: boolean;
  engagement: 'High' | 'Medium' | 'Low';
  description?: string;
  outline?: string[];
  target_audience?: string;
  content_pillars?: string[];
  seo_score?: number;
  created_at: string;
  updated_at: string;
  status: 'Draft' | 'In Progress' | 'Published' | 'Archived';
  performance?: {
    views: number;
    likes: number;
    shares: number;
    comments: number;
    engagement_rate: number;
  };
}

interface TrendingTopic {
  id: string;
  topic: string;
  growth: string;
  volume: string;
  category: string;
  related_keywords: string[];
  content_opportunities: number;
  competition_level: 'Low' | 'Medium' | 'High';
  last_updated: string;
}

interface ContentTemplate {
  id: string;
  name: string;
  type: string;
  description: string;
  structure: string[];
  usage_count: number;
  category: string;
}

interface ContentCalendar {
  id: string;
  title: string;
  type: string;
  scheduled_date: string;
  status: 'Planned' | 'In Progress' | 'Ready' | 'Published';
  assigned_to?: string;
  content_idea_id?: string;
}

const ContentIdeas: React.FC = () => {
  const [topic, setTopic] = useState('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [contentIdeas, setContentIdeas] = useState<ContentIdea[]>([]);
  const [trendingTopics, setTrendingTopics] = useState<TrendingTopic[]>([]);
  const [templates, setTemplates] = useState<ContentTemplate[]>([]);
  const [calendar, setCalendar] = useState<ContentCalendar[]>([]);
  const [selectedContentType, setSelectedContentType] = useState<string>('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState<string>('all');
  const [selectedAudience, setSelectedAudience] = useState<string>('all');
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newIdea, setNewIdea] = useState<Partial<ContentIdea>>({});
  const [filterType, setFilterType] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('created_at');
  const [searchQuery, setSearchQuery] = useState('');

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Load data on component mount
  useEffect(() => {
    loadContentIdeas();
    loadTrendingTopics();
    loadTemplates();
    loadCalendar();
  }, []);

  const loadContentIdeas = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('content_ideas')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setContentIdeas(data);
      } else {
        // Set mock data if no ideas exist
        const mockIdeas: ContentIdea[] = [
          {
            id: '1',
            title: 'The Ultimate Guide to Web Scraping Ethics',
            type: 'Blog Post',
            difficulty: 'Medium',
            estimatedTime: '45 min',
            keywords: ['web scraping', 'ethics', 'best practices'],
            trending: true,
            engagement: 'High',
            description: 'A comprehensive guide covering ethical considerations in web scraping',
            outline: ['Introduction to Ethics', 'Legal Considerations', 'Best Practices', 'Case Studies'],
            target_audience: 'Developers and Data Scientists',
            content_pillars: ['Education', 'Best Practices'],
            seo_score: 85,
            status: 'Draft',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            performance: {
              views: 0,
              likes: 0,
              shares: 0,
              comments: 0,
              engagement_rate: 0
            }
          },
          {
            id: '2',
            title: '10 Common Web Scraping Mistakes to Avoid',
            type: 'Blog Post',
            difficulty: 'Easy',
            estimatedTime: '30 min',
            keywords: ['web scraping', 'mistakes', 'tips'],
            trending: false,
            engagement: 'Medium',
            description: 'Learn about the most common pitfalls in web scraping and how to avoid them',
            outline: ['Rate Limiting Issues', 'Legal Compliance', 'Data Quality', 'Performance Optimization'],
            target_audience: 'Beginners',
            content_pillars: ['Education', 'Tips'],
            seo_score: 78,
            status: 'In Progress',
            created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            updated_at: new Date().toISOString(),
            performance: {
              views: 1250,
              likes: 89,
              shares: 23,
              comments: 15,
              engagement_rate: 7.2
            }
          }
        ];
        setContentIdeas(mockIdeas);
      }
    } catch (error) {
      console.error('Failed to load content ideas:', error);
      showError(error, 'Load content ideas');
    }
  }, [showError]);

  const loadTrendingTopics = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('trending_topics')
        .select('*')
        .order('growth', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setTrendingTopics(data);
      } else {
        // Set mock data if no trending topics exist
        const mockTopics: TrendingTopic[] = [
          {
            id: '1',
            topic: 'AI-powered web scraping',
            growth: '+156%',
            volume: '12.5K',
            category: 'Technology',
            related_keywords: ['AI', 'machine learning', 'automation'],
            content_opportunities: 15,
            competition_level: 'Medium',
            last_updated: new Date().toISOString()
          },
          {
            id: '2',
            topic: 'No-code data extraction',
            growth: '+89%',
            volume: '8.2K',
            category: 'Tools',
            related_keywords: ['no-code', 'visual scraping', 'drag-drop'],
            content_opportunities: 12,
            competition_level: 'Low',
            last_updated: new Date().toISOString()
          }
        ];
        setTrendingTopics(mockTopics);
      }
    } catch (error) {
      console.error('Failed to load trending topics:', error);
    }
  }, []);

  const loadTemplates = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('content_templates')
        .select('*')
        .order('usage_count', { ascending: false });

      if (error) throw error;
      setTemplates(data || []);
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  }, []);

  const loadCalendar = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('content_calendar')
        .select('*')
        .order('scheduled_date', { ascending: true });

      if (error) throw error;
      setCalendar(data || []);
    } catch (error) {
      console.error('Failed to load calendar:', error);
    }
  }, []);

  const handleGenerate = useCallback(async () => {
    if (!topic.trim()) {
      showError(new Error('Please enter a topic'), 'Generate content ideas');
      return;
    }

    setIsGenerating(true);
    try {
      // Simulate AI content generation
      await new Promise(resolve => setTimeout(resolve, 3000));

      const generatedIdeas = await generateContentIdeas(topic);
      setContentIdeas(prev => [...generatedIdeas, ...prev]);

      showSuccess(`Generated ${generatedIdeas.length} content ideas for "${topic}"`);
      setTopic('');
    } catch (error) {
      showError(error, 'Generate content ideas');
    } finally {
      setIsGenerating(false);
    }
  }, [topic, showError, showSuccess]);

  const generateContentIdeas = useCallback(async (topic: string): Promise<ContentIdea[]> => {
    const ideaTemplates = [
      {
        title: `The Complete Guide to ${topic}`,
        type: 'Blog Post' as const,
        difficulty: 'Medium' as const,
        estimatedTime: '45 min',
        engagement: 'High' as const,
        outline: ['Introduction', 'Key Concepts', 'Best Practices', 'Advanced Techniques', 'Conclusion']
      },
      {
        title: `10 ${topic} Tips for Beginners`,
        type: 'Blog Post' as const,
        difficulty: 'Easy' as const,
        estimatedTime: '25 min',
        engagement: 'Medium' as const,
        outline: ['Getting Started', 'Essential Tips', 'Common Mistakes', 'Resources']
      },
      {
        title: `${topic} vs Alternatives: Complete Comparison`,
        type: 'Blog Post' as const,
        difficulty: 'Medium' as const,
        estimatedTime: '35 min',
        engagement: 'High' as const,
        outline: ['Overview', 'Feature Comparison', 'Pros and Cons', 'Recommendations']
      }
    ];

    return ideaTemplates.map((template, index) => ({
      id: `generated-${Date.now()}-${index}`,
      title: template.title,
      type: template.type,
      difficulty: template.difficulty,
      estimatedTime: template.estimatedTime,
      keywords: [topic.toLowerCase(), 'guide', 'tutorial'],
      trending: Math.random() > 0.5,
      engagement: template.engagement,
      description: `Generated content idea about ${topic}`,
      outline: template.outline,
      target_audience: 'General',
      content_pillars: ['Education'],
      seo_score: Math.floor(Math.random() * 30) + 70,
      status: 'Draft',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      performance: {
        views: 0,
        likes: 0,
        shares: 0,
        comments: 0,
        engagement_rate: 0
      }
    }));
  }, []);

  const saveContentIdea = useCallback(async (idea: Partial<ContentIdea>) => {
    try {
      const { data, error } = await supabase
        .from('content_ideas')
        .insert([{
          ...idea,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }])
        .select()
        .single();

      if (error) throw error;

      setContentIdeas(prev => [data, ...prev]);
      showSuccess('Content idea saved successfully');
    } catch (error) {
      showError(error, 'Save content idea');
    }
  }, [showError, showSuccess]);

  const updateContentIdea = useCallback(async (id: string, updates: Partial<ContentIdea>) => {
    try {
      const { data, error } = await supabase
        .from('content_ideas')
        .update({ ...updates, updated_at: new Date().toISOString() })
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      setContentIdeas(prev => prev.map(idea =>
        idea.id === id ? { ...idea, ...data } : idea
      ));

      showSuccess('Content idea updated successfully');
    } catch (error) {
      showError(error, 'Update content idea');
    }
  }, [showError, showSuccess]);

  const deleteContentIdea = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from('content_ideas')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setContentIdeas(prev => prev.filter(idea => idea.id !== id));
      showSuccess('Content idea deleted successfully');
    } catch (error) {
      showError(error, 'Delete content idea');
    }
  }, [showError, showSuccess]);

  const addToCalendar = useCallback(async (ideaId: string, scheduledDate: string) => {
    try {
      const idea = contentIdeas.find(i => i.id === ideaId);
      if (!idea) return;

      const calendarItem: Partial<ContentCalendar> = {
        title: idea.title,
        type: idea.type,
        scheduled_date: scheduledDate,
        status: 'Planned',
        content_idea_id: ideaId
      };

      const { data, error } = await supabase
        .from('content_calendar')
        .insert([calendarItem])
        .select()
        .single();

      if (error) throw error;

      setCalendar(prev => [...prev, data]);
      showSuccess('Added to content calendar');
    } catch (error) {
      showError(error, 'Add to calendar');
    }
  }, [contentIdeas, showError, showSuccess]);

  const getContentTypes = useCallback(() => {
    const types = contentIdeas.reduce((acc, idea) => {
      acc[idea.type] = (acc[idea.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return [
      { type: 'Blog Post', icon: BookOpen, count: types['Blog Post'] || 0, color: 'bg-blue-100 text-blue-800' },
      { type: 'Video', icon: Video, count: types['Video'] || 0, color: 'bg-red-100 text-red-800' },
      { type: 'Podcast', icon: Mic, count: types['Podcast'] || 0, color: 'bg-green-100 text-green-800' },
      { type: 'Infographic', icon: Image, count: types['Infographic'] || 0, color: 'bg-purple-100 text-purple-800' }
    ];
  }, [contentIdeas]);

  const contentTypes = getContentTypes();

  const getFilteredIdeas = useCallback(() => {
    let filtered = [...contentIdeas];

    if (searchQuery) {
      filtered = filtered.filter(idea =>
        idea.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        idea.keywords.some(keyword => keyword.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (filterType && filterType !== 'all') {
      filtered = filtered.filter(idea => idea.type === filterType);
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'title':
          return a.title.localeCompare(b.title);
        case 'engagement':
          return b.performance?.engagement_rate || 0 - (a.performance?.engagement_rate || 0);
        case 'created_at':
        default:
          return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      }
    });

    return filtered;
  }, [contentIdeas, searchQuery, filterType, sortBy]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Easy': return 'bg-green-100 text-green-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Hard': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getEngagementColor = (engagement: string) => {
    switch (engagement) {
      case 'High': return 'text-green-600';
      case 'Medium': return 'text-yellow-600';
      case 'Low': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <MessageSquare className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">Content Ideas</h1>
      </div>

      <Tabs defaultValue="generator" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generator">Idea Generator</TabsTrigger>
          <TabsTrigger value="trending">Trending Topics</TabsTrigger>
          <TabsTrigger value="library">Content Library</TabsTrigger>
          <TabsTrigger value="analytics">Performance</TabsTrigger>
        </TabsList>

        <TabsContent value="generator" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Brain className="h-5 w-5" />
                <span>AI Content Idea Generator</span>
              </CardTitle>
              <CardDescription>
                Generate creative content ideas based on your topic and audience using AI
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="topic-input">Topic or Keyword *</Label>
                  <Input
                    id="topic-input"
                    placeholder="Enter your topic..."
                    value={topic}
                    onChange={(e) => setTopic(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && handleGenerate()}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="content-type">Content Type</Label>
                  <Select value={selectedContentType} onValueChange={setSelectedContentType}>
                    <SelectTrigger>
                      <SelectValue placeholder="Any type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Any type</SelectItem>
                      <SelectItem value="Blog Post">Blog Post</SelectItem>
                      <SelectItem value="Video">Video</SelectItem>
                      <SelectItem value="Podcast">Podcast</SelectItem>
                      <SelectItem value="Infographic">Infographic</SelectItem>
                      <SelectItem value="Social Media">Social Media</SelectItem>
                      <SelectItem value="Email">Email</SelectItem>
                      <SelectItem value="Webinar">Webinar</SelectItem>
                      <SelectItem value="Case Study">Case Study</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="difficulty">Difficulty Level</Label>
                  <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
                    <SelectTrigger>
                      <SelectValue placeholder="Any difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Any difficulty</SelectItem>
                      <SelectItem value="Easy">Easy</SelectItem>
                      <SelectItem value="Medium">Medium</SelectItem>
                      <SelectItem value="Hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="audience">Target Audience</Label>
                  <Select value={selectedAudience} onValueChange={setSelectedAudience}>
                    <SelectTrigger>
                      <SelectValue placeholder="General audience" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">General audience</SelectItem>
                      <SelectItem value="Beginners">Beginners</SelectItem>
                      <SelectItem value="Intermediate">Intermediate</SelectItem>
                      <SelectItem value="Advanced">Advanced</SelectItem>
                      <SelectItem value="Developers">Developers</SelectItem>
                      <SelectItem value="Marketers">Marketers</SelectItem>
                      <SelectItem value="Business Owners">Business Owners</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="flex space-x-2">
                <Button onClick={handleGenerate} disabled={isGenerating || !topic}>
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <Sparkles className="h-4 w-4 mr-2" />
                      Generate Ideas
                    </>
                  )}
                </Button>
                <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Manual
                    </Button>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>Create Content Idea</DialogTitle>
                      <DialogDescription>
                        Manually create a new content idea
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="idea-title">Title *</Label>
                        <Input
                          id="idea-title"
                          value={newIdea.title || ''}
                          onChange={(e) => setNewIdea(prev => ({ ...prev, title: e.target.value }))}
                        />
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="idea-type">Type</Label>
                          <Select
                            value={newIdea.type || 'Blog Post'}
                            onValueChange={(value) => setNewIdea(prev => ({ ...prev, type: value as ContentIdea['type'] }))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Blog Post">Blog Post</SelectItem>
                              <SelectItem value="Video">Video</SelectItem>
                              <SelectItem value="Podcast">Podcast</SelectItem>
                              <SelectItem value="Infographic">Infographic</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="idea-difficulty">Difficulty</Label>
                          <Select
                            value={newIdea.difficulty || 'Easy'}
                            onValueChange={(value) => setNewIdea(prev => ({ ...prev, difficulty: value as ContentIdea['difficulty'] }))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select difficulty" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Easy">Easy</SelectItem>
                              <SelectItem value="Medium">Medium</SelectItem>
                              <SelectItem value="Hard">Hard</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="idea-description">Description</Label>
                        <Textarea
                          id="idea-description"
                          rows={3}
                          value={newIdea.description || ''}
                          onChange={(e) => setNewIdea(prev => ({ ...prev, description: e.target.value }))}
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="idea-keywords">Keywords (comma-separated)</Label>
                        <Input
                          id="idea-keywords"
                          value={newIdea.keywords?.join(', ') || ''}
                          onChange={(e) => setNewIdea(prev => ({
                            ...prev,
                            keywords: e.target.value.split(',').map(k => k.trim()).filter(k => k)
                          }))}
                        />
                      </div>
                      <Button
                        onClick={() => {
                          if (newIdea.title && newIdea.type) {
                            saveContentIdea({
                              ...newIdea,
                              estimatedTime: '30 min',
                              engagement: 'Medium',
                              trending: false,
                              status: 'Draft'
                            });
                            setNewIdea({});
                            setShowCreateDialog(false);
                          }
                        }}
                        className="w-full"
                      >
                        <Save className="h-4 w-4 mr-2" />
                        Save Idea
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {isGenerating && (
                <div className="space-y-2">
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                    <span className="text-sm text-gray-600">AI is generating creative content ideas...</span>
                  </div>
                  <Progress value={60} className="w-full" />
                  <p className="text-xs text-gray-500">
                    Analyzing trends, keywords, and audience preferences
                  </p>
                </div>
              )}

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Content Ideas ({getFilteredIdeas().length})</h3>
                  <div className="flex space-x-2">
                    <Input
                      placeholder="Search ideas..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-48"
                    />
                    <Select value={filterType} onValueChange={setFilterType}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Filter" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All types</SelectItem>
                        <SelectItem value="Blog Post">Blog Post</SelectItem>
                        <SelectItem value="Video">Video</SelectItem>
                        <SelectItem value="Podcast">Podcast</SelectItem>
                      </SelectContent>
                    </Select>
                    <Select value={sortBy} onValueChange={setSortBy}>
                      <SelectTrigger className="w-32">
                        <SelectValue placeholder="Sort by" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="created_at">Recent</SelectItem>
                        <SelectItem value="title">Title</SelectItem>
                        <SelectItem value="engagement">Engagement</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {getFilteredIdeas().length > 0 ? (
                  getFilteredIdeas().map((idea) => (
                    <Card key={idea.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center space-x-2 mb-2">
                              <h4 className="font-medium">{idea.title}</h4>
                              {idea.trending && (
                                <Badge className="bg-orange-100 text-orange-800">
                                  <TrendingUp className="h-3 w-3 mr-1" />
                                  Trending
                                </Badge>
                              )}
                              <Badge variant="outline" className={`${idea.status === 'Published' ? 'bg-green-50 text-green-700' : ''}`}>
                                {idea.status}
                              </Badge>
                            </div>

                            {idea.description && (
                              <p className="text-sm text-gray-600 mb-2">{idea.description}</p>
                            )}

                            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-2">
                              <Badge variant="outline">{idea.type}</Badge>
                              <Badge className={getDifficultyColor(idea.difficulty)}>
                                {idea.difficulty}
                              </Badge>
                              <div className="flex items-center space-x-1">
                                <Clock className="h-3 w-3" />
                                <span>{idea.estimatedTime}</span>
                              </div>
                              <div className="flex items-center space-x-1">
                                <Eye className="h-3 w-3" />
                                <span className={getEngagementColor(idea.engagement)}>
                                  {idea.engagement} engagement
                                </span>
                              </div>
                              {idea.seo_score && (
                                <div className="flex items-center space-x-1">
                                  <Target className="h-3 w-3" />
                                  <span>SEO: {idea.seo_score}</span>
                                </div>
                              )}
                            </div>

                            <div className="flex flex-wrap gap-1">
                              {idea.keywords.map((keyword, idx) => (
                                <Badge key={idx} variant="secondary" className="text-xs">
                                  #{keyword}
                                </Badge>
                              ))}
                            </div>
                          </div>

                          <div className="flex flex-col space-y-1">
                            <div className="flex space-x-1">
                              <Button variant="ghost" size="sm">
                                <Star className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Copy className="h-3 w-3" />
                              </Button>
                              <Button variant="ghost" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                            <div className="flex space-x-1">
                              <Button variant="outline" size="sm">
                                <Calendar className="h-3 w-3 mr-1" />
                                Schedule
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => deleteContentIdea(idea.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                ) : (
                  <Card className="border-dashed">
                    <CardContent className="p-12 text-center">
                      <Lightbulb className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No content ideas yet</h3>
                      <p className="text-gray-600 mb-4">
                        Generate AI-powered content ideas or create them manually
                      </p>
                      <Button onClick={() => setTopic('web scraping')}>
                        <Sparkles className="h-4 w-4 mr-2" />
                        Try Example Topic
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trending" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Trending Topics</span>
              </CardTitle>
              <CardDescription>
                Discover what's trending in your industry right now
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {trendingTopics.map((item, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{item.topic}</h4>
                        <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                          <span>Search Volume: {item.volume}</span>
                          <Badge className="bg-green-100 text-green-800">
                            {item.growth}
                          </Badge>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Create Content
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="library" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {contentTypes.map((type, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${type.color}`}>
                      <type.icon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="text-2xl font-bold">{type.count}</div>
                      <div className="text-sm text-gray-600">{type.type}s</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Content Library</CardTitle>
              <CardDescription>
                Browse and manage your content ideas and drafts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Content Library</h3>
                <p className="text-gray-600 mb-4">
                  Organize and manage all your content ideas in one place
                </p>
                <Button>Browse Library</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Eye className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium">Total Views</span>
                </div>
                <div className="text-2xl font-bold mt-2">125.4K</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +24% this month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <ThumbsUp className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">Engagement</span>
                </div>
                <div className="text-2xl font-bold mt-2">8.7%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.1% vs last month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium">Comments</span>
                </div>
                <div className="text-2xl font-bold mt-2">1,234</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +15% this month
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Star className="h-5 w-5 text-yellow-600" />
                  <span className="text-sm font-medium">Avg. Rating</span>
                </div>
                <div className="text-2xl font-bold mt-2">4.8</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +0.2 this month
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Content Performance</CardTitle>
              <CardDescription>
                Track how your content is performing across different platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Performance Analytics</h3>
                <p className="text-gray-600">
                  Detailed content performance metrics coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ContentIdeas;
