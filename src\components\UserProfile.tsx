import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import {
  User,
  Mail,
  Lock,
  Shield,
  Smartphone,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  Settings,
  Bell,
  Globe,
  Monitor
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import PasswordStrengthMeter from '@/components/auth/PasswordStrengthMeter';
import { sessionManagerWrapper } from '@/lib/sessionManagerWrapper';

const UserProfile: React.FC = () => {
  const { user, updateProfile, updatePassword, setupTwoFactor, enableTwoFactor, disableTwoFactor, isTwoFactorEnabled } = useAuth();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('profile');

  // Profile form state
  const [profileForm, setProfileForm] = useState({
    name: user?.name || '',
    email: user?.email || '',
    avatar_url: user?.avatar_url || ''
  });

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    showPasswords: false
  });

  // 2FA state
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false);
  const [twoFactorSetup, setTwoFactorSetup] = useState<any>(null);
  const [verificationCode, setVerificationCode] = useState('');

  // Settings state
  const [settings, setSettings] = useState({
    emailNotifications: true,
    pushNotifications: false,
    marketingEmails: true,
    securityAlerts: true,
    sessionTimeout: 30
  });

  // Sessions state
  const [activeSessions, setActiveSessions] = useState<any[]>([]);

  useEffect(() => {
    if (user) {
      setProfileForm({
        name: user.name || '',
        email: user.email || '',
        avatar_url: user.avatar_url || ''
      });
      
      // Check 2FA status
      checkTwoFactorStatus();
      
      // Load active sessions
      loadActiveSessions();
    }
  }, [user]);

  const checkTwoFactorStatus = async () => {
    if (user) {
      const enabled = await isTwoFactorEnabled(user.id);
      setTwoFactorEnabled(enabled);
    }
  };

  const loadActiveSessions = async () => {
    if (user) {
      try {
        const sessions = await sessionManagerWrapper.getUserSessions(user.id);
        setActiveSessions(sessions);
      } catch (error) {
        console.error('Error loading sessions:', error);
        setError('Failed to load active sessions');
      }
    }
  };

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { user: updatedUser, error } = await updateProfile(profileForm);
      
      if (error) {
        setError(error);
      } else {
        setSuccess('Profile updated successfully!');
      }
    } catch (err) {
      setError('Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    setSuccess(null);

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      setError('New passwords do not match');
      setLoading(false);
      return;
    }

    try {
      const { error } = await updatePassword(passwordForm.newPassword);
      
      if (error) {
        setError(error);
      } else {
        setSuccess('Password updated successfully!');
        setPasswordForm({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
          showPasswords: false
        });
      }
    } catch (err) {
      setError('Failed to update password');
    } finally {
      setLoading(false);
    }
  };

  const handleSetupTwoFactor = async () => {
    if (!user) return;
    
    setLoading(true);
    setError(null);

    try {
      const { setup, error } = await setupTwoFactor(user.id, user.email);
      
      if (error) {
        setError(error);
      } else {
        setTwoFactorSetup(setup);
      }
    } catch (err) {
      setError('Failed to setup two-factor authentication');
    } finally {
      setLoading(false);
    }
  };

  const handleEnableTwoFactor = async () => {
    if (!user || !verificationCode) return;
    
    setLoading(true);
    setError(null);

    try {
      const { success, error } = await enableTwoFactor(user.id, verificationCode);
      
      if (error) {
        setError(error);
      } else if (success) {
        setSuccess('Two-factor authentication enabled successfully!');
        setTwoFactorEnabled(true);
        setTwoFactorSetup(null);
        setVerificationCode('');
      }
    } catch (err) {
      setError('Failed to enable two-factor authentication');
    } finally {
      setLoading(false);
    }
  };

  const handleDisableTwoFactor = async () => {
    if (!user || !verificationCode) return;
    
    setLoading(true);
    setError(null);

    try {
      const { success, error } = await disableTwoFactor(user.id, verificationCode);
      
      if (error) {
        setError(error);
      } else if (success) {
        setSuccess('Two-factor authentication disabled successfully!');
        setTwoFactorEnabled(false);
        setVerificationCode('');
      }
    } catch (err) {
      setError('Failed to disable two-factor authentication');
    } finally {
      setLoading(false);
    }
  };

  const handleTerminateSession = async (sessionToken: string) => {
    try {
      await sessionManagerWrapper.terminateSession(sessionToken);
      setSuccess('Session terminated successfully');
      loadActiveSessions();
    } catch (err: any) {
      setError(err.message || 'Failed to terminate session');
    }
  };

  const handleTerminateAllSessions = async () => {
    if (!user) return;

    try {
      await sessionManagerWrapper.terminateAllUserSessions(user.id);
      setSuccess('All other sessions terminated successfully');
      loadActiveSessions();
    } catch (err: any) {
      setError(err.message || 'Failed to terminate sessions');
    }
  };

  if (!user) {
    return <div>Please sign in to view your profile.</div>;
  }

  return (
    <div className="container mx-auto py-6 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Account Settings</h1>
          <p className="text-gray-600">Manage your account preferences and security settings</p>
        </div>

        {error && (
          <Alert className="mb-4 border-red-200 bg-red-50">
            <XCircle className="h-4 w-4" />
            <AlertDescription className="text-red-700">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="mb-4 border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-700">{success}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Profile
            </TabsTrigger>
            <TabsTrigger value="security" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Security
            </TabsTrigger>
            <TabsTrigger value="sessions" className="flex items-center gap-2">
              <Monitor className="h-4 w-4" />
              Sessions
            </TabsTrigger>
            <TabsTrigger value="preferences" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Preferences
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <Card>
              <CardHeader>
                <CardTitle>Profile Information</CardTitle>
                <CardDescription>
                  Update your personal information and account details
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleProfileUpdate} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={profileForm.name}
                        onChange={(e) => setProfileForm(prev => ({ ...prev, name: e.target.value }))}
                        placeholder="Enter your full name"
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={profileForm.email}
                        onChange={(e) => setProfileForm(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="Enter your email"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="avatar">Avatar URL</Label>
                    <Input
                      id="avatar"
                      value={profileForm.avatar_url}
                      onChange={(e) => setProfileForm(prev => ({ ...prev, avatar_url: e.target.value }))}
                      placeholder="Enter avatar URL (optional)"
                    />
                  </div>

                  <div className="flex items-center space-x-4">
                    <Badge variant={user.subscription_status === 'premium' ? 'default' : 'secondary'}>
                      {user.subscription_status?.toUpperCase() || 'FREE'}
                    </Badge>
                    <Badge variant="outline">
                      {user.role?.toUpperCase() || 'USER'}
                    </Badge>
                  </div>

                  <Button type="submit" disabled={loading}>
                    {loading ? 'Updating...' : 'Update Profile'}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="security">
            <div className="space-y-6">
              {/* Password Change */}
              <Card>
                <CardHeader>
                  <CardTitle>Change Password</CardTitle>
                  <CardDescription>
                    Update your password to keep your account secure
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handlePasswordChange} className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="newPassword">New Password</Label>
                      <div className="relative">
                        <Input
                          id="newPassword"
                          type={passwordForm.showPasswords ? "text" : "password"}
                          value={passwordForm.newPassword}
                          onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                          placeholder="Enter new password"
                        />
                        <button
                          type="button"
                          className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                          onClick={() => setPasswordForm(prev => ({ ...prev, showPasswords: !prev.showPasswords }))}
                        >
                          {passwordForm.showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </button>
                      </div>
                      <PasswordStrengthMeter password={passwordForm.newPassword} />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="confirmPassword">Confirm New Password</Label>
                      <Input
                        id="confirmPassword"
                        type={passwordForm.showPasswords ? "text" : "password"}
                        value={passwordForm.confirmPassword}
                        onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                        placeholder="Confirm new password"
                      />
                    </div>

                    <Button type="submit" disabled={loading}>
                      {loading ? 'Updating...' : 'Update Password'}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="sessions">
            <Card>
              <CardHeader>
                <CardTitle>Active Sessions</CardTitle>
                <CardDescription>
                  Manage your active sessions and sign out from other devices
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activeSessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">
                          {session.device_info?.browser} on {session.device_info?.os}
                        </div>
                        <div className="text-sm text-gray-600">
                          {session.ip_address} • Last active: {new Date(session.last_activity).toLocaleString()}
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleTerminateSession(session.session_token)}
                        disabled={session.session_token === 'current'}
                      >
                        {session.session_token === 'current' ? 'Current Session' : 'Sign Out'}
                      </Button>
                    </div>
                  ))}
                  
                  {activeSessions.length > 1 && (
                    <Button
                      variant="destructive"
                      onClick={handleTerminateAllSessions}
                      className="w-full"
                    >
                      Sign Out All Other Sessions
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="preferences">
            <Card>
              <CardHeader>
                <CardTitle>Notification Preferences</CardTitle>
                <CardDescription>
                  Choose how you want to receive notifications
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="email-notifications">Email Notifications</Label>
                      <p className="text-sm text-gray-600">Receive important updates via email</p>
                    </div>
                    <Switch
                      id="email-notifications"
                      checked={settings.emailNotifications}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, emailNotifications: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="security-alerts">Security Alerts</Label>
                      <p className="text-sm text-gray-600">Get notified about security events</p>
                    </div>
                    <Switch
                      id="security-alerts"
                      checked={settings.securityAlerts}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, securityAlerts: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="marketing-emails">Marketing Emails</Label>
                      <p className="text-sm text-gray-600">Receive product updates and tips</p>
                    </div>
                    <Switch
                      id="marketing-emails"
                      checked={settings.marketingEmails}
                      onCheckedChange={(checked) => setSettings(prev => ({ ...prev, marketingEmails: checked }))}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default UserProfile;
