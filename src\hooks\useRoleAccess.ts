import { useMemo } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export interface RolePermissions {
  [key: string]: string[];
}

// Define role hierarchy and permissions
const ROLE_HIERARCHY: { [key: string]: number } = {
  'user': 1,
  'premium': 2,
  'moderator': 3,
  'admin': 4,
  'super_admin': 5
};

const ROLE_PERMISSIONS: RolePermissions = {
  'user': [
    'read:profile',
    'update:profile',
    'read:basic_analytics',
    'create:basic_exports'
  ],
  'premium': [
    'read:profile',
    'update:profile',
    'read:basic_analytics',
    'read:advanced_analytics',
    'create:basic_exports',
    'create:advanced_exports',
    'access:premium_features',
    'create:api_keys'
  ],
  'moderator': [
    'read:profile',
    'update:profile',
    'read:basic_analytics',
    'read:advanced_analytics',
    'create:basic_exports',
    'create:advanced_exports',
    'access:premium_features',
    'create:api_keys',
    'moderate:content',
    'read:user_reports',
    'manage:user_content'
  ],
  'admin': [
    'read:profile',
    'update:profile',
    'read:basic_analytics',
    'read:advanced_analytics',
    'create:basic_exports',
    'create:advanced_exports',
    'access:premium_features',
    'create:api_keys',
    'moderate:content',
    'read:user_reports',
    'manage:user_content',
    'manage:users',
    'read:system_analytics',
    'manage:system_settings',
    'access:admin_panel'
  ],
  'super_admin': [
    'read:profile',
    'update:profile',
    'read:basic_analytics',
    'read:advanced_analytics',
    'create:basic_exports',
    'create:advanced_exports',
    'access:premium_features',
    'create:api_keys',
    'moderate:content',
    'read:user_reports',
    'manage:user_content',
    'manage:users',
    'read:system_analytics',
    'manage:system_settings',
    'access:admin_panel',
    'manage:roles',
    'manage:permissions',
    'access:system_logs',
    'manage:billing',
    'access:super_admin_panel'
  ]
};

export interface UseRoleAccessReturn {
  userRole: string;
  userPermissions: string[];
  hasRole: (role: string) => boolean;
  hasPermission: (permission: string) => boolean;
  hasAnyPermission: (permissions: string[]) => boolean;
  hasAllPermissions: (permissions: string[]) => boolean;
  canAccessRoute: (requiredRole?: string[], requiredPermissions?: string[]) => boolean;
  isAtLeastRole: (role: string) => boolean;
  getRoleLevel: (role: string) => number;
  canManageUser: (targetUserRole: string) => boolean;
}

export const useRoleAccess = (): UseRoleAccessReturn => {
  const { user } = useAuth();

  const userRole = useMemo(() => {
    return user?.role || 'user';
  }, [user?.role]);

  const userPermissions = useMemo(() => {
    const role = userRole;
    const rolePermissions = ROLE_PERMISSIONS[role] || ROLE_PERMISSIONS['user'];
    const customPermissions = user?.permissions || [];
    
    // Combine role-based permissions with custom permissions
    return [...new Set([...rolePermissions, ...customPermissions])];
  }, [userRole, user?.permissions]);

  const hasRole = (role: string): boolean => {
    return userRole === role;
  };

  const hasPermission = (permission: string): boolean => {
    return userPermissions.includes(permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => userPermissions.includes(permission));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => userPermissions.includes(permission));
  };

  const getRoleLevel = (role: string): number => {
    return ROLE_HIERARCHY[role] || 0;
  };

  const isAtLeastRole = (role: string): boolean => {
    const userLevel = getRoleLevel(userRole);
    const requiredLevel = getRoleLevel(role);
    return userLevel >= requiredLevel;
  };

  const canManageUser = (targetUserRole: string): boolean => {
    const userLevel = getRoleLevel(userRole);
    const targetLevel = getRoleLevel(targetUserRole);
    
    // Users can only manage users with lower role levels
    return userLevel > targetLevel;
  };

  const canAccessRoute = (
    requiredRole: string[] = [], 
    requiredPermissions: string[] = []
  ): boolean => {
    // Check role requirements
    if (requiredRole.length > 0) {
      const hasRequiredRole = requiredRole.includes(userRole) || 
                             requiredRole.some(role => isAtLeastRole(role));
      if (!hasRequiredRole) {
        return false;
      }
    }

    // Check permission requirements
    if (requiredPermissions.length > 0) {
      if (!hasAllPermissions(requiredPermissions)) {
        return false;
      }
    }

    return true;
  };

  return {
    userRole,
    userPermissions,
    hasRole,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccessRoute,
    isAtLeastRole,
    getRoleLevel,
    canManageUser
  };
};

// Helper function to check if a user can perform an action
export const canPerformAction = (
  userRole: string, 
  userPermissions: string[], 
  requiredPermission: string
): boolean => {
  const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
  return rolePermissions.includes(requiredPermission) || 
         userPermissions.includes(requiredPermission);
};

// Helper function to get all permissions for a role
export const getRolePermissions = (role: string): string[] => {
  return ROLE_PERMISSIONS[role] || [];
};

// Helper function to check if a role exists
export const isValidRole = (role: string): boolean => {
  return Object.keys(ROLE_PERMISSIONS).includes(role);
};

// Helper function to get available roles
export const getAvailableRoles = (): string[] => {
  return Object.keys(ROLE_PERMISSIONS);
};

// Helper function to get role hierarchy
export const getRoleHierarchy = (): { [key: string]: number } => {
  return ROLE_HIERARCHY;
};
