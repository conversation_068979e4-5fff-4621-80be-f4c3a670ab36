import { createClient } from '@supabase/supabase-js';
import { config } from './config';

// Initialize Supabase client with enhanced configuration to fix 406 errors
const supabase = createClient(config.supabase.url, config.supabase.anonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'Accept': 'application/json, application/vnd.pgrst.object+json',
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    },
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});

export { supabase };