#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em rgba(20, 184, 166, 0.6));
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em rgba(20, 184, 166, 0.6));
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
  border-radius: 0.5rem;
  border: 1px solid rgba(20, 184, 166, 0.1);
  background-color: rgba(20, 184, 166, 0.02);
  transition: all 0.3s ease;
}

.card:hover {
  border-color: rgba(20, 184, 166, 0.3);
  box-shadow: 0 4px 12px rgba(20, 184, 166, 0.1);
}

.read-the-docs {
  color: #5f7676;
}
