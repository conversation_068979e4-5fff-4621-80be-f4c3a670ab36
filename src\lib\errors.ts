// Error handling utilities and types

export class AppError extends Error {
  constructor(
    message: string,
    public code: string = 'UNKNOWN_ERROR',
    public statusCode: number = 500,
    public userMessage?: string
  ) {
    super(message);
    this.name = 'AppError';
  }
}

export class NetworkError extends AppError {
  constructor(message: string, userMessage?: string) {
    super(message, 'NETWORK_ERROR', 0, userMessage || 'Network connection failed. Please check your internet connection.');
    this.name = 'NetworkError';
  }
}

export class ValidationError extends AppError {
  constructor(message: string, userMessage?: string) {
    super(message, 'VALIDATION_ERROR', 400, userMessage || 'Invalid input provided.');
    this.name = 'ValidationError';
  }
}

export class SupabaseError extends AppError {
  constructor(message: string, userMessage?: string) {
    super(message, 'SUPABASE_ERROR', 500, userMessage || 'Database operation failed. Please try again.');
    this.name = 'SupabaseError';
  }
}

export const handleError = (error: unknown): AppError => {
  if (error instanceof AppError) {
    return error;
  }

  if (error instanceof Error) {
    // Handle specific error types
    if (error.message.includes('fetch')) {
      return new NetworkError(error.message);
    }
    
    if (error.message.includes('supabase') || error.message.includes('database')) {
      return new SupabaseError(error.message);
    }

    return new AppError(error.message, 'UNKNOWN_ERROR', 500, 'An unexpected error occurred. Please try again.');
  }

  return new AppError('Unknown error occurred', 'UNKNOWN_ERROR', 500, 'An unexpected error occurred. Please try again.');
};

export const logError = (error: AppError, context?: string) => {
  const errorInfo = {
    message: error.message,
    code: error.code,
    statusCode: error.statusCode,
    context,
    timestamp: new Date().toISOString(),
    stack: error.stack,
  };

  if (import.meta.env.DEV) {
    console.error('Error:', errorInfo);
  } else {
    // In production, you might want to send to an error tracking service
    console.error(`[${error.code}] ${error.message}`);
  }
};

export const createErrorHandler = (context: string) => {
  return (error: unknown) => {
    const appError = handleError(error);
    logError(appError, context);
    return appError;
  };
};
