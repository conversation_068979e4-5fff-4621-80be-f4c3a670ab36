import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { useToast } from '@/hooks/use-toast';
import { useError<PERSON>andler } from '@/hooks/useErrorHandler';
import { supabase } from '@/lib/supabase';
import {
  Share2,
  Calendar,
  BarChart3,
  Heart,
  MessageCircle,
  Repeat2,
  Eye,
  TrendingUp,
  Plus,
  Clock,
  Users,
  Settings,
  RefreshCw,
  Edit,
  Trash2,
  Copy,
  Download,
  Upload,
  Image as ImageIcon,
  Link,
  Hash,
  AtSign,
  CheckCircle,
  AlertCircle,
  Send,
  Pause,
  Play
} from 'lucide-react';

interface SocialPlatform {
  id: string;
  name: string;
  connected: boolean;
  followers: string;
  color: string;
  icon: string;
  access_token?: string;
  account_id?: string;
  last_sync?: string;
}

interface SocialPost {
  id: string;
  content: string;
  platform: string;
  status: 'Draft' | 'Scheduled' | 'Published' | 'Failed';
  scheduled_at?: string;
  published_at?: string;
  metrics: {
    likes: number;
    comments: number;
    shares: number;
    views: number;
    engagement_rate: number;
  };
  media_urls?: string[];
  hashtags?: string[];
  mentions?: string[];
  created_at: string;
}

interface ContentTemplate {
  id: string;
  name: string;
  content: string;
  category: string;
  hashtags: string[];
  usage_count: number;
}

interface AnalyticsData {
  platform: string;
  period: string;
  reach: number;
  engagement: number;
  followers_growth: number;
  top_posts: SocialPost[];
}

const SocialMedia: React.FC = () => {
  const [postContent, setPostContent] = useState('');
  const [scheduledTime, setScheduledTime] = useState('');
  const [scheduledDate, setScheduledDate] = useState('');
  const [selectedPlatforms, setSelectedPlatforms] = useState<string[]>([]);
  const [socialPlatforms, setSocialPlatforms] = useState<SocialPlatform[]>([]);
  const [posts, setPosts] = useState<SocialPost[]>([]);
  const [scheduledPosts, setScheduledPosts] = useState<SocialPost[]>([]);
  const [recentPosts, setRecentPosts] = useState<SocialPost[]>([]);
  const [templates, setTemplates] = useState<ContentTemplate[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData[]>([]);
  const [isPosting, setIsPosting] = useState(false);
  const [isScheduling, setIsScheduling] = useState(false);
  const [showMediaUpload, setShowMediaUpload] = useState(false);
  const [mediaFiles, setMediaFiles] = useState<File[]>([]);
  const [hashtags, setHashtags] = useState<string>('');
  const [mentions, setMentions] = useState<string>('');
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');

  const { toast } = useToast();
  const { showError, showSuccess } = useErrorHandler();

  // Load data on component mount
  useEffect(() => {
    loadSocialPlatforms();
    loadPosts();
    loadTemplates();
    loadAnalytics();
  }, []);

  const loadSocialPlatforms = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('social_platforms')
        .select('*')
        .order('name');

      if (error) throw error;

      if (data && data.length > 0) {
        setSocialPlatforms(data);
      } else {
        // Set mock data if no platforms exist
        const mockPlatforms: SocialPlatform[] = [
          {
            id: '1',
            name: 'Twitter',
            connected: true,
            followers: '12.5K',
            color: 'bg-blue-500',
            icon: '🐦',
            last_sync: new Date().toISOString()
          },
          {
            id: '2',
            name: 'LinkedIn',
            connected: true,
            followers: '8.2K',
            color: 'bg-blue-700',
            icon: '💼',
            last_sync: new Date().toISOString()
          },
          {
            id: '3',
            name: 'Facebook',
            connected: false,
            followers: '0',
            color: 'bg-blue-600',
            icon: '📘'
          },
          {
            id: '4',
            name: 'Instagram',
            connected: true,
            followers: '15.8K',
            color: 'bg-pink-500',
            icon: '📸',
            last_sync: new Date().toISOString()
          }
        ];
        setSocialPlatforms(mockPlatforms);
      }
    } catch (error) {
      console.error('Failed to load social platforms:', error);
      showError(error, 'Load social platforms');
    }
  }, [showError]);

  const loadPosts = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('social_posts')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(50);

      if (error) throw error;

      if (data && data.length > 0) {
        setPosts(data);
        setScheduledPosts(data.filter(post => post.status === 'Scheduled'));
        setRecentPosts(data.filter(post => post.status === 'Published').slice(0, 10));
      } else {
        // Set mock data if no posts exist
        const mockPosts: SocialPost[] = [
          {
            id: '1',
            content: 'Just launched our new web scraping tool! 🚀 Perfect for data analysts and marketers.',
            platform: 'Twitter',
            status: 'Scheduled',
            scheduled_at: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(),
            metrics: { likes: 0, comments: 0, shares: 0, views: 0, engagement_rate: 0 },
            hashtags: ['WebScraping', 'DataAnalytics'],
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            content: 'Web scraping best practices: Always respect robots.txt and rate limits! 🤖',
            platform: 'Twitter',
            status: 'Published',
            published_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            metrics: { likes: 45, comments: 12, shares: 8, views: 1250, engagement_rate: 5.2 },
            hashtags: ['WebScraping', 'BestPractices'],
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          }
        ];
        setPosts(mockPosts);
        setScheduledPosts(mockPosts.filter(post => post.status === 'Scheduled'));
        setRecentPosts(mockPosts.filter(post => post.status === 'Published'));
      }
    } catch (error) {
      console.error('Failed to load posts:', error);
      showError(error, 'Load posts');
    }
  }, [showError]);

  const loadTemplates = useCallback(async () => {
    try {
      const { data, error } = await supabase
        .from('content_templates')
        .select('*')
        .order('usage_count', { ascending: false });

      if (error) throw error;

      if (data && data.length > 0) {
        setTemplates(data);
      } else {
        // Set mock data if no templates exist
        const mockTemplates: ContentTemplate[] = [
          {
            id: '1',
            name: 'Product Launch',
            content: 'Excited to announce our latest product! 🚀 {{product_name}} is here to revolutionize {{industry}}.',
            category: 'Marketing',
            hashtags: ['ProductLaunch', 'Innovation', 'TechNews'],
            usage_count: 15
          },
          {
            id: '2',
            name: 'Tips & Tricks',
            content: '💡 Pro tip: {{tip_content}} What are your favorite {{topic}} tips?',
            category: 'Educational',
            hashtags: ['Tips', 'ProTip', 'Learning'],
            usage_count: 8
          }
        ];
        setTemplates(mockTemplates);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  }, []);

  const loadAnalytics = useCallback(async () => {
    try {
      // Mock analytics data
      const mockAnalytics: AnalyticsData[] = [
        {
          platform: 'Twitter',
          period: 'last_7_days',
          reach: 15200,
          engagement: 8.7,
          followers_growth: 156,
          top_posts: recentPosts.filter(post => post.platform === 'Twitter')
        },
        {
          platform: 'LinkedIn',
          period: 'last_7_days',
          reach: 8900,
          engagement: 12.3,
          followers_growth: 89,
          top_posts: recentPosts.filter(post => post.platform === 'LinkedIn')
        }
      ];
      setAnalytics(mockAnalytics);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    }
  }, [recentPosts]);

  const createPost = useCallback(async (scheduleTime?: string) => {
    if (!postContent.trim()) {
      showError(new Error('Please enter post content'), 'Create post');
      return;
    }

    if (selectedPlatforms.length === 0) {
      showError(new Error('Please select at least one platform'), 'Create post');
      return;
    }

    const isScheduled = !!scheduleTime;
    setIsPosting(!isScheduled);
    setIsScheduling(isScheduled);

    try {
      const hashtagArray = hashtags.split(',').map(tag => tag.trim()).filter(tag => tag);
      const mentionArray = mentions.split(',').map(mention => mention.trim()).filter(mention => mention);

      for (const platformId of selectedPlatforms) {
        const platform = socialPlatforms.find(p => p.id === platformId);
        if (!platform) continue;

        const newPost: Partial<SocialPost> = {
          content: postContent,
          platform: platform.name,
          status: isScheduled ? 'Scheduled' : 'Published',
          scheduled_at: scheduleTime,
          published_at: isScheduled ? undefined : new Date().toISOString(),
          metrics: { likes: 0, comments: 0, shares: 0, views: 0, engagement_rate: 0 },
          hashtags: hashtagArray,
          mentions: mentionArray,
          created_at: new Date().toISOString()
        };

        const { data, error } = await supabase
          .from('social_posts')
          .insert([newPost])
          .select()
          .single();

        if (error) throw error;

        setPosts(prev => [data, ...prev]);

        if (isScheduled) {
          setScheduledPosts(prev => [data, ...prev]);
        } else {
          setRecentPosts(prev => [data, ...prev.slice(0, 9)]);
        }
      }

      // Reset form
      setPostContent('');
      setSelectedPlatforms([]);
      setHashtags('');
      setMentions('');
      setMediaFiles([]);

      showSuccess(isScheduled ? 'Post scheduled successfully' : 'Post published successfully');
    } catch (error) {
      showError(error, isScheduled ? 'Schedule post' : 'Publish post');
    } finally {
      setIsPosting(false);
      setIsScheduling(false);
    }
  }, [postContent, selectedPlatforms, hashtags, mentions, socialPlatforms, showError, showSuccess]);

  const deletePost = useCallback(async (postId: string) => {
    try {
      const { error } = await supabase
        .from('social_posts')
        .delete()
        .eq('id', postId);

      if (error) throw error;

      setPosts(prev => prev.filter(post => post.id !== postId));
      setScheduledPosts(prev => prev.filter(post => post.id !== postId));
      setRecentPosts(prev => prev.filter(post => post.id !== postId));

      showSuccess('Post deleted successfully');
    } catch (error) {
      showError(error, 'Delete post');
    }
  }, [showError, showSuccess]);

  const connectPlatform = useCallback(async (platformId: string) => {
    try {
      // Simulate OAuth connection
      await new Promise(resolve => setTimeout(resolve, 2000));

      setSocialPlatforms(prev => prev.map(platform =>
        platform.id === platformId
          ? { ...platform, connected: true, last_sync: new Date().toISOString() }
          : platform
      ));

      showSuccess('Platform connected successfully');
    } catch (error) {
      showError(error, 'Connect platform');
    }
  }, [showError, showSuccess]);

  const disconnectPlatform = useCallback(async (platformId: string) => {
    try {
      setSocialPlatforms(prev => prev.map(platform =>
        platform.id === platformId
          ? { ...platform, connected: false, access_token: undefined, account_id: undefined }
          : platform
      ));

      showSuccess('Platform disconnected successfully');
    } catch (error) {
      showError(error, 'Disconnect platform');
    }
  }, [showError, showSuccess]);

  const useTemplate = useCallback((template: ContentTemplate) => {
    setPostContent(template.content);
    setHashtags(template.hashtags.join(', '));
    setSelectedTemplate(template.id);
    showSuccess(`Template "${template.name}" applied`);
  }, [showSuccess]);

  const getPlatformIcon = (platform: string) => {
    const icons = {
      'Twitter': '🐦',
      'LinkedIn': '💼',
      'Facebook': '📘',
      'Instagram': '📸'
    };
    return icons[platform as keyof typeof icons] || '📱';
  };

  const getCharacterLimit = (platform: string) => {
    const limits = {
      'Twitter': 280,
      'LinkedIn': 3000,
      'Facebook': 63206,
      'Instagram': 2200
    };
    return limits[platform as keyof typeof limits] || 280;
  };

  const getMaxCharacterLimit = () => {
    if (selectedPlatforms.length === 0) return 280;
    return Math.min(...selectedPlatforms.map(platformId => {
      const platform = socialPlatforms.find(p => p.id === platformId);
      return platform ? getCharacterLimit(platform.name) : 280;
    }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Share2 className="h-6 w-6 text-purple-600" />
        <h1 className="text-2xl font-bold">Social Media</h1>
      </div>

      <Tabs defaultValue="dashboard" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
          <TabsTrigger value="compose">Compose</TabsTrigger>
          <TabsTrigger value="schedule">Schedule</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="dashboard" className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold">Social Media Dashboard</h2>
            <Button variant="outline" onClick={() => { loadSocialPlatforms(); loadPosts(); }}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {socialPlatforms.map((platform) => (
              <Card key={platform.id} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-2">
                      <div className={`w-3 h-3 rounded-full ${platform.color}`}></div>
                      <span className="font-medium">{platform.name}</span>
                    </div>
                    <Badge variant={platform.connected ? "default" : "secondary"}>
                      {platform.connected ? (
                        <>
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Connected
                        </>
                      ) : (
                        <>
                          <AlertCircle className="h-3 w-3 mr-1" />
                          Disconnected
                        </>
                      )}
                    </Badge>
                  </div>

                  <div className="space-y-2">
                    <div>
                      <div className="text-2xl font-bold">{platform.followers}</div>
                      <div className="text-sm text-gray-600">Followers</div>
                    </div>

                    {platform.connected && platform.last_sync && (
                      <div className="text-xs text-gray-500">
                        Last sync: {new Date(platform.last_sync).toLocaleString()}
                      </div>
                    )}
                  </div>

                  <div className="mt-3 flex space-x-2">
                    {platform.connected ? (
                      <>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => disconnectPlatform(platform.id)}
                        >
                          <Settings className="h-3 w-3 mr-1" />
                          Settings
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => disconnectPlatform(platform.id)}
                          className="text-red-600"
                        >
                          Disconnect
                        </Button>
                      </>
                    ) : (
                      <Button
                        size="sm"
                        onClick={() => connectPlatform(platform.id)}
                        className="w-full"
                      >
                        <Link className="h-3 w-3 mr-1" />
                        Connect
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Clock className="h-5 w-5" />
                    <span>Scheduled Posts ({scheduledPosts.length})</span>
                  </div>
                  <Button variant="outline" size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    Schedule
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {scheduledPosts.length > 0 ? (
                  scheduledPosts.map((post) => (
                    <div key={post.id} className="border rounded-lg p-3 hover:shadow-sm transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span>{getPlatformIcon(post.platform)}</span>
                          <span className="font-medium">{post.platform}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="bg-blue-50">
                            <Clock className="h-3 w-3 mr-1" />
                            {post.status}
                          </Badge>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deletePost(post.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700 mb-2 line-clamp-2">{post.content}</p>
                      {post.hashtags && post.hashtags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {post.hashtags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              #{tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {post.scheduled_at ? new Date(post.scheduled_at).toLocaleString() : 'Not scheduled'}
                        </div>
                        <Button variant="ghost" size="sm">
                          <Edit className="h-3 w-3 mr-1" />
                          Edit
                        </Button>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">No scheduled posts</p>
                    <p className="text-xs text-gray-500">Schedule posts to see them here</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5" />
                    <span>Recent Posts ({recentPosts.length})</span>
                  </div>
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4 mr-2" />
                    View All
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {recentPosts.length > 0 ? (
                  recentPosts.map((post) => (
                    <div key={post.id} className="border rounded-lg p-3 hover:shadow-sm transition-shadow">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span>{getPlatformIcon(post.platform)}</span>
                          <span className="font-medium">{post.platform}</span>
                        </div>
                        <span className="text-xs text-gray-500">
                          {post.published_at ? new Date(post.published_at).toLocaleDateString() : 'Unknown'}
                        </span>
                      </div>
                      <p className="text-sm text-gray-700 mb-2 line-clamp-2">{post.content}</p>
                      {post.hashtags && post.hashtags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-2">
                          {post.hashtags.slice(0, 3).map((tag, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              #{tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Heart className="h-3 w-3" />
                            <span>{post.metrics.likes}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MessageCircle className="h-3 w-3" />
                            <span>{post.metrics.comments}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Repeat2 className="h-3 w-3" />
                            <span>{post.metrics.shares}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Eye className="h-3 w-3" />
                            <span>{post.metrics.views}</span>
                          </div>
                        </div>
                        <div className="text-xs text-gray-500">
                          {post.metrics.engagement_rate.toFixed(1)}% engagement
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8">
                    <BarChart3 className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">No recent posts</p>
                    <p className="text-xs text-gray-500">Published posts will appear here</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="compose" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Plus className="h-5 w-5" />
                <span>Create New Post</span>
              </CardTitle>
              <CardDescription>
                Compose and publish content across your social media platforms
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Content Templates */}
              {templates.length > 0 && (
                <div className="space-y-2">
                  <Label>Use Template (Optional)</Label>
                  <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                    <SelectTrigger>
                      <SelectValue placeholder="Choose a template" />
                    </SelectTrigger>
                    <SelectContent>
                      {templates.map((template) => (
                        <SelectItem key={template.id} value={template.id}>
                          {template.name} - {template.category}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {selectedTemplate && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const template = templates.find(t => t.id === selectedTemplate);
                        if (template) useTemplate(template);
                      }}
                    >
                      <Copy className="h-4 w-4 mr-2" />
                      Apply Template
                    </Button>
                  )}
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="post-content">Post Content *</Label>
                <Textarea
                  id="post-content"
                  placeholder="What's on your mind?"
                  rows={6}
                  value={postContent}
                  onChange={(e) => setPostContent(e.target.value)}
                />
                <div className="flex justify-between text-sm">
                  <span className={`${postContent.length > getMaxCharacterLimit() ? 'text-red-500' : 'text-gray-500'}`}>
                    {postContent.length}/{getMaxCharacterLimit()} characters
                  </span>
                  {selectedPlatforms.length > 0 && (
                    <span className="text-gray-500">
                      Limit based on selected platforms
                    </span>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="hashtags">Hashtags</Label>
                  <Input
                    id="hashtags"
                    placeholder="tag1, tag2, tag3"
                    value={hashtags}
                    onChange={(e) => setHashtags(e.target.value)}
                  />
                  <p className="text-xs text-gray-500">Separate hashtags with commas</p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="mentions">Mentions</Label>
                  <Input
                    id="mentions"
                    placeholder="@user1, @user2"
                    value={mentions}
                    onChange={(e) => setMentions(e.target.value)}
                  />
                  <p className="text-xs text-gray-500">Separate mentions with commas</p>
                </div>
              </div>

              <div className="space-y-2">
                <Label>Select Platforms *</Label>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                  {socialPlatforms.filter(p => p.connected).map((platform) => (
                    <div key={platform.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={platform.id}
                        checked={selectedPlatforms.includes(platform.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPlatforms(prev => [...prev, platform.id]);
                          } else {
                            setSelectedPlatforms(prev => prev.filter(id => id !== platform.id));
                          }
                        }}
                      />
                      <Label htmlFor={platform.id} className="flex items-center space-x-1 cursor-pointer">
                        <span>{getPlatformIcon(platform.name)}</span>
                        <span>{platform.name}</span>
                      </Label>
                    </div>
                  ))}
                </div>
                {socialPlatforms.filter(p => p.connected).length === 0 && (
                  <p className="text-sm text-gray-500">
                    No connected platforms. Connect platforms in the Dashboard tab.
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label>Media (Optional)</Label>
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                  <ImageIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-600 mb-2">Upload images or videos</p>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Choose Files
                  </Button>
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Button
                  onClick={() => createPost()}
                  disabled={isPosting || !postContent.trim() || selectedPlatforms.length === 0}
                >
                  {isPosting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Publishing...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      Post Now
                    </>
                  )}
                </Button>

                <Dialog>
                  <DialogTrigger asChild>
                    <Button variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      Schedule
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Schedule Post</DialogTitle>
                      <DialogDescription>
                        Choose when to publish your post
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="schedule-date">Date</Label>
                          <Input
                            id="schedule-date"
                            type="date"
                            value={scheduledDate}
                            onChange={(e) => setScheduledDate(e.target.value)}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="schedule-time">Time</Label>
                          <Input
                            id="schedule-time"
                            type="time"
                            value={scheduledTime}
                            onChange={(e) => setScheduledTime(e.target.value)}
                          />
                        </div>
                      </div>
                      <Button
                        onClick={() => {
                          if (scheduledDate && scheduledTime) {
                            const scheduleDateTime = new Date(`${scheduledDate}T${scheduledTime}`);
                            createPost(scheduleDateTime.toISOString());
                          }
                        }}
                        disabled={isScheduling || !scheduledDate || !scheduledTime}
                        className="w-full"
                      >
                        {isScheduling ? (
                          <>
                            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                            Scheduling...
                          </>
                        ) : (
                          <>
                            <Calendar className="h-4 w-4 mr-2" />
                            Schedule Post
                          </>
                        )}
                      </Button>
                    </div>
                  </DialogContent>
                </Dialog>

                <Button variant="outline">
                  <Copy className="h-4 w-4 mr-2" />
                  Save Draft
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="schedule" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Calendar className="h-5 w-5" />
                <span>Schedule Posts</span>
              </CardTitle>
              <CardDescription>
                Plan and schedule your social media content in advance
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="schedule-content">Post Content</Label>
                  <Textarea
                    id="schedule-content"
                    placeholder="Enter your post content..."
                    rows={3}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="schedule-time">Schedule Time</Label>
                  <Input
                    id="schedule-time"
                    type="datetime-local"
                    value={scheduledTime}
                    onChange={(e) => setScheduledTime(e.target.value)}
                  />
                </div>
              </div>

              <Button>Schedule Post</Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Upcoming Posts</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {scheduledPosts.map((post, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span>{getPlatformIcon(post.platform)}</span>
                        <span className="font-medium">{post.platform}</span>
                      </div>
                      <p className="text-sm text-gray-700">{post.content}</p>
                      <div className="flex items-center text-xs text-gray-500 mt-1">
                        <Calendar className="h-3 w-3 mr-1" />
                        {post.scheduledFor}
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">Edit</Button>
                      <Button variant="outline" size="sm">Delete</Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  <span className="text-sm font-medium">Total Reach</span>
                </div>
                <div className="text-2xl font-bold mt-2">45.2K</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +15% this week
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Heart className="h-5 w-5 text-red-600" />
                  <span className="text-sm font-medium">Engagement</span>
                </div>
                <div className="text-2xl font-bold mt-2">8.7%</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +2.3% vs last week
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <MessageCircle className="h-5 w-5 text-green-600" />
                  <span className="text-sm font-medium">Comments</span>
                </div>
                <div className="text-2xl font-bold mt-2">234</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +28% this week
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-2">
                  <Repeat2 className="h-5 w-5 text-purple-600" />
                  <span className="text-sm font-medium">Shares</span>
                </div>
                <div className="text-2xl font-bold mt-2">89</div>
                <div className="flex items-center text-sm text-green-600 mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +12% this week
                </div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Platform Performance</CardTitle>
              <CardDescription>
                Compare performance across different social media platforms
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Detailed Analytics</h3>
                <p className="text-gray-600">
                  Advanced analytics and insights coming soon
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SocialMedia;
