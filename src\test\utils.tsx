import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { BrowserRouter } from 'react-router-dom'
import { ThemeProvider } from '@/components/theme-provider'
import { TooltipProvider } from '@/components/ui/tooltip'
import { AppProvider } from '@/contexts/AppContext'

// Create a custom render function that includes providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: Infinity,
      },
      mutations: {
        retry: false,
      },
    },
  })

  return (
    <ThemeProvider defaultTheme="light">
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <BrowserRouter>
            <AppProvider>
              {children}
            </AppProvider>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  )
}

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options })

export * from '@testing-library/react'
export { customRender as render }

// Mock data factories
export const createMockScrapingJob = (overrides = {}) => ({
  id: 'test-job-id',
  url: 'https://example.com',
  status: 'completed' as const,
  created_at: '2024-01-01T00:00:00Z',
  completed_at: '2024-01-01T00:01:00Z',
  results: { title: 'Test Page', content: 'Test content' },
  ...overrides,
})

export const createMockScrapedData = (overrides = {}) => ({
  id: 'test-scraped-id',
  job_id: 'test-job-id',
  url: 'https://example.com',
  title: 'Test Page',
  content: 'This is test content with some keywords for testing purposes.',
  metadata: {
    headings: ['Test Heading'],
    links: ['https://example.com/link'],
    wordCount: 10,
  },
  scraped_at: '2024-01-01T00:01:00Z',
  scraping_jobs: createMockScrapingJob(),
  ...overrides,
})

export const createMockSEOAnalysis = (overrides = {}) => ({
  id: 'test-seo-id',
  scraped_data_id: 'test-scraped-id',
  overall_score: 75,
  title_score: 80,
  content_score: 70,
  technical_score: 75,
  keyword_score: 80,
  analysis_details: {
    titleAnalysis: {
      score: 80,
      issues: [],
      recommendations: ['Consider adding more descriptive keywords'],
    },
    contentAnalysis: {
      score: 70,
      wordCount: 150,
      readabilityScore: 75,
      issues: ['Content could be longer'],
      recommendations: ['Add more detailed content'],
    },
    technicalSEO: {
      score: 75,
      issues: [],
      recommendations: ['Add meta description'],
    },
    keywordAnalysis: {
      score: 80,
      topKeywords: [
        { keyword: 'test', frequency: 5, density: 2.5 },
        { keyword: 'content', frequency: 3, density: 1.5 },
      ],
      issues: [],
      recommendations: ['Consider adding more relevant keywords'],
    },
  },
  recommendations: ['Improve content length', 'Add meta description'],
  issues: ['Content is too short'],
  analyzed_at: '2024-01-01T00:02:00Z',
  ...overrides,
})

export const createMockKeywordData = (overrides = {}) => ({
  keyword: 'test',
  frequency: 5,
  context: 'This is a test keyword in context',
  ...overrides,
})

export const createMockLeadData = (overrides = {}) => ({
  email: '<EMAIL>',
  name: 'John Doe',
  company: 'Test Company',
  url: 'https://example.com',
  title: 'Test Page',
  ...overrides,
})

export const createMockAnalyticsStats = (overrides = {}) => ({
  totalJobs: 10,
  completedJobs: 8,
  totalData: 8,
  avgWordCount: 150,
  ...overrides,
})

// Helper functions for testing
export const waitForLoadingToFinish = () => {
  return new Promise(resolve => setTimeout(resolve, 0))
}

export const mockConsoleError = () => {
  const originalError = console.error
  console.error = vi.fn()
  return () => {
    console.error = originalError
  }
}

export const mockConsoleWarn = () => {
  const originalWarn = console.warn
  console.warn = vi.fn()
  return () => {
    console.warn = originalWarn
  }
}
