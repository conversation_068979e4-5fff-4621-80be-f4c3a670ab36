-- Quick fix for 406 errors - Create missing tables
-- Run this in your Supabase SQL Editor to create the missing tables that are causing 406 errors

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Lead Campaigns Table
-- Stores lead generation campaigns with targeting criteria and performance metrics
CREATE TABLE IF NOT EXISTS lead_campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Active', 'Paused', 'Completed', 'Draft')),
    leads INTEGER DEFAULT 0,
    qualified INTEGER DEFAULT 0,
    converted INTEGER DEFAULT 0,
    conversion_rate DECIMAL(5,2) DEFAULT 0.0,
    target_criteria JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_leads_count CHECK (leads >= 0),
    CONSTRAINT valid_qualified_count CHECK (qualified >= 0 AND qualified <= leads),
    CONSTRAINT valid_converted_count CHECK (converted >= 0 AND converted <= qualified),
    CONSTRAINT valid_conversion_rate CHECK (conversion_rate >= 0.0 AND conversion_rate <= 100.0)
);

-- Lead Management Table
-- Stores leads for the LeadGeneration component (separate from scraped leads)
CREATE TABLE IF NOT EXISTS lead_management (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    email TEXT NOT NULL,
    phone TEXT,
    company TEXT NOT NULL,
    title TEXT NOT NULL,
    location TEXT NOT NULL,
    score INTEGER DEFAULT 50,
    status TEXT NOT NULL DEFAULT 'Cold' CHECK (status IN ('Hot', 'Warm', 'Cold', 'Qualified', 'Converted', 'Lost')),
    source TEXT NOT NULL DEFAULT 'Manual',
    tags JSONB DEFAULT '[]',
    notes TEXT DEFAULT '',
    last_contact TIMESTAMP WITH TIME ZONE,
    next_follow_up TIMESTAMP WITH TIME ZONE,
    social_profiles JSONB DEFAULT '{}',
    company_info JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_email_format CHECK (email ~ '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$'),
    CONSTRAINT valid_score_range CHECK (score >= 0 AND score <= 100)
);

-- Insert sample data for lead_campaigns
INSERT INTO lead_campaigns (name, description, status, leads, qualified, converted, conversion_rate, target_criteria) VALUES
('SaaS Decision Makers', 'Target decision makers in SaaS companies', 'Active', 1250, 89, 23, 25.8, '{"industries": ["Technology", "SaaS"], "job_titles": ["CEO", "CTO", "VP"], "company_sizes": ["100-500", "500+"]}'),
('E-commerce Managers', 'Marketing managers in e-commerce', 'Paused', 890, 67, 18, 26.9, '{"industries": ["E-commerce", "Retail"], "job_titles": ["Marketing Manager", "Marketing Director"]}'),
('Enterprise Sales', 'Enterprise sales professionals', 'Draft', 0, 0, 0, 0.0, '{"industries": ["Enterprise Software"], "job_titles": ["Sales Director", "VP Sales"]}')
ON CONFLICT DO NOTHING;

-- Insert sample data for lead_management
INSERT INTO lead_management (name, email, phone, company, title, location, score, status, source, tags, notes, social_profiles, company_info) VALUES
('Sarah Johnson', '<EMAIL>', '+****************', 'TechCorp Inc.', 'Marketing Director', 'San Francisco, CA', 85, 'Hot', 'LinkedIn', '["Enterprise", "SaaS"]', 'Interested in our enterprise solution', '{"linkedin": "https://linkedin.com/in/sarahjohnson", "website": "https://techcorp.com"}', '{"industry": "Technology", "size": "500-1000", "revenue": "$50M-100M"}'),
('Michael Chen', '<EMAIL>', '+****************', 'DataFlow Solutions', 'VP of Sales', 'New York, NY', 72, 'Warm', 'Website', '["Mid-market", "Analytics"]', 'Downloaded whitepaper, needs follow-up', '{}', '{"industry": "Data Analytics", "size": "100-500", "revenue": "$10M-50M"}'),
('Emily Rodriguez', '<EMAIL>', '+****************', 'Innovate Co', 'CTO', 'Austin, TX', 91, 'Hot', 'Referral', '["Enterprise", "AI"]', 'Referred by existing customer', '{"linkedin": "https://linkedin.com/in/emilyrodriguez"}', '{"industry": "AI/ML", "size": "200-500", "revenue": "$25M-50M"}')
ON CONFLICT DO NOTHING;

-- Verify tables were created successfully
SELECT 'Tables created successfully!' as status;

-- Check if the tables exist
SELECT table_name FROM information_schema.tables
WHERE table_schema = 'public'
AND table_name IN ('lead_campaigns', 'lead_management')
ORDER BY table_name;

-- Show sample data
SELECT 'Lead Campaigns:' as info;
SELECT name, status, leads, qualified, converted FROM lead_campaigns LIMIT 3;

SELECT 'Lead Management:' as info;
SELECT name, company, title, status, score FROM lead_management LIMIT 3;

-- User Profiles Table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    first_name TEXT NOT NULL,
    last_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    company TEXT,
    bio TEXT,
    avatar_url TEXT,
    phone TEXT,
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    language TEXT NOT NULL DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User Settings Table
CREATE TABLE IF NOT EXISTS user_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    theme TEXT NOT NULL DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
    language TEXT NOT NULL DEFAULT 'en',
    timezone TEXT NOT NULL DEFAULT 'America/New_York',
    notifications JSONB NOT NULL DEFAULT '{
        "email": true,
        "push": false,
        "marketing": true
    }',
    privacy JSONB NOT NULL DEFAULT '{
        "analytics": true,
        "data_collection": true
    }',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Notification Settings Table
CREATE TABLE IF NOT EXISTS notification_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    email BOOLEAN NOT NULL DEFAULT true,
    push BOOLEAN NOT NULL DEFAULT false,
    sms BOOLEAN NOT NULL DEFAULT false,
    marketing BOOLEAN NOT NULL DEFAULT true,
    security_alerts BOOLEAN NOT NULL DEFAULT true,
    export_complete BOOLEAN NOT NULL DEFAULT true,
    weekly_reports BOOLEAN NOT NULL DEFAULT false,
    system_updates BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Privacy Settings Table
CREATE TABLE IF NOT EXISTS privacy_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    dataCollection BOOLEAN NOT NULL DEFAULT true,
    analytics BOOLEAN NOT NULL DEFAULT true,
    thirdParty BOOLEAN NOT NULL DEFAULT false,
    cookieConsent BOOLEAN NOT NULL DEFAULT true,
    dataRetention INTEGER NOT NULL DEFAULT 365,
    shareUsageData BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- Security Settings Table
CREATE TABLE IF NOT EXISTS security_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    two_factor_enabled BOOLEAN NOT NULL DEFAULT false,
    password_last_changed TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    login_notifications BOOLEAN NOT NULL DEFAULT true,
    session_timeout INTEGER NOT NULL DEFAULT 60,
    ip_whitelist JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id)
);

-- API Keys Table
CREATE TABLE IF NOT EXISTS api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    key TEXT UNIQUE NOT NULL,
    permissions JSONB NOT NULL DEFAULT '["read"]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_used TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    is_active BOOLEAN NOT NULL DEFAULT true
);

-- Notifications Table (for in-app notifications)
CREATE TABLE IF NOT EXISTS notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    type TEXT NOT NULL DEFAULT 'info' CHECK (type IN ('info', 'success', 'warning', 'error')),
    read BOOLEAN NOT NULL DEFAULT FALSE,
    action_url TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    read_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '30 days')
);

-- Saved Keywords Table
CREATE TABLE IF NOT EXISTS saved_keywords (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES user_profiles(id) ON DELETE CASCADE,
    keyword TEXT NOT NULL,
    volume TEXT,
    difficulty INTEGER,
    cpc TEXT,
    trend TEXT,
    competition TEXT,
    intent TEXT,
    serp_features JSONB DEFAULT '[]',
    related_keywords JSONB DEFAULT '[]',
    questions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_email ON user_profiles(email);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_notification_settings_user_id ON notification_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_privacy_settings_user_id ON privacy_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_security_settings_user_id ON security_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_user_id ON api_keys(user_id);
CREATE INDEX IF NOT EXISTS idx_api_keys_key ON api_keys(key);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_saved_keywords_user_id ON saved_keywords(user_id);

-- Enable Row Level Security
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE privacy_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE security_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE saved_keywords ENABLE ROW LEVEL SECURITY;

-- Create permissive RLS policies for demo (replace with proper user-based policies in production)
-- Use DO blocks to handle existing policies gracefully
DO $$
BEGIN
    -- Drop existing policies if they exist, then recreate them
    DROP POLICY IF EXISTS "Allow all operations on user_profiles" ON user_profiles;
    CREATE POLICY "Allow all operations on user_profiles" ON user_profiles FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on user_settings" ON user_settings;
    CREATE POLICY "Allow all operations on user_settings" ON user_settings FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on notification_settings" ON notification_settings;
    CREATE POLICY "Allow all operations on notification_settings" ON notification_settings FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on privacy_settings" ON privacy_settings;
    CREATE POLICY "Allow all operations on privacy_settings" ON privacy_settings FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on security_settings" ON security_settings;
    CREATE POLICY "Allow all operations on security_settings" ON security_settings FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on api_keys" ON api_keys;
    CREATE POLICY "Allow all operations on api_keys" ON api_keys FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on notifications" ON notifications;
    CREATE POLICY "Allow all operations on notifications" ON notifications FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on saved_keywords" ON saved_keywords;
    CREATE POLICY "Allow all operations on saved_keywords" ON saved_keywords FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on lead_campaigns" ON lead_campaigns;
    CREATE POLICY "Allow all operations on lead_campaigns" ON lead_campaigns FOR ALL USING (true);

    DROP POLICY IF EXISTS "Allow all operations on lead_management" ON lead_management;
    CREATE POLICY "Allow all operations on lead_management" ON lead_management FOR ALL USING (true);
END $$;

-- Insert sample data for testing
INSERT INTO notifications (title, message, type) VALUES 
('Welcome!', 'Welcome to MarketCrawler Pro', 'info'),
('Feature Update', 'New keyword research features are now available', 'success')
ON CONFLICT DO NOTHING;
