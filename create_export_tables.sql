-- Create missing tables for Data Export functionality and User Sessions
-- Run this script in your Supabase SQL editor

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they exist (for clean recreation)
DROP TABLE IF EXISTS session_activities CASCADE;
DROP TABLE IF EXISTS user_sessions CASCADE;
DROP TABLE IF EXISTS export_history CASCADE;
DROP TABLE IF EXISTS scheduled_exports CASCADE;
DROP TABLE IF EXISTS export_settings CASCADE;
DROP TABLE IF EXISTS cloud_destinations CASCADE;

-- User Sessions Table
-- Stores active user sessions for security and session management
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    session_token TEXT NOT NULL UNIQUE,
    ip_address TEXT NOT NULL,
    user_agent TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_activity TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    device_info JSONB DEFAULT '{}'
);

-- Session Activities Table
-- Tracks user activities within sessions for security auditing
CREATE TABLE session_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id UUID NOT NULL REFERENCES user_sessions(id) ON DELETE CASCADE,
    activity_type TEXT NOT NULL CHECK (activity_type IN ('login', 'logout', 'page_view', 'api_call', 'password_change', 'settings_change')),
    details JSONB DEFAULT '{}',
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    ip_address TEXT NOT NULL
);

-- Create export_history table
CREATE TABLE export_history (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    export_name TEXT NOT NULL,
    export_type TEXT NOT NULL CHECK (export_type IN ('Manual', 'Scheduled', 'API')),
    table_name TEXT NOT NULL,
    format TEXT NOT NULL CHECK (format IN ('CSV', 'JSON', 'Excel', 'PDF')),
    status TEXT NOT NULL DEFAULT 'Pending' CHECK (status IN ('Pending', 'In Progress', 'Completed', 'Failed', 'Cancelled')),
    file_size BIGINT DEFAULT 0,
    file_path TEXT,
    download_url TEXT,
    row_count INTEGER DEFAULT 0,
    filters JSONB DEFAULT '{}',
    columns JSONB DEFAULT '[]',
    destination TEXT CHECK (destination IN ('Local', 'Cloud', 'Email')),
    destination_id UUID,
    error_message TEXT,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create scheduled_exports table
CREATE TABLE scheduled_exports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    table_name TEXT NOT NULL,
    format TEXT NOT NULL CHECK (format IN ('CSV', 'JSON', 'Excel', 'PDF')),
    schedule_type TEXT NOT NULL CHECK (schedule_type IN ('Daily', 'Weekly', 'Monthly', 'Custom')),
    cron_expression TEXT,
    filters JSONB DEFAULT '{}',
    columns JSONB DEFAULT '[]',
    destination TEXT NOT NULL CHECK (destination IN ('Local', 'Cloud', 'Email')),
    destination_id UUID,
    email_recipients JSONB DEFAULT '[]',
    is_active BOOLEAN DEFAULT true,
    last_run_at TIMESTAMP WITH TIME ZONE,
    next_run_at TIMESTAMP WITH TIME ZONE,
    run_count INTEGER DEFAULT 0,
    success_count INTEGER DEFAULT 0,
    failure_count INTEGER DEFAULT 0,
    created_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create export_settings table (single row with all settings)
CREATE TABLE export_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    default_format TEXT NOT NULL DEFAULT 'csv',
    include_headers BOOLEAN DEFAULT true,
    compress_files BOOLEAN DEFAULT false,
    email_notification BOOLEAN DEFAULT true,
    max_records INTEGER DEFAULT 100000,
    chunk_size INTEGER DEFAULT 10000,
    retention_days INTEGER DEFAULT 30,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create cloud_destinations table
CREATE TABLE cloud_destinations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('AWS_S3', 'Google_Drive', 'Dropbox', 'Azure_Blob', 'FTP', 'SFTP')),
    configuration JSONB NOT NULL DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    is_default BOOLEAN DEFAULT false,
    last_used_at TIMESTAMP WITH TIME ZONE,
    connection_status TEXT DEFAULT 'Unknown' CHECK (connection_status IN ('Connected', 'Disconnected', 'Error', 'Unknown')),
    last_test_at TIMESTAMP WITH TIME ZONE,
    created_by TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
-- Session tables indexes
CREATE INDEX idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX idx_user_sessions_session_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_is_active ON user_sessions(is_active);
CREATE INDEX idx_user_sessions_expires_at ON user_sessions(expires_at);
CREATE INDEX idx_user_sessions_last_activity ON user_sessions(last_activity);
CREATE INDEX idx_session_activities_session_id ON session_activities(session_id);
CREATE INDEX idx_session_activities_activity_type ON session_activities(activity_type);
CREATE INDEX idx_session_activities_timestamp ON session_activities(timestamp);

-- Export tables indexes
CREATE INDEX idx_export_history_status ON export_history(status);
CREATE INDEX idx_export_history_created_at ON export_history(created_at);
CREATE INDEX idx_export_history_table_name ON export_history(table_name);
CREATE INDEX idx_export_history_export_type ON export_history(export_type);
CREATE INDEX idx_scheduled_exports_is_active ON scheduled_exports(is_active);
CREATE INDEX idx_scheduled_exports_next_run_at ON scheduled_exports(next_run_at);
CREATE INDEX idx_scheduled_exports_table_name ON scheduled_exports(table_name);
CREATE INDEX idx_export_settings_default_format ON export_settings(default_format);
CREATE INDEX idx_cloud_destinations_type ON cloud_destinations(type);
CREATE INDEX idx_cloud_destinations_is_active ON cloud_destinations(is_active);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE user_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE session_activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE export_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE scheduled_exports ENABLE ROW LEVEL SECURITY;
ALTER TABLE export_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE cloud_destinations ENABLE ROW LEVEL SECURITY;

-- Create permissive policies (you can make these more restrictive later)
CREATE POLICY "Allow all operations on user_sessions" ON user_sessions FOR ALL USING (true);
CREATE POLICY "Allow all operations on session_activities" ON session_activities FOR ALL USING (true);
CREATE POLICY "Allow all operations on export_history" ON export_history FOR ALL USING (true);
CREATE POLICY "Allow all operations on scheduled_exports" ON scheduled_exports FOR ALL USING (true);
CREATE POLICY "Allow all operations on export_settings" ON export_settings FOR ALL USING (true);
CREATE POLICY "Allow all operations on cloud_destinations" ON cloud_destinations FOR ALL USING (true);

-- Create triggers to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_export_history_updated_at 
    BEFORE UPDATE ON export_history 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_scheduled_exports_updated_at 
    BEFORE UPDATE ON scheduled_exports 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_export_settings_updated_at 
    BEFORE UPDATE ON export_settings 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_cloud_destinations_updated_at 
    BEFORE UPDATE ON cloud_destinations 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();



-- Verify tables were created successfully
SELECT 'Session and Export tables created successfully!' as status;

-- Show table information
SELECT
    table_name,
    (SELECT COUNT(*) FROM information_schema.columns WHERE table_name = t.table_name AND table_schema = 'public') as column_count
FROM information_schema.tables t
WHERE table_schema = 'public'
AND table_name IN ('user_sessions', 'session_activities', 'export_history', 'scheduled_exports', 'export_settings', 'cloud_destinations')
ORDER BY table_name;

-- Show sample data counts
SELECT 'user_sessions' as table_name, COUNT(*) as row_count FROM user_sessions
UNION ALL
SELECT 'session_activities' as table_name, COUNT(*) as row_count FROM session_activities
UNION ALL
SELECT 'export_history' as table_name, COUNT(*) as row_count FROM export_history
UNION ALL
SELECT 'scheduled_exports' as table_name, COUNT(*) as row_count FROM scheduled_exports
UNION ALL
SELECT 'export_settings' as table_name, COUNT(*) as row_count FROM export_settings
UNION ALL
SELECT 'cloud_destinations' as table_name, COUNT(*) as row_count FROM cloud_destinations;
