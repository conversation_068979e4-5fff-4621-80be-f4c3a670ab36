-- Social Media Tables Creation Script
-- Run this in your Supabase SQL Editor to create the missing social media tables
-- This will fix the 404 errors you're seeing in the SocialMedia component

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables if they have wrong structure (backup data first if needed)
DROP TABLE IF EXISTS social_posts CASCADE;

-- Social Platforms Table
-- Stores social media platform configurations and connection status
CREATE TABLE IF NOT EXISTS social_platforms (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL UNIQUE,
    connected BOOLEAN DEFAULT FALSE,
    followers TEXT DEFAULT '0',
    color TEXT NOT NULL DEFAULT 'bg-gray-500',
    icon TEXT NOT NULL DEFAULT '📱',
    access_token TEXT,
    account_id TEXT,
    last_sync TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_platform_name CHECK (name IN ('Twitter', 'LinkedIn', 'Facebook', 'Instagram', 'TikTok', 'YouTube'))
);

-- Content Templates Table
-- Stores reusable content templates for social media posts
CREATE TABLE IF NOT EXISTS content_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    content TEXT NOT NULL,
    category TEXT NOT NULL,
    hashtags JSONB DEFAULT '[]',
    usage_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_usage_count CHECK (usage_count >= 0),
    CONSTRAINT valid_content_length CHECK (char_length(content) > 0 AND char_length(content) <= 5000)
);

-- Social Posts Table
-- Stores social media posts with metrics and scheduling
CREATE TABLE social_posts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    content TEXT NOT NULL,
    platform TEXT NOT NULL,
    status TEXT NOT NULL DEFAULT 'Draft' CHECK (status IN ('Draft', 'Scheduled', 'Published', 'Failed')),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    published_at TIMESTAMP WITH TIME ZONE,
    metrics JSONB DEFAULT '{
        "likes": 0,
        "comments": 0,
        "shares": 0,
        "views": 0,
        "engagement_rate": 0
    }',
    media_urls JSONB DEFAULT '[]',
    hashtags JSONB DEFAULT '[]',
    mentions JSONB DEFAULT '[]',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

    -- Constraints
    CONSTRAINT valid_platform CHECK (platform IN ('Twitter', 'LinkedIn', 'Facebook', 'Instagram', 'TikTok', 'YouTube')),
    CONSTRAINT valid_content_length CHECK (char_length(content) > 0 AND char_length(content) <= 5000)
);

-- Insert default social platforms
INSERT INTO social_platforms (name, connected, followers, color, icon) VALUES
    ('Twitter', false, '0', 'bg-blue-500', '🐦'),
    ('LinkedIn', false, '0', 'bg-blue-700', '💼'),
    ('Facebook', false, '0', 'bg-blue-600', '📘'),
    ('Instagram', false, '0', 'bg-pink-500', '📸')
ON CONFLICT (name) DO NOTHING;

-- Insert default content templates
INSERT INTO content_templates (name, content, category, hashtags, usage_count) VALUES
    ('Product Launch', 'Excited to announce our latest product! 🚀 {{product_name}} is here to revolutionize {{industry}}.', 'Marketing', '["ProductLaunch", "Innovation", "TechNews"]', 0),
    ('Tips & Tricks', '💡 Pro tip: {{tip_content}} What are your favorite {{topic}} tips?', 'Educational', '["Tips", "ProTip", "Learning"]', 0),
    ('Behind the Scenes', 'Take a peek behind the scenes at {{company_name}}! {{description}}', 'Company Culture', '["BehindTheScenes", "TeamWork", "Culture"]', 0),
    ('Industry News', 'Breaking: {{news_headline}} What are your thoughts on this development? {{opinion}}', 'News', '["IndustryNews", "Breaking", "Trending"]', 0),
    ('Customer Success', '🎉 Success story alert! {{customer_name}} achieved {{achievement}} using our {{product}}. {{details}}', 'Success Stories', '["CustomerSuccess", "Testimonial", "Results"]', 0),
    ('Weekly Roundup', '📅 This week in review: {{summary}} What was your highlight this week?', 'Weekly Content', '["WeeklyRoundup", "Summary", "Engagement"]', 0),
    ('Question Post', '🤔 Quick question for our community: {{question}} Share your thoughts in the comments!', 'Engagement', '["Question", "Community", "Discussion"]', 0),
    ('Motivational Quote', '✨ "{{quote}}" - {{author}} What motivates you today?', 'Inspiration', '["Motivation", "Quote", "Inspiration"]', 0)
ON CONFLICT DO NOTHING;

-- Insert some sample social posts for testing
INSERT INTO social_posts (content, platform, status, scheduled_at, metrics, hashtags) VALUES
    ('Just launched our new web scraping tool! 🚀 Perfect for data analysts and marketers.', 'Twitter', 'Scheduled', NOW() + INTERVAL '2 hours', '{"likes": 0, "comments": 0, "shares": 0, "views": 0, "engagement_rate": 0}', '["WebScraping", "DataAnalytics", "Marketing"]'),
    ('Excited to share our latest insights on social media marketing trends for 2024!', 'LinkedIn', 'Published', NOW() - INTERVAL '1 day', '{"likes": 24, "comments": 8, "shares": 12, "views": 156, "engagement_rate": 28.2}', '["SocialMedia", "Marketing", "Trends2024"]'),
    ('Behind the scenes: How we built our scraping analysis platform 🔧', 'Facebook', 'Draft', NULL, '{"likes": 0, "comments": 0, "shares": 0, "views": 0, "engagement_rate": 0}', '["BehindTheScenes", "Development", "Tech"]')
ON CONFLICT DO NOTHING;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_social_platforms_connected ON social_platforms(connected);
CREATE INDEX IF NOT EXISTS idx_social_platforms_name ON social_platforms(name);
CREATE INDEX IF NOT EXISTS idx_content_templates_category ON content_templates(category);
CREATE INDEX IF NOT EXISTS idx_content_templates_usage_count ON content_templates(usage_count DESC);
CREATE INDEX IF NOT EXISTS idx_social_posts_platform ON social_posts(platform);
CREATE INDEX IF NOT EXISTS idx_social_posts_status ON social_posts(status);
CREATE INDEX IF NOT EXISTS idx_social_posts_created_at ON social_posts(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_social_posts_scheduled_at ON social_posts(scheduled_at);

-- Create triggers to update updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_social_platforms_updated_at
    BEFORE UPDATE ON social_platforms
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_content_templates_updated_at
    BEFORE UPDATE ON content_templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_social_posts_updated_at
    BEFORE UPDATE ON social_posts
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) - uncomment if you need user-specific access control
-- ALTER TABLE social_platforms ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE content_templates ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE social_posts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (uncomment and modify if you need user-specific data)
-- CREATE POLICY "Users can view all social platforms" ON social_platforms FOR SELECT USING (true);
-- CREATE POLICY "Users can view all content templates" ON content_templates FOR SELECT USING (true);
-- CREATE POLICY "Users can manage their own social posts" ON social_posts FOR ALL USING (auth.uid() IS NOT NULL);

-- Grant permissions to authenticated users
GRANT ALL ON social_platforms TO authenticated;
GRANT ALL ON content_templates TO authenticated;
GRANT ALL ON social_posts TO authenticated;

-- Grant permissions to anonymous users for read operations
GRANT SELECT ON social_platforms TO anon;
GRANT SELECT ON content_templates TO anon;

-- Verify tables were created successfully
DO $$
DECLARE
    table_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_name IN ('social_platforms', 'content_templates', 'social_posts')
    AND table_schema = 'public';

    IF table_count = 3 THEN
        RAISE NOTICE 'SUCCESS: All 3 social media tables created successfully!';
        RAISE NOTICE 'Tables: social_platforms, content_templates, social_posts';
    ELSE
        RAISE NOTICE 'WARNING: Only % out of 3 tables were created', table_count;
    END IF;
END $$;
