import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'

// Mock API responses
export const handlers = [
  // Mock Supabase API calls
  http.get('https://test.supabase.co/rest/v1/*', () => {
    return HttpResponse.json([])
  }),

  http.post('https://test.supabase.co/rest/v1/*', () => {
    return HttpResponse.json({ id: 'test-id' })
  }),

  http.patch('https://test.supabase.co/rest/v1/*', () => {
    return HttpResponse.json({ id: 'test-id' })
  }),

  http.delete('https://test.supabase.co/rest/v1/*', () => {
    return HttpResponse.json({})
  }),

  // Mock external scraping APIs
  http.get('https://api.allorigins.win/get', () => {
    return HttpResponse.json({
      contents: `
        <html>
          <head>
            <title>Test Page</title>
            <meta name="description" content="Test description">
          </head>
          <body>
            <h1>Test Heading</h1>
            <p>Test content with some keywords for testing.</p>
            <a href="https://example.com">Test Link</a>
          </body>
        </html>
      `
    })
  }),

  // Mock CORS proxy
  http.get('https://cors-anywhere.herokuapp.com/*', () => {
    return HttpResponse.text(`
      <html>
        <head>
          <title>CORS Proxy Test</title>
        </head>
        <body>
          <p>Test content from CORS proxy</p>
        </body>
      </html>
    `)
  }),

  // Mock direct website access
  http.get('https://example.com', () => {
    return HttpResponse.text(`
      <html>
        <head>
          <title>Example Domain</title>
          <meta name="description" content="Example website for testing">
        </head>
        <body>
          <h1>Example Domain</h1>
          <p>This domain is for use in illustrative examples in documents.</p>
          <p>Contact: <EMAIL></p>
        </body>
      </html>
    `)
  }),
]

export const server = setupServer(...handlers)
