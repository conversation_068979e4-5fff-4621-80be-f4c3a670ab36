<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid;
        }
        .success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Database Fix Verification</h1>
        <p>This page will test if the 406 errors have been resolved by checking if the missing database tables now exist.</p>
        
        <div id="config-info" class="test-result info">
            <strong>Configuration:</strong><br>
            Supabase URL: https://rclikclltlyzyojjttqv.supabase.co<br>
            Testing tables: notification_settings, privacy_settings, security_settings, user_settings
        </div>

        <button onclick="testDatabaseTables()">🧪 Test Database Tables</button>
        <button onclick="testBasicConnection()">🔗 Test Basic Connection</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>

        <div id="results"></div>
    </div>

    <script type="module">
        // Import Supabase
        import { createClient } from 'https://cdn.skypack.dev/@supabase/supabase-js@2';

        // Initialize Supabase client
        const supabaseUrl = 'https://rclikclltlyzyojjttqv.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTAzOTYzOTIsImV4cCI6MjA2NTk3MjM5Mn0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw';
        
        const supabase = createClient(supabaseUrl, supabaseKey);

        // Make functions available globally
        window.testDatabaseTables = testDatabaseTables;
        window.testBasicConnection = testBasicConnection;
        window.clearResults = clearResults;

        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function testBasicConnection() {
            addResult('🔄 Testing basic Supabase connection...', 'info');
            
            try {
                // Test basic connection
                const { data, error } = await supabase.from('user_settings').select('count', { count: 'exact', head: true });
                
                if (error) {
                    if (error.code === '42P01') {
                        addResult(`❌ Table 'user_settings' does not exist. Error: ${error.message}`, 'error');
                    } else {
                        addResult(`⚠️ Connection error: ${error.message} (Code: ${error.code})`, 'error');
                    }
                } else {
                    addResult('✅ Basic connection successful!', 'success');
                }
            } catch (err) {
                addResult(`💥 Connection failed: ${err.message}`, 'error');
            }
        }

        async function testDatabaseTables() {
            addResult('🔄 Testing database tables...', 'info');
            
            const tables = [
                'notification_settings',
                'privacy_settings', 
                'security_settings',
                'user_settings'
            ];
            
            const results = {};
            
            for (const table of tables) {
                try {
                    addResult(`Testing ${table}...`, 'info');
                    
                    // Test basic connection with count query
                    const { data, error, count } = await supabase
                        .from(table)
                        .select('*', { count: 'exact', head: true });
                    
                    if (error) {
                        results[table] = {
                            status: 'ERROR',
                            message: error.message,
                            code: error.code
                        };
                        
                        if (error.code === '42P01') {
                            addResult(`❌ ${table}: Table does not exist`, 'error');
                        } else if (error.code === 'PGRST106') {
                            addResult(`❌ ${table}: 406 error - ${error.message}`, 'error');
                        } else {
                            addResult(`❌ ${table}: ${error.message} (${error.code})`, 'error');
                        }
                    } else {
                        results[table] = {
                            status: 'SUCCESS',
                            count: count || 0
                        };
                        addResult(`✅ ${table}: Connected successfully (${count || 0} rows)`, 'success');
                    }
                } catch (err) {
                    results[table] = {
                        status: 'EXCEPTION',
                        message: err.message
                    };
                    addResult(`💥 ${table}: Exception - ${err.message}`, 'error');
                }
            }
            
            // Summary
            const successCount = Object.values(results).filter(r => r.status === 'SUCCESS').length;
            const totalCount = Object.keys(results).length;
            
            if (successCount === totalCount) {
                addResult(`🎉 All ${totalCount} tables are working correctly! The 406 errors should be resolved.`, 'success');
            } else {
                addResult(`⚠️ ${successCount}/${totalCount} tables are working. Some issues remain.`, 'error');
            }
            
            // Show detailed results
            addResult(`<strong>Detailed Results:</strong><pre>${JSON.stringify(results, null, 2)}</pre>`, 'info');
        }

        // Auto-run basic test on page load
        window.addEventListener('load', () => {
            setTimeout(testBasicConnection, 1000);
        });
    </script>
</body>
</html>
