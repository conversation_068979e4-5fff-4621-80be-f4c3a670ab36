# Fix Social Media 404 Errors

## Problem
Your application is showing 404 errors for these missing database tables:
- `content_templates` 
- `social_platforms`
- `social_posts` (exists but has wrong structure)

## Error Messages
```
GET https://rclikclltlyzyojjttqv.supabase.co/rest/v1/content_templates?select=*&order=usage_count.desc 404 (Not Found)
GET https://rclikclltlyzyojjttqv.supabase.co/rest/v1/social_platforms?select=*&order=name.asc 404 (Not Found)
GET https://rclikclltlyzyojjttqv.supabase.co/rest/v1/social_posts?select=*&order=created_at.desc&limit=50 404 (Not Found)
```

## Solution

### Step 1: Run the SQL Script in Supabase Dashboard

1. **Open your Supabase Dashboard**
   - Go to [https://supabase.com/dashboard](https://supabase.com/dashboard)
   - Navigate to your project: `rclikclltlyzyojjttqv`

2. **Open SQL Editor**
   - Click on "SQL Editor" in the left sidebar
   - Click "New Query"

3. **<PERSON><PERSON> and <PERSON>e the SQL Script**
   - Open the file `database/create_social_media_tables.sql`
   - Copy the entire content
   - Paste it into the SQL Editor

4. **Run the Script**
   - Click the "Run" button (or press Ctrl+Enter)
   - Wait for the script to complete

### Step 2: Verify Tables Were Created

After running the SQL script, you should see:
- ✅ `social_platforms` table with 4 default platforms (Twitter, LinkedIn, Facebook, Instagram)
- ✅ `content_templates` table with 5 default templates
- ✅ `social_posts` table with correct structure

### Step 3: Test the Fix

1. **Option A: Use Browser Console Test**
   - Open your application at `http://localhost:5173`
   - Press F12 to open Developer Tools
   - Go to Console tab
   - Copy and paste the content from `test-social-media-tables.js`
   - Press Enter to run the test

2. **Option B: Simply Refresh Your App**
   - Refresh your application
   - Navigate to the Social Media section
   - The 404 errors should be gone

## What the SQL Script Does

1. **Creates `social_platforms` table** with columns:
   - `id`, `name`, `connected`, `followers`, `color`, `icon`
   - `access_token`, `account_id`, `last_sync`
   - Inserts default platforms: Twitter, LinkedIn, Facebook, Instagram

2. **Creates `content_templates` table** with columns:
   - `id`, `name`, `content`, `category`, `hashtags`, `usage_count`
   - Inserts 5 default templates for different content types

3. **Recreates `social_posts` table** with correct structure:
   - `id`, `content`, `platform`, `status`, `scheduled_at`, `published_at`
   - `metrics` (JSON), `media_urls`, `hashtags`, `mentions`

4. **Adds indexes** for better performance
5. **Sets up proper constraints** and data validation

## Expected Results

After running the script, your Social Media component should:
- ✅ Load without 404 errors
- ✅ Show 4 social platforms in the dashboard
- ✅ Display content templates in the dropdown
- ✅ Allow creating and scheduling posts
- ✅ Show mock data if no real posts exist

## Troubleshooting

If you still see errors after running the script:

1. **Check the SQL Editor for errors**
   - Look for any red error messages
   - Some warnings are normal (like "relation already exists")

2. **Verify table permissions**
   - Go to Authentication > Policies
   - Ensure RLS policies allow access to the tables

3. **Check your environment variables**
   - Verify `VITE_SUPABASE_URL` and `VITE_SUPABASE_ANON_KEY` are correct

4. **Clear browser cache**
   - Hard refresh (Ctrl+Shift+R) or clear browser cache

## Files Created

- `database/create_social_media_tables.sql` - Main SQL script to create tables
- `test-social-media-tables.js` - Browser console test script
- `run-social-media-sql.js` - Node.js script (alternative approach)
- `FIX_SOCIAL_MEDIA_404_ERRORS.md` - This instruction file
