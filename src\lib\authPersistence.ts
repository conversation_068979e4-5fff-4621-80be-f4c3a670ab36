// Authentication state persistence utilities

import { supabase } from './supabase';
import { securityAudit } from './security';
import { sessionManager } from './sessionManager';

export interface AuthState {
  user: any | null;
  session: any | null;
  isAuthenticated: boolean;
  lastActivity: string;
  sessionId?: string;
}

export interface AuthPersistenceOptions {
  rememberMe: boolean;
  sessionTimeout: number; // in minutes
  autoRefresh: boolean;
}

class AuthPersistenceService {
  private static instance: AuthPersistenceService;
  private readonly STORAGE_KEY = 'auth_state';
  private readonly REMEMBER_KEY = 'remember_me';
  private readonly ACTIVITY_KEY = 'last_activity';
  private readonly SESSION_TIMEOUT_DEFAULT = 30; // 30 minutes
  private activityTimer: NodeJS.Timeout | null = null;
  private refreshTimer: NodeJS.Timeout | null = null;

  static getInstance(): AuthPersistenceService {
    if (!AuthPersistenceService.instance) {
      AuthPersistenceService.instance = new AuthPersistenceService();
    }
    return AuthPersistenceService.instance;
  }

  /**
   * Initialize authentication persistence
   */
  async initialize(): Promise<AuthState | null> {
    try {
      // Check for existing session
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Error getting session:', error);
        this.clearAuthState();
        return null;
      }

      if (session) {
        // Validate session activity
        const isValidSession = await this.validateSessionActivity();
        
        if (!isValidSession) {
          await this.signOut();
          return null;
        }

        // Update last activity
        this.updateLastActivity();
        
        // Start activity monitoring
        this.startActivityMonitoring();
        
        // Start session refresh if auto-refresh is enabled
        this.startSessionRefresh();

        const authState: AuthState = {
          user: session.user,
          session: session,
          isAuthenticated: true,
          lastActivity: new Date().toISOString(),
          sessionId: session.access_token
        };

        this.saveAuthState(authState);
        
        securityAudit.log('Session restored', { 
          userId: session.user?.id,
          sessionId: session.access_token 
        }, 'low');

        return authState;
      }

      return null;
    } catch (error) {
      console.error('Error initializing auth persistence:', error);
      this.clearAuthState();
      return null;
    }
  }

  /**
   * Save authentication state
   */
  saveAuthState(authState: AuthState, options?: AuthPersistenceOptions): void {
    try {
      const storageType = options?.rememberMe ? localStorage : sessionStorage;
      
      // Save auth state
      storageType.setItem(this.STORAGE_KEY, JSON.stringify({
        user: authState.user,
        isAuthenticated: authState.isAuthenticated,
        lastActivity: authState.lastActivity,
        sessionId: authState.sessionId
      }));

      // Save remember me preference
      if (options?.rememberMe !== undefined) {
        localStorage.setItem(this.REMEMBER_KEY, JSON.stringify(options.rememberMe));
      }

      // Update last activity
      this.updateLastActivity();

      // Start monitoring if authenticated
      if (authState.isAuthenticated) {
        this.startActivityMonitoring(options?.sessionTimeout);
        
        if (options?.autoRefresh) {
          this.startSessionRefresh();
        }
      }
    } catch (error) {
      console.error('Error saving auth state:', error);
    }
  }

  /**
   * Load authentication state
   */
  loadAuthState(): AuthState | null {
    try {
      // Try localStorage first (remember me), then sessionStorage
      let authData = localStorage.getItem(this.STORAGE_KEY);
      let storageType = 'localStorage';
      
      if (!authData) {
        authData = sessionStorage.getItem(this.STORAGE_KEY);
        storageType = 'sessionStorage';
      }

      if (!authData) {
        return null;
      }

      const parsedData = JSON.parse(authData);
      
      // Validate session activity
      if (!this.validateSessionActivity()) {
        this.clearAuthState();
        return null;
      }

      securityAudit.log('Auth state loaded', { 
        storageType,
        userId: parsedData.user?.id 
      }, 'low');

      return parsedData;
    } catch (error) {
      console.error('Error loading auth state:', error);
      this.clearAuthState();
      return null;
    }
  }

  /**
   * Clear authentication state
   */
  clearAuthState(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      sessionStorage.removeItem(this.STORAGE_KEY);
      localStorage.removeItem(this.ACTIVITY_KEY);
      
      this.stopActivityMonitoring();
      this.stopSessionRefresh();

      securityAudit.log('Auth state cleared', {}, 'low');
    } catch (error) {
      console.error('Error clearing auth state:', error);
    }
  }

  /**
   * Update last activity timestamp
   */
  updateLastActivity(): void {
    try {
      const timestamp = new Date().toISOString();
      localStorage.setItem(this.ACTIVITY_KEY, timestamp);
    } catch (error) {
      console.error('Error updating last activity:', error);
    }
  }

  /**
   * Validate session activity
   */
  validateSessionActivity(): boolean {
    try {
      const lastActivity = localStorage.getItem(this.ACTIVITY_KEY);
      
      if (!lastActivity) {
        return false;
      }

      const lastActivityTime = new Date(lastActivity);
      const now = new Date();
      const timeDiff = now.getTime() - lastActivityTime.getTime();
      const minutesDiff = timeDiff / (1000 * 60);

      // Check if session has expired (default 30 minutes)
      const sessionTimeout = this.getSessionTimeout();
      
      return minutesDiff < sessionTimeout;
    } catch (error) {
      console.error('Error validating session activity:', error);
      return false;
    }
  }

  /**
   * Get session timeout from settings or default
   */
  private getSessionTimeout(): number {
    try {
      // In a real app, this would come from user settings or system config
      return this.SESSION_TIMEOUT_DEFAULT;
    } catch (error) {
      return this.SESSION_TIMEOUT_DEFAULT;
    }
  }

  /**
   * Start activity monitoring
   */
  private startActivityMonitoring(customTimeout?: number): void {
    this.stopActivityMonitoring();

    const timeout = customTimeout || this.getSessionTimeout();
    
    // Check activity every minute
    this.activityTimer = setInterval(() => {
      if (!this.validateSessionActivity()) {
        this.handleSessionExpiry();
      }
    }, 60000); // Check every minute

    // Update activity on user interactions
    const updateActivity = () => this.updateLastActivity();
    
    document.addEventListener('click', updateActivity);
    document.addEventListener('keypress', updateActivity);
    document.addEventListener('scroll', updateActivity);
    document.addEventListener('mousemove', updateActivity);
  }

  /**
   * Stop activity monitoring
   */
  private stopActivityMonitoring(): void {
    if (this.activityTimer) {
      clearInterval(this.activityTimer);
      this.activityTimer = null;
    }

    // Remove event listeners
    const updateActivity = () => this.updateLastActivity();
    
    document.removeEventListener('click', updateActivity);
    document.removeEventListener('keypress', updateActivity);
    document.removeEventListener('scroll', updateActivity);
    document.removeEventListener('mousemove', updateActivity);
  }

  /**
   * Handle session expiry
   */
  private async handleSessionExpiry(): Promise<void> {
    try {
      securityAudit.log('Session expired due to inactivity', {}, 'medium');
      
      await this.signOut();
      
      // Notify user about session expiry
      if (window.location.pathname !== '/') {
        window.location.href = '/?session_expired=true';
      }
    } catch (error) {
      console.error('Error handling session expiry:', error);
    }
  }

  /**
   * Start automatic session refresh
   */
  private startSessionRefresh(): void {
    this.stopSessionRefresh();

    // Refresh session every 45 minutes (before 1-hour expiry)
    this.refreshTimer = setInterval(async () => {
      try {
        const { data, error } = await supabase.auth.refreshSession();
        
        if (error) {
          console.error('Error refreshing session:', error);
          this.handleSessionExpiry();
        } else if (data.session) {
          securityAudit.log('Session refreshed automatically', {
            userId: data.session.user?.id
          }, 'low');
        }
      } catch (error) {
        console.error('Error in session refresh:', error);
      }
    }, 45 * 60 * 1000); // 45 minutes
  }

  /**
   * Stop automatic session refresh
   */
  private stopSessionRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Sign out and clean up
   */
  async signOut(): Promise<void> {
    try {
      // Get current session info for logging
      const authState = this.loadAuthState();
      
      // Sign out from Supabase
      await supabase.auth.signOut();
      
      // Terminate session in our session manager
      if (authState?.sessionId) {
        await sessionManager.terminateSession(authState.sessionId);
      }
      
      // Clear local state
      this.clearAuthState();
      
      securityAudit.log('User signed out', { 
        userId: authState?.user?.id 
      }, 'low');
    } catch (error) {
      console.error('Error during sign out:', error);
      // Still clear local state even if remote sign out fails
      this.clearAuthState();
    }
  }

  /**
   * Check if remember me is enabled
   */
  isRememberMeEnabled(): boolean {
    try {
      const rememberMe = localStorage.getItem(this.REMEMBER_KEY);
      return rememberMe ? JSON.parse(rememberMe) : false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Get last activity timestamp
   */
  getLastActivity(): Date | null {
    try {
      const lastActivity = localStorage.getItem(this.ACTIVITY_KEY);
      return lastActivity ? new Date(lastActivity) : null;
    } catch (error) {
      return null;
    }
  }
}

// Export singleton instance
export const authPersistence = AuthPersistenceService.getInstance();
