import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Mail, Lock, User, Eye, EyeOff } from 'lucide-react';
import ForgotPasswordForm from '@/components/auth/ForgotPasswordForm';
import EmailVerificationForm from '@/components/auth/EmailVerificationForm';
import PasswordStrengthMeter from '@/components/auth/PasswordStrengthMeter';
import AccountLockoutAlert from '@/components/auth/AccountLockoutAlert';
import { accountSecurity } from '@/lib/accountSecurity';

interface AuthWrapperProps {
  children: React.ReactNode;
}

const AuthWrapper: React.FC<AuthWrapperProps> = ({ children }) => {
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [authLoading, setAuthLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [authMode, setAuthMode] = useState<'signin' | 'signup' | 'forgot-password' | 'verify-email' | 'locked'>('signin');
  const [pendingVerificationEmail, setPendingVerificationEmail] = useState<string | null>(null);
  const [lockedEmail, setLockedEmail] = useState<string | null>(null);
  
  const [signInForm, setSignInForm] = useState({
    email: '',
    password: ''
  });
  
  const [signUpForm, setSignUpForm] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: ''
  });

  useEffect(() => {
    // Check current session
    const checkSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        setUser(session?.user || null);
      } catch (error) {
        console.error('Error checking session:', error);
      } finally {
        setLoading(false);
      }
    };

    checkSession();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user || null);
        setLoading(false);
        
        if (event === 'SIGNED_IN') {
          setSuccess('Successfully signed in!');
          setError(null);
        } else if (event === 'SIGNED_OUT') {
          setSuccess('Successfully signed out!');
          setError(null);
        }
      }
    );

    return () => subscription.unsubscribe();
  }, []);

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Check if account is locked before attempting sign in
      const isLocked = await accountSecurity.isAccountLocked(signInForm.email);
      if (isLocked) {
        setLockedEmail(signInForm.email);
        setAuthMode('locked');
        setAuthLoading(false);
        return;
      }

      const { data, error } = await supabase.auth.signInWithPassword({
        email: signInForm.email,
        password: signInForm.password,
      });

      if (error) {
        // Check if this error should trigger account lockout
        if (error.message.includes('Invalid login credentials') ||
            error.message.includes('Email not confirmed')) {
          await accountSecurity.recordLoginAttempt(
            signInForm.email,
            false,
            'unknown', // In a real app, get actual IP
            navigator.userAgent,
            error.message
          );

          // Check if account is now locked after this attempt
          const nowLocked = await accountSecurity.isAccountLocked(signInForm.email);
          if (nowLocked) {
            setLockedEmail(signInForm.email);
            setAuthMode('locked');
            setAuthLoading(false);
            return;
          }
        }
        throw error;
      }

      // Create user profile if it doesn't exist
      if (data.user) {
        const { error: profileError } = await supabase
          .from('user_profiles')
          .upsert([{
            id: data.user.id,
            first_name: data.user.user_metadata?.first_name || 'User',
            last_name: data.user.user_metadata?.last_name || '',
            email: data.user.email,
          }], { onConflict: 'id' });

        if (profileError) {
          console.warn('Could not create/update profile:', profileError);
        }
      }

    } catch (error: any) {
      setError(error.message);
    } finally {
      setAuthLoading(false);
    }
  };

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthLoading(true);
    setError(null);
    setSuccess(null);

    if (signUpForm.password !== signUpForm.confirmPassword) {
      setError('Passwords do not match');
      setAuthLoading(false);
      return;
    }

    try {
      const { data, error } = await supabase.auth.signUp({
        email: signUpForm.email,
        password: signUpForm.password,
        options: {
          data: {
            first_name: signUpForm.firstName,
            last_name: signUpForm.lastName,
          }
        }
      });

      if (error) throw error;

      if (data.user) {
        setPendingVerificationEmail(signUpForm.email);
        setAuthMode('verify-email');
        setSuccess('Account created! Please check your email to verify your account.');

        // Create user profile
        const { error: profileError } = await supabase
          .from('user_profiles')
          .insert([{
            id: data.user.id,
            first_name: signUpForm.firstName,
            last_name: signUpForm.lastName,
            email: signUpForm.email,
          }]);

        if (profileError) {
          console.warn('Could not create profile:', profileError);
        }
      }

    } catch (error: any) {
      setError(error.message);
    } finally {
      setAuthLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      setError(error.message);
    }
  };

  const createTestUser = async () => {
    setAuthLoading(true);
    setError(null);
    
    try {
      // Try to sign in with test credentials first
      const { data, error } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'testpassword123',
      });

      if (error && error.message.includes('Invalid login credentials')) {
        // Create test user if doesn't exist
        const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
          email: '<EMAIL>',
          password: 'testpassword123',
          options: {
            data: {
              first_name: 'Test',
              last_name: 'User',
            }
          }
        });

        if (signUpError) throw signUpError;
        setSuccess('Test user created! You can now sign <NAME_EMAIL> / testpassword123');
      } else if (error) {
        throw error;
      } else {
        setSuccess('Signed in as test user!');
      }
    } catch (error: any) {
      setError(error.message);
    } finally {
      setAuthLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 p-4">
        {authMode === 'forgot-password' && (
          <ForgotPasswordForm onBack={() => setAuthMode('signin')} />
        )}

        {authMode === 'verify-email' && (
          <EmailVerificationForm
            email={pendingVerificationEmail || undefined}
            onVerificationComplete={() => {
              setAuthMode('signin');
              setPendingVerificationEmail(null);
              setSuccess('Email verified successfully! You can now sign in.');
            }}
            onResendSuccess={() => setSuccess('Verification email sent!')}
          />
        )}

        {authMode === 'locked' && lockedEmail && (
          <AccountLockoutAlert
            email={lockedEmail}
            onContactSupport={() => {
              // In a real app, this would open a support ticket or contact form
              alert('Please contact <NAME_EMAIL> for assistance.');
            }}
          />
        )}

        {(authMode === 'signin' || authMode === 'signup') && (
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              MarketCrawler Pro
            </CardTitle>
            <CardDescription>
              Sign in to access your marketing intelligence suite
            </CardDescription>
          </CardHeader>
          <CardContent>
            {error && (
              <Alert className="mb-4 border-red-200 bg-red-50">
                <AlertDescription className="text-red-700">{error}</AlertDescription>
              </Alert>
            )}
            
            {success && (
              <Alert className="mb-4 border-green-200 bg-green-50">
                <AlertDescription className="text-green-700">{success}</AlertDescription>
              </Alert>
            )}

            <Tabs defaultValue="signin" className="space-y-4">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="signin">Sign In</TabsTrigger>
                <TabsTrigger value="signup">Sign Up</TabsTrigger>
              </TabsList>

              <TabsContent value="signin">
                <form onSubmit={handleSignIn} className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="signin-email">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="signin-email"
                        type="email"
                        placeholder="Enter your email"
                        className="pl-10"
                        value={signInForm.email}
                        onChange={(e) => setSignInForm(prev => ({ ...prev, email: e.target.value }))}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signin-password">Password</Label>
                    <div className="relative">
                      <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="signin-password"
                        type={showPassword ? "text" : "password"}
                        placeholder="Enter your password"
                        className="pl-10 pr-10"
                        value={signInForm.password}
                        onChange={(e) => setSignInForm(prev => ({ ...prev, password: e.target.value }))}
                        required
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                    </div>
                  </div>

                  <Button type="submit" className="w-full" disabled={authLoading}>
                    {authLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Signing In...
                      </>
                    ) : (
                      'Sign In'
                    )}
                  </Button>

                  <div className="text-center">
                    <Button
                      variant="link"
                      onClick={() => setAuthMode('forgot-password')}
                      className="text-sm text-blue-600 hover:text-blue-800"
                      disabled={authLoading}
                    >
                      Forgot your password?
                    </Button>
                  </div>
                </form>
              </TabsContent>

              <TabsContent value="signup">
                <form onSubmit={handleSignUp} className="space-y-4">
                  <div className="grid grid-cols-2 gap-2">
                    <div className="space-y-2">
                      <Label htmlFor="signup-firstname">First Name</Label>
                      <Input
                        id="signup-firstname"
                        placeholder="First name"
                        value={signUpForm.firstName}
                        onChange={(e) => setSignUpForm(prev => ({ ...prev, firstName: e.target.value }))}
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="signup-lastname">Last Name</Label>
                      <Input
                        id="signup-lastname"
                        placeholder="Last name"
                        value={signUpForm.lastName}
                        onChange={(e) => setSignUpForm(prev => ({ ...prev, lastName: e.target.value }))}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-email">Email</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="signup-email"
                        type="email"
                        placeholder="Enter your email"
                        className="pl-10"
                        value={signUpForm.email}
                        onChange={(e) => setSignUpForm(prev => ({ ...prev, email: e.target.value }))}
                        required
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-password">Password</Label>
                    <Input
                      id="signup-password"
                      type="password"
                      placeholder="Create a password"
                      value={signUpForm.password}
                      onChange={(e) => setSignUpForm(prev => ({ ...prev, password: e.target.value }))}
                      required
                    />
                    <PasswordStrengthMeter password={signUpForm.password} />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="signup-confirm">Confirm Password</Label>
                    <Input
                      id="signup-confirm"
                      type="password"
                      placeholder="Confirm your password"
                      value={signUpForm.confirmPassword}
                      onChange={(e) => setSignUpForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                      required
                    />
                  </div>

                  <Button type="submit" className="w-full" disabled={authLoading}>
                    {authLoading ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating Account...
                      </>
                    ) : (
                      'Create Account'
                    )}
                  </Button>
                </form>
              </TabsContent>
            </Tabs>

            <div className="mt-6 pt-4 border-t">
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={createTestUser}
                disabled={authLoading}
              >
                {authLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Test User...
                  </>
                ) : (
                  'Create Test User (Demo)'
                )}
              </Button>
              <p className="text-xs text-gray-500 text-center mt-2">
                Creates a test account for demo purposes
              </p>
            </div>
          </CardContent>
        </Card>
        )}
      </div>
    );
  }

  // User is authenticated, show the main app
  return (
    <div>
      {/* Add a simple logout button in the header */}
      <div className="absolute top-4 right-4 z-50">
        <Button variant="outline" size="sm" onClick={handleSignOut}>
          Sign Out ({user.email})
        </Button>
      </div>
      {children}
    </div>
  );
};

export default AuthWrapper;
