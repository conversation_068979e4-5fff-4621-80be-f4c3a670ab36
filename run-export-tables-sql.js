// <PERSON><PERSON><PERSON> to run the export tables SQL script
// This will create the missing keyword_searches table that is causing 404 errors

import { readFileSync } from 'fs';
import { createClient } from '@supabase/supabase-js';

// Read config from the same source as the app
const config = {
  supabase: {
    url: process.env.VITE_SUPABASE_URL || 'https://rclikclltlyzyojjttqv.supabase.co',
    anonKey: process.env.VITE_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJjbGlrY2xsdGx5enlvamp0dHF2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5NjE1NzQsImV4cCI6MjA1MDUzNzU3NH0.Ktdh-7B_tZIbNJQqsrhFLun-CqL47mwLTTN9sUm4wKw'
  }
};

const supabase = createClient(config.supabase.url, config.supabase.anonKey);

async function runExportTablesSQL() {
  try {
    console.log('🔍 Reading export tables SQL file...');
    
    // Read the SQL file
    const sqlContent = readFileSync('./database/create_export_tables.sql', 'utf8');
    
    console.log('📝 SQL content loaded, executing...');
    console.log('SQL Preview:', sqlContent.substring(0, 200) + '...');
    
    // Execute the SQL
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });
    
    if (error) {
      console.error('❌ Error executing SQL:', error);
      
      // Try alternative method - split and execute individual statements
      console.log('🔄 Trying alternative method...');
      const statements = sqlContent
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));
      
      for (const statement of statements) {
        if (statement.trim()) {
          console.log(`Executing: ${statement.substring(0, 50)}...`);
          const { error: stmtError } = await supabase.rpc('exec_sql', { sql: statement + ';' });
          if (stmtError) {
            console.error(`❌ Error in statement: ${stmtError.message}`);
          } else {
            console.log('✅ Statement executed successfully');
          }
        }
      }
    } else {
      console.log('✅ Export tables SQL executed successfully!');
      console.log('Data:', data);
    }
    
    // Test the keyword_searches table
    console.log('🧪 Testing keyword_searches table...');
    const { data: testData, error: testError } = await supabase
      .from('keyword_searches')
      .select('*')
      .limit(1);
    
    if (testError) {
      console.error('❌ Error testing keyword_searches table:', testError);
    } else {
      console.log('✅ keyword_searches table is working!');
      console.log('Test query result:', testData);
    }
    
  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the script
runExportTablesSQL();
