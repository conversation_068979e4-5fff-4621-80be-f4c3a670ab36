// Application configuration management
interface AppConfig {
  supabase: {
    url: string;
    anonKey: string;
    functionsUrl: string;
  };
  app: {
    name: string;
    version: string;
  };
  dev: {
    mode: boolean;
    logLevel: string;
  };
}

const getEnvVar = (name: string, defaultValue?: string): string => {
  const value = import.meta.env[name] || defaultValue;
  if (!value) {
    throw new Error(`Environment variable ${name} is required but not set`);
  }
  return value;
};

const getBooleanEnvVar = (name: string, defaultValue: boolean = false): boolean => {
  const value = import.meta.env[name];
  if (value === undefined) return defaultValue;
  return value === 'true' || value === '1';
};

export const config: AppConfig = {
  supabase: {
    url: getEnvVar('VITE_SUPABASE_URL'),
    anonKey: getEnvVar('VITE_SUPABASE_ANON_KEY'),
    functionsUrl: getEnvVar('VITE_SUPABASE_FUNCTIONS_URL'),
  },
  app: {
    name: getEnvVar('VITE_APP_NAME', 'Scraping Analysis Marketing'),
    version: getEnvVar('VITE_APP_VERSION', '1.0.0'),
  },
  dev: {
    mode: getBooleanEnvVar('VITE_DEV_MODE', import.meta.env.DEV),
    logLevel: getEnvVar('VITE_LOG_LEVEL', 'info'),
  },
};

// Validate configuration on load
if (!config.supabase.url.startsWith('https://')) {
  throw new Error('Supabase URL must be a valid HTTPS URL');
}

if (!config.supabase.functionsUrl.startsWith('https://')) {
  throw new Error('Supabase Functions URL must be a valid HTTPS URL');
}

export default config;
