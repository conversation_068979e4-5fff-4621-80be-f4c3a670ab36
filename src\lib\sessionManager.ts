// Session management and security utilities

import { supabase } from './supabase'
import { securityAudit } from './security'

export interface UserSession {
  id: string
  user_id: string
  session_token: string
  ip_address: string
  user_agent: string
  created_at: string
  last_activity: string
  expires_at: string
  is_active: boolean
  device_info?: {
    browser: string
    os: string
    device: string
  }
}

export interface SessionActivity {
  id: string
  session_id: string
  activity_type: 'login' | 'logout' | 'page_view' | 'api_call' | 'password_change' | 'settings_change'
  details: Record<string, any>
  timestamp: string
  ip_address: string
}

class SessionManager {
  private static instance: SessionManager
  private readonly SESSION_TIMEOUT_HOURS = 24
  private readonly ACTIVITY_TIMEOUT_MINUTES = 30
  private activityTimer: NodeJS.Timeout | null = null

  static getInstance(): SessionManager {
    if (!SessionManager.instance) {
      SessionManager.instance = new SessionManager()
    }
    return SessionManager.instance
  }

  /**
   * Create new session for user
   */
  async createSession(userId: string, ipAddress: string, userAgent: string): Promise<{ session: UserSession | null; error: string | null }> {
    try {
      const sessionToken = this.generateSessionToken()
      const expiresAt = new Date()
      expiresAt.setHours(expiresAt.getHours() + this.SESSION_TIMEOUT_HOURS)

      const deviceInfo = this.parseUserAgent(userAgent)

      const sessionData: Omit<UserSession, 'id'> = {
        user_id: userId,
        session_token: sessionToken,
        ip_address: ipAddress,
        user_agent: userAgent,
        created_at: new Date().toISOString(),
        last_activity: new Date().toISOString(),
        expires_at: expiresAt.toISOString(),
        is_active: true,
        device_info: deviceInfo
      }

      const { data, error } = await supabase
        .from('user_sessions')
        .insert(sessionData)
        .select()
        .single()

      if (error) {
        return { session: null, error: error.message }
      }

      // Log session creation
      await this.logActivity(data.id, 'login', { 
        device_info: deviceInfo,
        ip_address: ipAddress 
      }, ipAddress)

      securityAudit.log('Session created', { 
        userId, 
        sessionId: data.id,
        ipAddress,
        deviceInfo 
      }, 'low')

      // Start activity tracking
      this.startActivityTracking(data.id)

      return { session: data, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { session: null, error: errorMessage }
    }
  }

  /**
   * Validate and refresh session
   */
  async validateSession(sessionToken: string): Promise<{ session: UserSession | null; error: string | null }> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('session_token', sessionToken)
        .eq('is_active', true)
        .gte('expires_at', new Date().toISOString())
        .single()

      if (error || !data) {
        return { session: null, error: 'Invalid or expired session' }
      }

      // Check for activity timeout
      const lastActivity = new Date(data.last_activity)
      const now = new Date()
      const minutesSinceActivity = (now.getTime() - lastActivity.getTime()) / (1000 * 60)

      if (minutesSinceActivity > this.ACTIVITY_TIMEOUT_MINUTES) {
        await this.terminateSession(sessionToken)
        return { session: null, error: 'Session expired due to inactivity' }
      }

      // Update last activity
      await this.updateSessionActivity(sessionToken)

      return { session: data, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { session: null, error: errorMessage }
    }
  }

  /**
   * Update session activity timestamp
   */
  async updateSessionActivity(sessionToken: string): Promise<void> {
    try {
      await supabase
        .from('user_sessions')
        .update({ last_activity: new Date().toISOString() })
        .eq('session_token', sessionToken)
        .eq('is_active', true)
    } catch (error) {
      console.error('Error updating session activity:', error)
    }
  }

  /**
   * Terminate specific session
   */
  async terminateSession(sessionToken: string): Promise<{ error: string | null }> {
    try {
      const { data: session } = await supabase
        .from('user_sessions')
        .select('id, user_id')
        .eq('session_token', sessionToken)
        .single()

      const { error } = await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('session_token', sessionToken)

      if (error) {
        return { error: error.message }
      }

      if (session) {
        await this.logActivity(session.id, 'logout', {}, 'unknown')
        securityAudit.log('Session terminated', { 
          sessionId: session.id,
          userId: session.user_id 
        }, 'low')
      }

      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { error: errorMessage }
    }
  }

  /**
   * Terminate all sessions for a user
   */
  async terminateAllUserSessions(userId: string, exceptSessionToken?: string): Promise<{ error: string | null }> {
    try {
      let query = supabase
        .from('user_sessions')
        .update({ is_active: false })
        .eq('user_id', userId)
        .eq('is_active', true)

      if (exceptSessionToken) {
        query = query.neq('session_token', exceptSessionToken)
      }

      const { error } = await query

      if (error) {
        return { error: error.message }
      }

      securityAudit.log('All user sessions terminated', { 
        userId,
        exceptSession: exceptSessionToken ? 'current' : 'none'
      }, 'medium')

      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { error: errorMessage }
    }
  }

  /**
   * Get active sessions for user
   */
  async getUserSessions(userId: string): Promise<UserSession[]> {
    try {
      const { data, error } = await supabase
        .from('user_sessions')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .gte('expires_at', new Date().toISOString())
        .order('last_activity', { ascending: false })

      if (error) {
        console.error('Error fetching user sessions:', error)
        return []
      }

      return data || []
    } catch (error) {
      console.error('Error fetching user sessions:', error)
      return []
    }
  }

  /**
   * Log session activity
   */
  async logActivity(
    sessionId: string, 
    activityType: SessionActivity['activity_type'], 
    details: Record<string, any>,
    ipAddress: string
  ): Promise<void> {
    try {
      const activity: Omit<SessionActivity, 'id'> = {
        session_id: sessionId,
        activity_type: activityType,
        details,
        timestamp: new Date().toISOString(),
        ip_address: ipAddress
      }

      await supabase
        .from('session_activities')
        .insert(activity)
    } catch (error) {
      console.error('Error logging session activity:', error)
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = new Date().toISOString()

      // Mark expired sessions as inactive
      await supabase
        .from('user_sessions')
        .update({ is_active: false })
        .lt('expires_at', now)
        .eq('is_active', true)

      // Delete old session data (older than 90 days)
      const oldDataCutoff = new Date()
      oldDataCutoff.setDate(oldDataCutoff.getDate() - 90)

      await supabase
        .from('user_sessions')
        .delete()
        .lt('created_at', oldDataCutoff.toISOString())

      await supabase
        .from('session_activities')
        .delete()
        .lt('timestamp', oldDataCutoff.toISOString())

      securityAudit.log('Session cleanup completed', {}, 'low')
    } catch (error) {
      console.error('Error during session cleanup:', error)
    }
  }

  /**
   * Generate secure session token
   */
  private generateSessionToken(): string {
    const array = new Uint8Array(32)
    crypto.getRandomValues(array)
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Parse user agent for device information
   */
  private parseUserAgent(userAgent: string): { browser: string; os: string; device: string } {
    // Simplified user agent parsing
    let browser = 'Unknown'
    let os = 'Unknown'
    let device = 'Desktop'

    if (userAgent.includes('Chrome')) browser = 'Chrome'
    else if (userAgent.includes('Firefox')) browser = 'Firefox'
    else if (userAgent.includes('Safari')) browser = 'Safari'
    else if (userAgent.includes('Edge')) browser = 'Edge'

    if (userAgent.includes('Windows')) os = 'Windows'
    else if (userAgent.includes('Mac')) os = 'macOS'
    else if (userAgent.includes('Linux')) os = 'Linux'
    else if (userAgent.includes('Android')) os = 'Android'
    else if (userAgent.includes('iOS')) os = 'iOS'

    if (userAgent.includes('Mobile') || userAgent.includes('Android') || userAgent.includes('iPhone')) {
      device = 'Mobile'
    } else if (userAgent.includes('Tablet') || userAgent.includes('iPad')) {
      device = 'Tablet'
    }

    return { browser, os, device }
  }

  /**
   * Start activity tracking for session
   */
  private startActivityTracking(sessionId: string): void {
    if (this.activityTimer) {
      clearInterval(this.activityTimer)
    }

    this.activityTimer = setInterval(() => {
      // In a real app, this would track user activity
      // For now, we just update the session activity timestamp
    }, 60000) // Check every minute
  }
}

export const sessionManager = SessionManager.getInstance()
