import { useState, useEffect, useCallback } from 'react';

export const useLocalStorage = <T>(
  key: string,
  initialValue: T
): [T, (value: T | ((val: T) => T)) => void, () => void] => {
  // Get from local storage then parse stored json or return initialValue
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = useCallback((value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to local storage
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  }, [key, storedValue]);

  // Remove from localStorage
  const removeValue = useCallback(() => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.error(`Error removing localStorage key "${key}":`, error);
    }
  }, [key, initialValue]);

  // Listen for changes to this key in other tabs/windows
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === key && e.newValue !== null) {
        try {
          setStoredValue(JSON.parse(e.newValue));
        } catch (error) {
          console.error(`Error parsing localStorage value for key "${key}":`, error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, [key]);

  return [storedValue, setValue, removeValue];
};

// Specialized hooks for common use cases
export const useUserPreferences = () => {
  return useLocalStorage('userPreferences', {
    theme: 'system' as 'light' | 'dark' | 'system',
    sidebarCollapsed: false,
    defaultView: 'grid' as 'grid' | 'list',
    itemsPerPage: 20,
    autoSave: true,
    notifications: true
  });
};

export const useRecentSearches = () => {
  const [searches, setSearches] = useLocalStorage<string[]>('recentSearches', []);

  const addSearch = useCallback((search: string) => {
    setSearches(prev => {
      const filtered = prev.filter(s => s !== search);
      return [search, ...filtered].slice(0, 10); // Keep only last 10 searches
    });
  }, [setSearches]);

  const clearSearches = useCallback(() => {
    setSearches([]);
  }, [setSearches]);

  return { searches, addSearch, clearSearches };
};

export const useRecentUrls = () => {
  const [urls, setUrls] = useLocalStorage<string[]>('recentUrls', []);

  const addUrl = useCallback((url: string) => {
    setUrls(prev => {
      const filtered = prev.filter(u => u !== url);
      return [url, ...filtered].slice(0, 20); // Keep only last 20 URLs
    });
  }, [setUrls]);

  const clearUrls = useCallback(() => {
    setUrls([]);
  }, [setUrls]);

  return { urls, addUrl, clearUrls };
};

export const useDraftData = <T>(key: string) => {
  const [draft, setDraft, removeDraft] = useLocalStorage<T | null>(`draft_${key}`, null);

  const saveDraft = useCallback((data: T) => {
    setDraft(data);
  }, [setDraft]);

  const clearDraft = useCallback(() => {
    removeDraft();
  }, [removeDraft]);

  const hasDraft = draft !== null;

  return { draft, saveDraft, clearDraft, hasDraft };
};
