import { apiService, SupabaseService, ApiResponse } from './apiService';

export interface ScrapingConfig {
  url: string;
  selectors: {
    [key: string]: string;
  };
  options: {
    waitFor?: number;
    scrollToBottom?: boolean;
    followPagination?: boolean;
    maxPages?: number;
    delay?: number;
    userAgent?: string;
    headers?: Record<string, string>;
    proxy?: string;
    javascript?: boolean;
  };
  filters?: {
    minLength?: number;
    maxLength?: number;
    excludeEmpty?: boolean;
    uniqueOnly?: boolean;
  };
}

export interface ScrapingJob {
  id: string;
  config: ScrapingConfig;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  results: any[];
  error?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  total_items?: number;
  processed_items?: number;
}

export interface ScrapingResult {
  id: string;
  job_id: string;
  data: Record<string, any>;
  url: string;
  scraped_at: string;
  metadata?: {
    response_time: number;
    status_code: number;
    content_length: number;
  };
}

class ScrapingService {
  async createJob(config: ScrapingConfig): Promise<ApiResponse<ScrapingJob>> {
    try {
      // Validate config
      if (!config.url || !config.selectors || Object.keys(config.selectors).length === 0) {
        return {
          success: false,
          error: 'Invalid scraping configuration'
        };
      }

      // Create job in database
      const jobData = {
        config,
        status: 'pending' as const,
        progress: 0,
        results: [],
        created_at: new Date().toISOString()
      };

      const result = await SupabaseService.insert<ScrapingJob>('scraping_jobs', jobData);
      
      if (!result.success || !result.data) {
        return result;
      }

      // Start scraping process
      this.startScraping(result.data.id);

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to create scraping job'
      };
    }
  }

  async getJob(jobId: string): Promise<ApiResponse<ScrapingJob>> {
    return SupabaseService.query<ScrapingJob>('scraping_jobs', {
      filters: { id: jobId }
    }).then(result => {
      if (result.success && result.data && result.data.length > 0) {
        return { success: true, data: result.data[0] };
      }
      return { success: false, error: 'Job not found' };
    });
  }

  async getJobs(filters?: { status?: string; limit?: number }): Promise<ApiResponse<ScrapingJob[]>> {
    return SupabaseService.query<ScrapingJob>('scraping_jobs', {
      filters: filters?.status ? { status: filters.status } : undefined,
      orderBy: 'created_at',
      orderDirection: 'desc',
      limit: filters?.limit || 50
    });
  }

  async getResults(jobId: string, pagination?: { limit?: number; offset?: number }): Promise<ApiResponse<ScrapingResult[]>> {
    return SupabaseService.query<ScrapingResult>('scraping_results', {
      filters: { job_id: jobId },
      orderBy: 'scraped_at',
      orderDirection: 'desc',
      limit: pagination?.limit || 100,
      offset: pagination?.offset || 0
    });
  }

  async cancelJob(jobId: string): Promise<ApiResponse<void>> {
    try {
      const result = await SupabaseService.update('scraping_jobs', jobId, {
        status: 'cancelled',
        completed_at: new Date().toISOString()
      });

      // In a real implementation, you would also cancel the actual scraping process
      // This might involve sending a signal to a worker process or job queue

      return result;
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to cancel job'
      };
    }
  }

  async deleteJob(jobId: string): Promise<ApiResponse<void>> {
    try {
      // Delete results first
      await SupabaseService.query('scraping_results', {
        filters: { job_id: jobId }
      }).then(async (result) => {
        if (result.success && result.data) {
          for (const item of result.data) {
            await SupabaseService.delete('scraping_results', item.id);
          }
        }
      });

      // Delete job
      return SupabaseService.delete('scraping_jobs', jobId);
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete job'
      };
    }
  }

  private async startScraping(jobId: string): Promise<void> {
    try {
      // Update job status
      await SupabaseService.update('scraping_jobs', jobId, {
        status: 'running',
        started_at: new Date().toISOString()
      });

      // In a real implementation, this would:
      // 1. Send the job to a queue (Redis, AWS SQS, etc.)
      // 2. A worker process would pick up the job
      // 3. The worker would perform the actual scraping
      // 4. Results would be saved to the database
      // 5. Job status would be updated

      // For demo purposes, we'll simulate the process
      setTimeout(async () => {
        await this.simulateScraping(jobId);
      }, 1000);

    } catch (error) {
      console.error('Failed to start scraping:', error);
      await SupabaseService.update('scraping_jobs', jobId, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        completed_at: new Date().toISOString()
      });
    }
  }

  private async simulateScraping(jobId: string): Promise<void> {
    try {
      const jobResult = await this.getJob(jobId);
      if (!jobResult.success || !jobResult.data) return;

      const job = jobResult.data;
      const totalItems = Math.floor(Math.random() * 50) + 10; // 10-60 items

      // Simulate scraping progress
      for (let i = 0; i < totalItems; i++) {
        const progress = Math.round((i / totalItems) * 100);
        
        // Update progress
        await SupabaseService.update('scraping_jobs', jobId, {
          progress,
          processed_items: i + 1,
          total_items: totalItems
        });

        // Create mock result
        const mockData: Record<string, any> = {};
        Object.keys(job.config.selectors).forEach(key => {
          mockData[key] = `Sample ${key} data ${i + 1}`;
        });

        const resultData = {
          job_id: jobId,
          data: mockData,
          url: job.config.url,
          scraped_at: new Date().toISOString(),
          metadata: {
            response_time: Math.floor(Math.random() * 1000) + 200,
            status_code: 200,
            content_length: Math.floor(Math.random() * 10000) + 1000
          }
        };

        await SupabaseService.insert('scraping_results', resultData);

        // Simulate delay
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Complete the job
      await SupabaseService.update('scraping_jobs', jobId, {
        status: 'completed',
        progress: 100,
        completed_at: new Date().toISOString(),
        total_items: totalItems,
        processed_items: totalItems
      });

    } catch (error) {
      console.error('Scraping simulation failed:', error);
      await SupabaseService.update('scraping_jobs', jobId, {
        status: 'failed',
        error: error instanceof Error ? error.message : 'Scraping failed',
        completed_at: new Date().toISOString()
      });
    }
  }

  async validateUrl(url: string): Promise<ApiResponse<{ valid: boolean; accessible: boolean }>> {
    try {
      // Basic URL validation
      const urlPattern = /^https?:\/\/.+/;
      if (!urlPattern.test(url)) {
        return {
          success: true,
          data: { valid: false, accessible: false }
        };
      }

      // In a real implementation, you might check if the URL is accessible
      // For now, we'll just return valid for properly formatted URLs
      return {
        success: true,
        data: { valid: true, accessible: true }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'URL validation failed'
      };
    }
  }

  async getJobStats(): Promise<ApiResponse<{
    total: number;
    pending: number;
    running: number;
    completed: number;
    failed: number;
  }>> {
    try {
      const allJobs = await SupabaseService.query<ScrapingJob>('scraping_jobs', {});
      
      if (!allJobs.success || !allJobs.data) {
        return { success: false, error: 'Failed to fetch job stats' };
      }

      const stats = allJobs.data.reduce((acc, job) => {
        acc.total++;
        acc[job.status]++;
        return acc;
      }, {
        total: 0,
        pending: 0,
        running: 0,
        completed: 0,
        failed: 0,
        cancelled: 0
      });

      return { success: true, data: stats };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to get job stats'
      };
    }
  }
}

export const scrapingService = new ScrapingService();
export default scrapingService;
