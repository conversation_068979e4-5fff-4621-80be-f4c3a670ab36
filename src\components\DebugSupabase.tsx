import React, { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';

const DebugSupabase: React.FC = () => {
  const [authStatus, setAuthStatus] = useState<string>('Checking...');
  const [userInfo, setUserInfo] = useState<any>(null);
  const [settingsResult, setSettingsResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        // Check authentication
        const { data: { user }, error: authError } = await supabase.auth.getUser();
        
        if (authError) {
          setAuthStatus(`Auth Error: ${authError.message}`);
          setError(authError.message);
          return;
        }

        if (!user) {
          setAuthStatus('Not authenticated');
          return;
        }

        setAuthStatus('Authenticated');
        setUserInfo(user);

        // Try to fetch settings
        console.log('Attempting to fetch user_settings...');
        const { data, error: settingsError } = await supabase
          .from('user_settings')
          .select('*')
          .eq('user_id', user.id);

        if (settingsError) {
          console.error('Settings error:', settingsError);
          setSettingsResult({ error: settingsError });
        } else {
          console.log('Settings data:', data);
          setSettingsResult({ data });
        }

      } catch (err) {
        console.error('Debug error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
      }
    };

    checkAuth();
  }, []);

  const testConnection = async () => {
    try {
      console.log('Testing basic connection...');
      const { data, error } = await supabase
        .from('user_settings')
        .select('count', { count: 'exact', head: true });

      if (error) {
        console.error('Connection test failed:', error);
        setError(`Connection test failed: ${error.message}`);
      } else {
        console.log('Connection test successful:', data);
        setError(null);
      }
    } catch (err) {
      console.error('Connection test error:', err);
      setError(err instanceof Error ? err.message : 'Unknown connection error');
    }
  };

  const createTestSettings = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        setError('User not authenticated');
        return;
      }

      const { data, error } = await supabase
        .from('user_settings')
        .upsert([{
          user_id: user.id,
          theme: 'system',
          language: 'en',
          timezone: 'America/New_York',
          notifications: {
            email: true,
            push: false,
            marketing: true
          },
          privacy: {
            analytics: true,
            data_collection: true
          }
        }])
        .select();

      if (error) {
        console.error('Create settings error:', error);
        setError(`Create settings failed: ${error.message}`);
      } else {
        console.log('Settings created:', data);
        setSettingsResult({ data });
        setError(null);
      }
    } catch (err) {
      console.error('Create settings error:', err);
      setError(err instanceof Error ? err.message : 'Unknown create error');
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Supabase Debug Info</h3>
      
      <div className="space-y-2">
        <div>
          <strong>Auth Status:</strong> {authStatus}
        </div>
        
        {userInfo && (
          <div>
            <strong>User ID:</strong> {userInfo.id}
            <br />
            <strong>Email:</strong> {userInfo.email}
          </div>
        )}
        
        {settingsResult && (
          <div>
            <strong>Settings Result:</strong>
            <pre className="text-xs bg-white p-2 rounded mt-1">
              {JSON.stringify(settingsResult, null, 2)}
            </pre>
          </div>
        )}
        
        {error && (
          <div className="text-red-600">
            <strong>Error:</strong> {error}
          </div>
        )}
      </div>

      <div className="mt-4 space-x-2">
        <button 
          onClick={testConnection}
          className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
        >
          Test Connection
        </button>
        <button 
          onClick={createTestSettings}
          className="px-3 py-1 bg-green-500 text-white rounded text-sm"
        >
          Create Test Settings
        </button>
      </div>
    </div>
  );
};

export default DebugSupabase;
