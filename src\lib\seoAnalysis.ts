// SEO Analysis utilities

export interface SEOAnalysisResult {
  score: number;
  issues: string[];
  recommendations: string[];
  details: {
    titleAnalysis: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    contentAnalysis: {
      score: number;
      wordCount: number;
      readabilityScore: number;
      issues: string[];
      recommendations: string[];
    };
    technicalSEO: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    keywordAnalysis: {
      score: number;
      topKeywords: Array<{ keyword: string; frequency: number; density: number }>;
      issues: string[];
      recommendations: string[];
    };
  };
}

export interface ScrapedDataForSEO {
  title?: string;
  content?: string;
  url: string;
  metadata?: {
    headings?: string[];
    links?: string[];
    wordCount?: number;
  };
}

export class SEOAnalyzer {
  private stopWords = new Set([
    'the', 'and', 'for', 'are', 'but', 'not', 'you', 'all', 'can', 'had', 'her', 'was', 'one', 'our', 'out', 'day', 'get', 'has', 'him', 'his', 'how', 'its', 'may', 'new', 'now', 'old', 'see', 'two', 'way', 'who', 'boy', 'did', 'man', 'men', 'put', 'say', 'she', 'too', 'use', 'with', 'have', 'this', 'will', 'your', 'from', 'they', 'know', 'want', 'been', 'good', 'much', 'some', 'time', 'very', 'when', 'come', 'here', 'just', 'like', 'long', 'make', 'many', 'over', 'such', 'take', 'than', 'them', 'well', 'were'
  ]);

  analyzeData(data: ScrapedDataForSEO[]): SEOAnalysisResult {
    if (!data || data.length === 0) {
      throw new Error('No data provided for SEO analysis');
    }

    const titleAnalysis = this.analyzeTitles(data);
    const contentAnalysis = this.analyzeContent(data);
    const technicalSEO = this.analyzeTechnicalSEO(data);
    const keywordAnalysis = this.analyzeKeywords(data);

    const overallScore = Math.round(
      (titleAnalysis.score + contentAnalysis.score + technicalSEO.score + keywordAnalysis.score) / 4
    );

    const allIssues = [
      ...titleAnalysis.issues,
      ...contentAnalysis.issues,
      ...technicalSEO.issues,
      ...keywordAnalysis.issues
    ];

    const allRecommendations = [
      ...titleAnalysis.recommendations,
      ...contentAnalysis.recommendations,
      ...technicalSEO.recommendations,
      ...keywordAnalysis.recommendations
    ];

    return {
      score: overallScore,
      issues: allIssues,
      recommendations: allRecommendations,
      details: {
        titleAnalysis,
        contentAnalysis,
        technicalSEO,
        keywordAnalysis
      }
    };
  }

  private analyzeTitles(data: ScrapedDataForSEO[]) {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    const titlesWithoutTitle = data.filter(item => !item.title || item.title.trim().length === 0);
    const shortTitles = data.filter(item => item.title && item.title.length < 30);
    const longTitles = data.filter(item => item.title && item.title.length > 60);

    if (titlesWithoutTitle.length > 0) {
      score -= 30;
      issues.push(`${titlesWithoutTitle.length} pages missing titles`);
      recommendations.push('Add descriptive titles to all pages');
    }

    if (shortTitles.length > 0) {
      score -= 15;
      issues.push(`${shortTitles.length} titles are too short (< 30 characters)`);
      recommendations.push('Expand titles to 30-60 characters for better SEO');
    }

    if (longTitles.length > 0) {
      score -= 10;
      issues.push(`${longTitles.length} titles are too long (> 60 characters)`);
      recommendations.push('Shorten titles to under 60 characters to prevent truncation');
    }

    return {
      score: Math.max(0, score),
      issues,
      recommendations
    };
  }

  private analyzeContent(data: ScrapedDataForSEO[]) {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    const totalWordCount = data.reduce((sum, item) => sum + (item.metadata?.wordCount || 0), 0);
    const avgWordCount = totalWordCount / data.length;

    const shortContent = data.filter(item => (item.metadata?.wordCount || 0) < 300);
    const noContent = data.filter(item => !item.content || item.content.trim().length === 0);

    if (noContent.length > 0) {
      score -= 40;
      issues.push(`${noContent.length} pages have no content`);
      recommendations.push('Add meaningful content to all pages');
    }

    if (shortContent.length > 0) {
      score -= 20;
      issues.push(`${shortContent.length} pages have thin content (< 300 words)`);
      recommendations.push('Expand content to at least 300 words per page');
    }

    if (avgWordCount < 500) {
      score -= 15;
      issues.push(`Average word count is low (${Math.round(avgWordCount)} words)`);
      recommendations.push('Aim for 500+ words per page for better SEO performance');
    }

    return {
      score: Math.max(0, score),
      wordCount: totalWordCount,
      readabilityScore: this.calculateReadabilityScore(data),
      issues,
      recommendations
    };
  }

  private analyzeTechnicalSEO(data: ScrapedDataForSEO[]) {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    const urlIssues = data.filter(item => {
      const url = item.url.toLowerCase();
      return url.includes('_') || url.includes('%20') || url.length > 100;
    });

    if (urlIssues.length > 0) {
      score -= 15;
      issues.push(`${urlIssues.length} URLs have technical issues (underscores, spaces, too long)`);
      recommendations.push('Use hyphens instead of underscores, avoid spaces, keep URLs under 100 characters');
    }

    const duplicateTitles = this.findDuplicateTitles(data);
    if (duplicateTitles.length > 0) {
      score -= 25;
      issues.push(`${duplicateTitles.length} duplicate titles found`);
      recommendations.push('Ensure all page titles are unique');
    }

    return {
      score: Math.max(0, score),
      issues,
      recommendations
    };
  }

  private analyzeKeywords(data: ScrapedDataForSEO[]) {
    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    const keywordMap = new Map<string, number>();
    let totalWords = 0;

    data.forEach(item => {
      const text = `${item.title || ''} ${item.content || ''}`.toLowerCase();
      const words = text.match(/\b[a-z]{3,}\b/g) || [];
      
      words.forEach(word => {
        if (!this.stopWords.has(word)) {
          keywordMap.set(word, (keywordMap.get(word) || 0) + 1);
          totalWords++;
        }
      });
    });

    const topKeywords = Array.from(keywordMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([keyword, frequency]) => ({
        keyword,
        frequency,
        density: (frequency / totalWords) * 100
      }));

    if (topKeywords.length === 0) {
      score -= 50;
      issues.push('No keywords found in content');
      recommendations.push('Add relevant keywords to your content');
    } else if (topKeywords[0].density > 5) {
      score -= 20;
      issues.push(`Keyword "${topKeywords[0].keyword}" may be over-optimized (${topKeywords[0].density.toFixed(1)}% density)`);
      recommendations.push('Reduce keyword density to 2-3% for natural content');
    }

    return {
      score: Math.max(0, score),
      topKeywords,
      issues,
      recommendations
    };
  }

  private calculateReadabilityScore(data: ScrapedDataForSEO[]): number {
    // Simple readability calculation based on average sentence and word length
    let totalSentences = 0;
    let totalWords = 0;
    let totalSyllables = 0;

    data.forEach(item => {
      if (item.content) {
        const sentences = item.content.split(/[.!?]+/).filter(s => s.trim().length > 0);
        const words = item.content.match(/\b\w+\b/g) || [];
        
        totalSentences += sentences.length;
        totalWords += words.length;
        totalSyllables += words.reduce((sum, word) => sum + this.countSyllables(word), 0);
      }
    });

    if (totalSentences === 0 || totalWords === 0) return 0;

    // Simplified Flesch Reading Ease formula
    const avgSentenceLength = totalWords / totalSentences;
    const avgSyllablesPerWord = totalSyllables / totalWords;
    
    return Math.max(0, Math.min(100, 
      206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
    ));
  }

  private countSyllables(word: string): number {
    word = word.toLowerCase();
    if (word.length <= 3) return 1;
    
    const vowels = word.match(/[aeiouy]+/g);
    let syllables = vowels ? vowels.length : 1;
    
    if (word.endsWith('e')) syllables--;
    if (word.endsWith('le') && word.length > 2) syllables++;
    
    return Math.max(1, syllables);
  }

  private findDuplicateTitles(data: ScrapedDataForSEO[]): string[] {
    const titleCounts = new Map<string, number>();
    
    data.forEach(item => {
      if (item.title) {
        titleCounts.set(item.title, (titleCounts.get(item.title) || 0) + 1);
      }
    });

    return Array.from(titleCounts.entries())
      .filter(([_, count]) => count > 1)
      .map(([title, _]) => title);
  }
}
