// Two-Factor Authentication utilities

import { supabase } from './supabase'
import { securityAudit } from './security'

export interface TwoFactorSetup {
  secret: string
  qrCodeUrl: string
  backupCodes: string[]
}

export interface TwoFactorVerification {
  token: string
  backupCode?: string
}

export interface UserTwoFactor {
  id: string
  user_id: string
  secret_encrypted: string
  enabled: boolean
  backup_codes_hash: string[]
  last_used_at?: string
  created_at: string
}

class TwoFactorAuthService {
  private static instance: TwoFactorAuthService

  static getInstance(): TwoFactorAuthService {
    if (!TwoFactorAuthService.instance) {
      TwoFactorAuthService.instance = new TwoFactorAuthService()
    }
    return TwoFactorAuthService.instance
  }

  /**
   * Generate TOTP secret and QR code for setup
   */
  async generateTwoFactorSetup(userId: string, email: string): Promise<{ setup: TwoFactorSetup | null; error: string | null }> {
    try {
      // Generate a random secret (32 characters, base32)
      const secret = this.generateSecret()
      
      // Generate QR code URL for authenticator apps
      const appName = 'MarketCrawler Pro'
      const qrCodeUrl = `otpauth://totp/${encodeURIComponent(appName)}:${encodeURIComponent(email)}?secret=${secret}&issuer=${encodeURIComponent(appName)}`
      
      // Generate backup codes
      const backupCodes = this.generateBackupCodes()
      
      // Store temporary setup data (not enabled yet)
      const { error } = await supabase
        .from('user_two_factor_temp')
        .upsert({
          user_id: userId,
          secret_encrypted: await this.encryptSecret(secret),
          backup_codes_hash: await Promise.all(backupCodes.map(code => this.hashBackupCode(code))),
          created_at: new Date().toISOString()
        })

      if (error) {
        return { setup: null, error: error.message }
      }

      securityAudit.log('2FA setup initiated', { userId }, 'low')

      return {
        setup: {
          secret,
          qrCodeUrl,
          backupCodes
        },
        error: null
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { setup: null, error: errorMessage }
    }
  }

  /**
   * Verify TOTP token and enable 2FA
   */
  async enableTwoFactor(userId: string, token: string): Promise<{ success: boolean; error: string | null }> {
    try {
      // Get temporary setup data
      const { data: tempData, error: tempError } = await supabase
        .from('user_two_factor_temp')
        .select('*')
        .eq('user_id', userId)
        .single()

      if (tempError || !tempData) {
        return { success: false, error: 'No 2FA setup found. Please start the setup process again.' }
      }

      // Decrypt secret and verify token
      const secret = await this.decryptSecret(tempData.secret_encrypted)
      const isValid = this.verifyTOTP(secret, token)

      if (!isValid) {
        return { success: false, error: 'Invalid verification code. Please try again.' }
      }

      // Move from temp to permanent storage
      const { error: insertError } = await supabase
        .from('user_two_factor')
        .insert({
          user_id: userId,
          secret_encrypted: tempData.secret_encrypted,
          enabled: true,
          backup_codes_hash: tempData.backup_codes_hash,
          created_at: new Date().toISOString()
        })

      if (insertError) {
        return { success: false, error: insertError.message }
      }

      // Clean up temp data
      await supabase
        .from('user_two_factor_temp')
        .delete()
        .eq('user_id', userId)

      securityAudit.log('2FA enabled', { userId }, 'medium')

      return { success: true, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Verify 2FA token during login
   */
  async verifyTwoFactor(userId: string, verification: TwoFactorVerification): Promise<{ valid: boolean; error: string | null }> {
    try {
      const { data: twoFactorData, error } = await supabase
        .from('user_two_factor')
        .select('*')
        .eq('user_id', userId)
        .eq('enabled', true)
        .single()

      if (error || !twoFactorData) {
        return { valid: false, error: '2FA not enabled for this account' }
      }

      let isValid = false

      if (verification.token) {
        // Verify TOTP token
        const secret = await this.decryptSecret(twoFactorData.secret_encrypted)
        isValid = this.verifyTOTP(secret, verification.token)
      } else if (verification.backupCode) {
        // Verify backup code
        const hashedCode = await this.hashBackupCode(verification.backupCode)
        isValid = twoFactorData.backup_codes_hash.includes(hashedCode)

        if (isValid) {
          // Remove used backup code
          const updatedCodes = twoFactorData.backup_codes_hash.filter(code => code !== hashedCode)
          await supabase
            .from('user_two_factor')
            .update({ backup_codes_hash: updatedCodes })
            .eq('user_id', userId)

          securityAudit.log('2FA backup code used', { userId }, 'medium')
        }
      }

      if (isValid) {
        // Update last used timestamp
        await supabase
          .from('user_two_factor')
          .update({ last_used_at: new Date().toISOString() })
          .eq('user_id', userId)

        securityAudit.log('2FA verification successful', { userId }, 'low')
      } else {
        securityAudit.log('2FA verification failed', { userId }, 'medium')
      }

      return { valid: isValid, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { valid: false, error: errorMessage }
    }
  }

  /**
   * Disable 2FA for user
   */
  async disableTwoFactor(userId: string, token: string): Promise<{ success: boolean; error: string | null }> {
    try {
      // Verify current token before disabling
      const verification = await this.verifyTwoFactor(userId, { token })
      
      if (!verification.valid) {
        return { success: false, error: 'Invalid verification code' }
      }

      const { error } = await supabase
        .from('user_two_factor')
        .delete()
        .eq('user_id', userId)

      if (error) {
        return { success: false, error: error.message }
      }

      securityAudit.log('2FA disabled', { userId }, 'medium')

      return { success: true, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { success: false, error: errorMessage }
    }
  }

  /**
   * Check if user has 2FA enabled
   */
  async isTwoFactorEnabled(userId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('user_two_factor')
        .select('enabled')
        .eq('user_id', userId)
        .eq('enabled', true)
        .single()

      return !error && !!data
    } catch (error) {
      return false
    }
  }

  /**
   * Generate random secret for TOTP
   */
  private generateSecret(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567'
    let secret = ''
    for (let i = 0; i < 32; i++) {
      secret += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return secret
  }

  /**
   * Generate backup codes
   */
  private generateBackupCodes(): string[] {
    const codes: string[] = []
    for (let i = 0; i < 10; i++) {
      const code = Math.random().toString(36).substring(2, 10).toUpperCase()
      codes.push(code)
    }
    return codes
  }

  /**
   * Verify TOTP token
   */
  private verifyTOTP(secret: string, token: string): boolean {
    // This is a simplified TOTP verification
    // In a real implementation, you'd use a proper TOTP library
    const timeStep = Math.floor(Date.now() / 30000)
    
    // Check current time step and previous/next for clock drift
    for (let i = -1; i <= 1; i++) {
      const expectedToken = this.generateTOTP(secret, timeStep + i)
      if (expectedToken === token) {
        return true
      }
    }
    
    return false
  }

  /**
   * Generate TOTP token (simplified implementation)
   */
  private generateTOTP(secret: string, timeStep: number): string {
    // This is a very simplified TOTP generation
    // In production, use a proper TOTP library like 'otplib'
    const hash = btoa(secret + timeStep.toString()).slice(0, 6)
    return hash.replace(/[^0-9]/g, '').padStart(6, '0').slice(0, 6)
  }

  /**
   * Encrypt secret for storage
   */
  private async encryptSecret(secret: string): Promise<string> {
    // In production, use proper encryption
    return btoa(secret)
  }

  /**
   * Decrypt secret from storage
   */
  private async decryptSecret(encryptedSecret: string): Promise<string> {
    // In production, use proper decryption
    return atob(encryptedSecret)
  }

  /**
   * Hash backup code for storage
   */
  private async hashBackupCode(code: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(code)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }
}

export const twoFactorAuth = TwoFactorAuthService.getInstance()
