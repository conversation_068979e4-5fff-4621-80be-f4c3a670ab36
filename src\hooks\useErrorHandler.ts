import { useCallback } from 'react';
import { toast } from 'sonner';
import { handleError, logError, AppError } from '@/lib/errors';

export const useErrorHandler = () => {
  const showError = useCallback((error: unknown, context?: string) => {
    const appError = handleError(error);
    logError(appError, context);
    
    toast.error(appError.userMessage || appError.message, {
      description: context ? `Context: ${context}` : undefined,
      duration: 5000,
    });
    
    return appError;
  }, []);

  const showSuccess = useCallback((message: string, description?: string) => {
    toast.success(message, {
      description,
      duration: 3000,
    });
  }, []);

  const showWarning = useCallback((message: string, description?: string) => {
    toast.warning(message, {
      description,
      duration: 4000,
    });
  }, []);

  const showInfo = useCallback((message: string, description?: string) => {
    toast.info(message, {
      description,
      duration: 3000,
    });
  }, []);

  return {
    showError,
    showSuccess,
    showWarning,
    showInfo,
  };
};
