import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/lib/supabase';
import { config } from '@/lib/config';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { SEOAnalyzer, SEOAnalysisResult } from '@/lib/seoAnalysis';
import { AnalyticsStats, StatusVariant } from '@/types';
import { BarChart3, TrendingUp, Globe, Users, RefreshCw, AlertCircle, CheckCircle, Target, FileText, Link } from 'lucide-react';

const AnalyticsDashboard: React.FC = () => {
  const [stats, setStats] = useState<AnalyticsStats>({ totalJobs: 0, completedJobs: 0, totalData: 0, avgWordCount: 0 });
  const [seoData, setSeoData] = useState<SEOAnalysisResult | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [statsLoading, setStatsLoading] = useState<boolean>(true);
  const [seoError, setSeoError] = useState<string | null>(null);
  const { showError, showSuccess } = useErrorHandler();
  const seoAnalyzer = new SEOAnalyzer();

  const fetchStats = async () => {
    setStatsLoading(true);
    try {
      const { data: jobs, error: jobsError } = await supabase.from('scraping_jobs').select('*');
      if (jobsError) throw new Error(`Failed to fetch jobs: ${jobsError.message}`);

      const { data: scraped, error: scrapedError } = await supabase.from('scraped_data').select('metadata');
      if (scrapedError) throw new Error(`Failed to fetch scraped data: ${scrapedError.message}`);

      const totalJobs = jobs?.length || 0;
      const completedJobs = jobs?.filter(j => j.status === 'completed').length || 0;
      const totalData = scraped?.length || 0;
      const avgWordCount = totalData > 0
        ? scraped?.reduce((acc, item) => acc + (item.metadata?.wordCount || 0), 0) / totalData
        : 0;

      setStats({ totalJobs, completedJobs, totalData, avgWordCount: Math.round(avgWordCount) });
    } catch (error) {
      showError(error, 'Fetching analytics stats');
    } finally {
      setStatsLoading(false);
    }
  };

  const runSEOAnalysis = async () => {
    setLoading(true);
    setSeoError(null);

    try {
      // Fetch scraped data for analysis
      const { data: scrapedData, error: dataError } = await supabase
        .from('scraped_data')
        .select('url, title, content, metadata');

      if (dataError) throw new Error(`Failed to fetch data: ${dataError.message}`);

      if (!scrapedData || scrapedData.length === 0) {
        throw new Error('No scraped data available for analysis. Please scrape some websites first.');
      }

      // Run local SEO analysis
      const analysisResult = seoAnalyzer.analyzeData(scrapedData);
      setSeoData(analysisResult);
      showSuccess(`SEO analysis completed for ${scrapedData.length} pages`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'SEO analysis failed';
      setSeoError(errorMessage);
      showError(error, 'SEO Analysis');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-blue-600 text-sm font-medium">Total Jobs</p>
                <p className="text-2xl font-bold text-blue-800">{stats.totalJobs}</p>
              </div>
              <Globe className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-green-600 text-sm font-medium">Completed</p>
                <p className="text-2xl font-bold text-green-800">{stats.completedJobs}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-purple-600 text-sm font-medium">Data Points</p>
                <p className="text-2xl font-bold text-purple-800">{stats.totalData}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-orange-600 text-sm font-medium">Avg Words</p>
                <p className="text-2xl font-bold text-orange-800">{stats.avgWordCount}</p>
              </div>
              <Users className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            SEO Analysis
            <Button onClick={runSEOAnalysis} disabled={loading} size="sm">
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Analyze
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <RefreshCw className="h-6 w-6 animate-spin mr-2" />
              <span>Running SEO analysis...</span>
            </div>
          ) : seoError ? (
            <div className="flex items-center space-x-2 p-4 bg-destructive/10 border border-destructive/20 rounded-md">
              <AlertCircle className="h-5 w-5 text-destructive" />
              <div>
                <p className="font-medium text-destructive">Analysis Failed</p>
                <p className="text-sm text-destructive/80">{seoError}</p>
              </div>
            </div>
          ) : seoData ? (
            <div className="space-y-6">
              {/* Overall Score */}
              <div className="flex items-center space-x-4 p-4 bg-muted/50 rounded-lg">
                <div className="text-4xl font-bold text-primary">{seoData.score}/100</div>
                <div className="flex-1">
                  <p className="font-semibold text-lg">Overall SEO Score</p>
                  <Badge variant={seoData.score > 80 ? 'default' : seoData.score > 60 ? 'secondary' : 'destructive'}>
                    {seoData.score > 80 ? 'Excellent' : seoData.score > 60 ? 'Good' : 'Needs Work'}
                  </Badge>
                  <Progress value={seoData.score} className="mt-2" />
                </div>
              </div>

              {/* Detailed Analysis Tabs */}
              <Tabs defaultValue="overview" className="w-full">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger value="overview">Overview</TabsTrigger>
                  <TabsTrigger value="titles">Titles</TabsTrigger>
                  <TabsTrigger value="content">Content</TabsTrigger>
                  <TabsTrigger value="technical">Technical</TabsTrigger>
                  <TabsTrigger value="keywords">Keywords</TabsTrigger>
                </TabsList>

                <TabsContent value="overview" className="space-y-4">
                  <div className="grid md:grid-cols-2 gap-4">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2 text-destructive">
                          <AlertCircle className="h-5 w-5" />
                          <span>Issues Found ({seoData.issues?.length || 0})</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {seoData.issues?.length > 0 ? (
                          <ul className="space-y-2">
                            {seoData.issues.map((issue: string, i: number) => (
                              <li key={i} className="flex items-start space-x-2">
                                <AlertCircle className="h-4 w-4 text-destructive mt-0.5 flex-shrink-0" />
                                <span className="text-sm">{issue}</span>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-muted-foreground">No issues found</p>
                        )}
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center space-x-2 text-primary">
                          <CheckCircle className="h-5 w-5" />
                          <span>Recommendations ({seoData.recommendations?.length || 0})</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        {seoData.recommendations?.length > 0 ? (
                          <ul className="space-y-2">
                            {seoData.recommendations.map((rec: string, i: number) => (
                              <li key={i} className="flex items-start space-x-2">
                                <CheckCircle className="h-4 w-4 text-primary mt-0.5 flex-shrink-0" />
                                <span className="text-sm">{rec}</span>
                              </li>
                            ))}
                          </ul>
                        ) : (
                          <p className="text-sm text-muted-foreground">No recommendations available</p>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                </TabsContent>

                <TabsContent value="titles" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <FileText className="h-5 w-5" />
                        <span>Title Analysis</span>
                        <Badge variant="outline">{seoData.details.titleAnalysis.score}/100</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Progress value={seoData.details.titleAnalysis.score} className="mb-4" />
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">Issues:</h4>
                          {seoData.details.titleAnalysis.issues.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.titleAnalysis.issues.map((issue, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <AlertCircle className="h-3 w-3 text-destructive mt-1 flex-shrink-0" />
                                  <span>{issue}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No title issues found</p>
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Recommendations:</h4>
                          {seoData.details.titleAnalysis.recommendations.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.titleAnalysis.recommendations.map((rec, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <CheckCircle className="h-3 w-3 text-primary mt-1 flex-shrink-0" />
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No recommendations</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="content" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <FileText className="h-5 w-5" />
                        <span>Content Analysis</span>
                        <Badge variant="outline">{seoData.details.contentAnalysis.score}/100</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Progress value={seoData.details.contentAnalysis.score} className="mb-4" />
                      <div className="grid md:grid-cols-3 gap-4 mb-4">
                        <div className="text-center p-3 bg-muted/50 rounded-lg">
                          <div className="text-2xl font-bold text-primary">{seoData.details.contentAnalysis.wordCount}</div>
                          <div className="text-sm text-muted-foreground">Total Words</div>
                        </div>
                        <div className="text-center p-3 bg-muted/50 rounded-lg">
                          <div className="text-2xl font-bold text-primary">{Math.round(seoData.details.contentAnalysis.readabilityScore)}</div>
                          <div className="text-sm text-muted-foreground">Readability Score</div>
                        </div>
                        <div className="text-center p-3 bg-muted/50 rounded-lg">
                          <div className="text-2xl font-bold text-primary">{seoData.details.contentAnalysis.score}</div>
                          <div className="text-sm text-muted-foreground">Content Score</div>
                        </div>
                      </div>
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">Issues:</h4>
                          {seoData.details.contentAnalysis.issues.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.contentAnalysis.issues.map((issue, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <AlertCircle className="h-3 w-3 text-destructive mt-1 flex-shrink-0" />
                                  <span>{issue}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No content issues found</p>
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Recommendations:</h4>
                          {seoData.details.contentAnalysis.recommendations.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.contentAnalysis.recommendations.map((rec, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <CheckCircle className="h-3 w-3 text-primary mt-1 flex-shrink-0" />
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No recommendations</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="technical" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Link className="h-5 w-5" />
                        <span>Technical SEO</span>
                        <Badge variant="outline">{seoData.details.technicalSEO.score}/100</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Progress value={seoData.details.technicalSEO.score} className="mb-4" />
                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">Issues:</h4>
                          {seoData.details.technicalSEO.issues.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.technicalSEO.issues.map((issue, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <AlertCircle className="h-3 w-3 text-destructive mt-1 flex-shrink-0" />
                                  <span>{issue}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No technical issues found</p>
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Recommendations:</h4>
                          {seoData.details.technicalSEO.recommendations.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.technicalSEO.recommendations.map((rec, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <CheckCircle className="h-3 w-3 text-primary mt-1 flex-shrink-0" />
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No recommendations</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="keywords" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle className="flex items-center space-x-2">
                        <Target className="h-5 w-5" />
                        <span>Keyword Analysis</span>
                        <Badge variant="outline">{seoData.details.keywordAnalysis.score}/100</Badge>
                      </CardTitle>
                    </CardHeader>
                    <CardContent>
                      <Progress value={seoData.details.keywordAnalysis.score} className="mb-4" />

                      {seoData.details.keywordAnalysis.topKeywords.length > 0 && (
                        <div className="mb-6">
                          <h4 className="font-medium mb-3">Top Keywords:</h4>
                          <div className="grid gap-2">
                            {seoData.details.keywordAnalysis.topKeywords.map((keyword, i) => (
                              <div key={i} className="flex items-center justify-between p-2 bg-muted/50 rounded">
                                <span className="font-medium">{keyword.keyword}</span>
                                <div className="flex items-center space-x-2">
                                  <Badge variant="outline">{keyword.frequency}x</Badge>
                                  <Badge variant="secondary">{keyword.density.toFixed(1)}%</Badge>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      <div className="grid md:grid-cols-2 gap-4">
                        <div>
                          <h4 className="font-medium mb-2">Issues:</h4>
                          {seoData.details.keywordAnalysis.issues.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.keywordAnalysis.issues.map((issue, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <AlertCircle className="h-3 w-3 text-destructive mt-1 flex-shrink-0" />
                                  <span>{issue}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No keyword issues found</p>
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Recommendations:</h4>
                          {seoData.details.keywordAnalysis.recommendations.length > 0 ? (
                            <ul className="text-sm space-y-1">
                              {seoData.details.keywordAnalysis.recommendations.map((rec, i) => (
                                <li key={i} className="flex items-start space-x-2">
                                  <CheckCircle className="h-3 w-3 text-primary mt-1 flex-shrink-0" />
                                  <span>{rec}</span>
                                </li>
                              ))}
                            </ul>
                          ) : (
                            <p className="text-sm text-muted-foreground">No recommendations</p>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground mb-2">Click "Analyze" to run SEO analysis on your scraped data.</p>
              <p className="text-sm text-muted-foreground">Make sure you have scraped some websites first.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AnalyticsDashboard;