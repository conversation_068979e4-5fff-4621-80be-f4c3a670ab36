// Account security and recovery utilities

import { supabase } from './supabase'
import { securityAudit } from './security'

export interface LoginAttempt {
  id: string
  email: string
  ip_address: string
  user_agent: string
  success: boolean
  attempted_at: string
  failure_reason?: string
}

export interface AccountLockout {
  id: string
  email: string
  locked_at: string
  locked_until: string
  attempt_count: number
  reason: string
}

export interface SecurityQuestion {
  id: string
  question: string
  answer_hash: string
}

export interface RecoveryMethod {
  type: 'email' | 'security_questions' | 'backup_codes'
  enabled: boolean
  verified: boolean
  created_at: string
}

class AccountSecurityService {
  private static instance: AccountSecurityService
  private readonly MAX_LOGIN_ATTEMPTS = 5
  private readonly LOCKOUT_DURATION_MINUTES = 30
  private readonly RATE_LIMIT_WINDOW_MINUTES = 15

  static getInstance(): AccountSecurityService {
    if (!AccountSecurityService.instance) {
      AccountSecurityService.instance = new AccountSecurityService()
    }
    return AccountSecurityService.instance
  }

  /**
   * Check if account is locked out
   */
  async isAccountLocked(email: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('account_lockouts')
        .select('*')
        .eq('email', email)
        .gte('locked_until', new Date().toISOString())
        .single()

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('Error checking account lockout:', error)
        return false
      }

      return !!data
    } catch (error) {
      console.error('Error checking account lockout:', error)
      return false
    }
  }

  /**
   * Record login attempt
   */
  async recordLoginAttempt(
    email: string, 
    success: boolean, 
    ipAddress: string, 
    userAgent: string,
    failureReason?: string
  ): Promise<void> {
    try {
      const attempt: Omit<LoginAttempt, 'id'> = {
        email,
        ip_address: ipAddress,
        user_agent: userAgent,
        success,
        attempted_at: new Date().toISOString(),
        failure_reason: failureReason
      }

      await supabase.from('login_attempts').insert(attempt)

      if (!success) {
        await this.checkAndApplyLockout(email, ipAddress)
      }

      securityAudit.log('Login attempt recorded', { 
        email, 
        success, 
        ipAddress,
        failureReason 
      }, success ? 'low' : 'medium')
    } catch (error) {
      console.error('Error recording login attempt:', error)
    }
  }

  /**
   * Check recent failed attempts and apply lockout if necessary
   */
  private async checkAndApplyLockout(email: string, ipAddress: string): Promise<void> {
    try {
      const windowStart = new Date()
      windowStart.setMinutes(windowStart.getMinutes() - this.RATE_LIMIT_WINDOW_MINUTES)

      const { data: recentAttempts, error } = await supabase
        .from('login_attempts')
        .select('*')
        .eq('email', email)
        .eq('success', false)
        .gte('attempted_at', windowStart.toISOString())
        .order('attempted_at', { ascending: false })

      if (error) {
        console.error('Error checking recent attempts:', error)
        return
      }

      if (recentAttempts && recentAttempts.length >= this.MAX_LOGIN_ATTEMPTS) {
        await this.lockAccount(email, ipAddress, recentAttempts.length)
      }
    } catch (error) {
      console.error('Error checking lockout conditions:', error)
    }
  }

  /**
   * Lock account due to suspicious activity
   */
  private async lockAccount(email: string, ipAddress: string, attemptCount: number): Promise<void> {
    try {
      const lockedUntil = new Date()
      lockedUntil.setMinutes(lockedUntil.getMinutes() + this.LOCKOUT_DURATION_MINUTES)

      const lockout: Omit<AccountLockout, 'id'> = {
        email,
        locked_at: new Date().toISOString(),
        locked_until: lockedUntil.toISOString(),
        attempt_count: attemptCount,
        reason: `Too many failed login attempts from IP: ${ipAddress}`
      }

      await supabase.from('account_lockouts').insert(lockout)

      securityAudit.log('Account locked', { 
        email, 
        ipAddress, 
        attemptCount,
        lockedUntil: lockedUntil.toISOString()
      }, 'high')
    } catch (error) {
      console.error('Error locking account:', error)
    }
  }

  /**
   * Unlock account manually (admin function)
   */
  async unlockAccount(email: string, adminUserId: string): Promise<{ error: string | null }> {
    try {
      const { error } = await supabase
        .from('account_lockouts')
        .delete()
        .eq('email', email)

      if (error) {
        return { error: error.message }
      }

      securityAudit.log('Account unlocked manually', { 
        email, 
        adminUserId 
      }, 'medium')

      return { error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { error: errorMessage }
    }
  }

  /**
   * Get account lockout information
   */
  async getAccountLockoutInfo(email: string): Promise<AccountLockout | null> {
    try {
      const { data, error } = await supabase
        .from('account_lockouts')
        .select('*')
        .eq('email', email)
        .gte('locked_until', new Date().toISOString())
        .single()

      if (error && error.code !== 'PGRST116') {
        console.error('Error getting lockout info:', error)
        return null
      }

      return data || null
    } catch (error) {
      console.error('Error getting lockout info:', error)
      return null
    }
  }

  /**
   * Generate backup recovery codes
   */
  async generateBackupCodes(userId: string): Promise<{ codes: string[]; error: string | null }> {
    try {
      const codes = Array.from({ length: 10 }, () => 
        Math.random().toString(36).substring(2, 10).toUpperCase()
      )

      const hashedCodes = await Promise.all(
        codes.map(async (code) => {
          // In a real implementation, you'd use a proper hashing function
          return btoa(code) // Simple base64 encoding for demo
        })
      )

      const { error } = await supabase
        .from('backup_codes')
        .insert(
          hashedCodes.map(hash => ({
            user_id: userId,
            code_hash: hash,
            used: false,
            created_at: new Date().toISOString()
          }))
        )

      if (error) {
        return { codes: [], error: error.message }
      }

      securityAudit.log('Backup codes generated', { userId }, 'low')
      return { codes, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { codes: [], error: errorMessage }
    }
  }

  /**
   * Verify backup code
   */
  async verifyBackupCode(userId: string, code: string): Promise<{ valid: boolean; error: string | null }> {
    try {
      const hashedCode = btoa(code)

      const { data, error } = await supabase
        .from('backup_codes')
        .select('*')
        .eq('user_id', userId)
        .eq('code_hash', hashedCode)
        .eq('used', false)
        .single()

      if (error && error.code !== 'PGRST116') {
        return { valid: false, error: error.message }
      }

      if (!data) {
        return { valid: false, error: null }
      }

      // Mark code as used
      await supabase
        .from('backup_codes')
        .update({ used: true, used_at: new Date().toISOString() })
        .eq('id', data.id)

      securityAudit.log('Backup code used', { userId }, 'medium')
      return { valid: true, error: null }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error'
      return { valid: false, error: errorMessage }
    }
  }

  /**
   * Clean up expired lockouts and old login attempts
   */
  async cleanupExpiredData(): Promise<void> {
    try {
      const now = new Date().toISOString()
      const oldDataCutoff = new Date()
      oldDataCutoff.setDate(oldDataCutoff.getDate() - 30) // Keep 30 days of data

      // Remove expired lockouts
      await supabase
        .from('account_lockouts')
        .delete()
        .lt('locked_until', now)

      // Remove old login attempts
      await supabase
        .from('login_attempts')
        .delete()
        .lt('attempted_at', oldDataCutoff.toISOString())

      securityAudit.log('Security data cleanup completed', {}, 'low')
    } catch (error) {
      console.error('Error during security data cleanup:', error)
    }
  }
}

export const accountSecurity = AccountSecurityService.getInstance()
