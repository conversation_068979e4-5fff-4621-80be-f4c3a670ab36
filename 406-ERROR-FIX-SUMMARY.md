# 🔧 406 Error Fix Summary

## Problem
You were experiencing a **406 (Not Acceptable)** error when querying the `user_settings` table in Supabase:
```
GET https://rclikclltlyzyojjttqv.supabase.co/rest/v1/user_settings?select=*&user_id=eq.d4707a14-ee21-4074-b9b0-1f4d7022cf6b 406 (Not Acceptable)
```

## Root Causes
The 406 error typically occurs due to:

1. **Incorrect Accept Headers** - PostgREST expects specific headers
2. **Missing Table Structure** - Tables or foreign key relationships not properly set up
3. **RLS Policy Issues** - Row Level Security blocking access
4. **Authentication Problems** - User not properly authenticated

## ✅ Solutions Implemented

### 1. Enhanced Supabase Client Configuration
**File:** `src/lib/supabase.ts`

**Changes Made:**
- Added PostgREST-specific Accept header: `application/vnd.pgrst.object+json`
- Added `Prefer: return=representation` header
- Enhanced authentication configuration
- Added database schema specification

**Before:**
```typescript
const supabase = createClient(config.supabase.url, config.supabase.anonKey, {
  global: {
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  },
});
```

**After:**
```typescript
const supabase = createClient(config.supabase.url, config.supabase.anonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  global: {
    headers: {
      'Accept': 'application/json, application/vnd.pgrst.object+json',
      'Content-Type': 'application/json',
      'Prefer': 'return=representation'
    },
  },
  db: {
    schema: 'public'
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
});
```

### 2. Database Structure Fix
**File:** `database/comprehensive_406_fix.sql`

**Key Features:**
- Creates all required tables with proper foreign key relationships
- Sets up Row Level Security (RLS) policies
- Creates automatic user profile and settings creation triggers
- Inserts default settings for existing users

### 3. Enhanced Testing Tool
**File:** `test-406-enhanced-fix.html`

**Features:**
- Comprehensive diagnostic testing
- Header configuration testing
- Authentication flow testing
- Database structure validation
- RLS policy testing
- Foreign key constraint checking

## 🚀 Next Steps

### Step 1: Run Database Fix Script
1. Open your Supabase Dashboard: https://supabase.com/dashboard/project/rclikclltlyzyojjttqv/sql
2. Go to **SQL Editor**
3. Copy and paste the entire contents of `database/comprehensive_406_fix.sql`
4. Click **"Run"** to execute the script

### Step 2: Test the Fix
1. Open the enhanced test file: `test-406-enhanced-fix.html` (already opened in your browser)
2. Click **"🔍 Test Connection"** to verify basic connectivity
3. Click **"👤 Sign In Anonymously"** to authenticate
4. Click **"Test User Settings"** to test the previously failing query
5. Run **"🔍 Full Diagnostic"** for comprehensive testing

### Step 3: Update Your Application
The enhanced Supabase client configuration in `src/lib/supabase.ts` should resolve the 406 errors in your React application.

## 🔍 Key Technical Details

### Why the 406 Error Occurred
- **PostgREST Specificity**: Supabase uses PostgREST, which expects specific Accept headers
- **Content Negotiation**: The server couldn't produce a response matching the client's Accept header
- **Missing Headers**: The original configuration was missing PostgREST-specific headers

### The Fix Explained
1. **Accept Header**: Added `application/vnd.pgrst.object+json` for PostgREST compatibility
2. **Prefer Header**: Added `return=representation` to ensure proper response format
3. **Combined Accept**: Used both JSON and PostgREST headers for maximum compatibility

### Testing Results Expected
After implementing the fixes, you should see:
- ✅ Connection tests pass
- ✅ Authentication works properly
- ✅ User settings queries return data without 406 errors
- ✅ All database tables are accessible
- ✅ RLS policies work correctly

## 🛡️ Security Considerations
- RLS policies ensure users can only access their own data
- Anonymous authentication is enabled for testing but should be configured per your security requirements
- Foreign key constraints maintain data integrity

## 📞 Support
If you continue to experience issues:
1. Check the browser console for detailed error messages
2. Use the diagnostic tools in the test file
3. Verify that the database script executed successfully
4. Ensure your Supabase project has the correct permissions

## 🎉 Success Indicators
You'll know the fix worked when:
- No more 406 errors in browser console
- User settings queries return data successfully
- All diagnostic tests pass in the test file
- Your React application can properly fetch user settings

---
**Last Updated:** 2025-06-25
**Status:** Ready for Testing
