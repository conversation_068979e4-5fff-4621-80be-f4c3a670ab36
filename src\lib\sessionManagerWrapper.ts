// Session management wrapper with graceful fallbacks
import { sessionManager } from './sessionManager';

export interface MockSession {
  id: string;
  session_token: string;
  device_info?: {
    browser: string;
    os: string;
  };
  ip_address: string;
  last_activity: string;
}

class SessionManagerWrapper {
  private static instance: Session<PERSON>anagerWrapper;
  private isSessionTableAvailable: boolean | null = null;

  static getInstance(): SessionManagerWrapper {
    if (!SessionManagerWrapper.instance) {
      SessionManagerWrapper.instance = new SessionManagerWrapper();
    }
    return SessionManagerWrapper.instance;
  }

  /**
   * Check if session management is available
   */
  private async checkSessionTableAvailability(): Promise<boolean> {
    if (this.isSessionTableAvailable !== null) {
      return this.isSessionTableAvailable;
    }

    try {
      // Try to get sessions with a valid UUID - if it fails, the table doesn't exist
      // Using a valid UUID format that won't exist but won't cause UUID parsing errors
      await sessionManager.getUserSessions('00000000-0000-0000-0000-000000000000');
      this.isSessionTableAvailable = true;
      return true;
    } catch (error: any) {
      // Check if it's a table not found error
      if (error?.code === '42P01' || error?.message?.includes('does not exist')) {
        this.isSessionTableAvailable = false;
        return false;
      }
      // Check if it's a UUID format error (shouldn't happen now, but just in case)
      if (error?.code === '22P02' || error?.message?.includes('invalid input syntax for type uuid')) {
        this.isSessionTableAvailable = false;
        return false;
      }
      // For other errors, assume table exists but there was another issue
      this.isSessionTableAvailable = true;
      return true;
    }
  }

  /**
   * Get user sessions with fallback to mock data
   */
  async getUserSessions(userId: string): Promise<MockSession[]> {
    const isAvailable = await this.checkSessionTableAvailability();
    
    if (!isAvailable) {
      // Return mock current session
      return [{
        id: 'current-session',
        session_token: 'current',
        device_info: {
          browser: this.getBrowserName(),
          os: this.getOperatingSystem()
        },
        ip_address: 'Current Session',
        last_activity: new Date().toISOString()
      }];
    }

    try {
      const sessions = await sessionManager.getUserSessions(userId);
      return sessions.map(session => ({
        id: session.id,
        session_token: session.session_token,
        device_info: session.device_info,
        ip_address: session.ip_address,
        last_activity: session.last_activity
      }));
    } catch (error) {
      console.error('Error fetching sessions:', error);
      // Fallback to mock session
      return [{
        id: 'current-session',
        session_token: 'current',
        device_info: {
          browser: this.getBrowserName(),
          os: this.getOperatingSystem()
        },
        ip_address: 'Current Session',
        last_activity: new Date().toISOString()
      }];
    }
  }

  /**
   * Terminate a session with graceful fallback
   */
  async terminateSession(sessionToken: string): Promise<void> {
    if (sessionToken === 'current') {
      throw new Error('Cannot terminate current session');
    }

    const isAvailable = await this.checkSessionTableAvailability();
    
    if (!isAvailable) {
      throw new Error('Session management is not available yet');
    }

    try {
      await sessionManager.terminateSession(sessionToken);
    } catch (error) {
      console.error('Error terminating session:', error);
      throw new Error('Failed to terminate session');
    }
  }

  /**
   * Terminate all user sessions with graceful fallback
   */
  async terminateAllUserSessions(userId: string): Promise<void> {
    const isAvailable = await this.checkSessionTableAvailability();
    
    if (!isAvailable) {
      throw new Error('Session management is not available yet');
    }

    try {
      await sessionManager.terminateAllUserSessions(userId);
    } catch (error) {
      console.error('Error terminating all sessions:', error);
      throw new Error('Failed to terminate all sessions');
    }
  }

  /**
   * Get browser name from user agent
   */
  private getBrowserName(): string {
    const userAgent = navigator.userAgent;
    
    if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
      return 'Chrome';
    } else if (userAgent.includes('Firefox')) {
      return 'Firefox';
    } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
      return 'Safari';
    } else if (userAgent.includes('Edg')) {
      return 'Edge';
    } else if (userAgent.includes('Opera') || userAgent.includes('OPR')) {
      return 'Opera';
    } else {
      return 'Unknown Browser';
    }
  }

  /**
   * Get operating system from user agent
   */
  private getOperatingSystem(): string {
    const platform = navigator.platform;
    const userAgent = navigator.userAgent;

    if (platform.includes('Win')) {
      return 'Windows';
    } else if (platform.includes('Mac')) {
      return 'macOS';
    } else if (platform.includes('Linux')) {
      return 'Linux';
    } else if (userAgent.includes('Android')) {
      return 'Android';
    } else if (userAgent.includes('iPhone') || userAgent.includes('iPad')) {
      return 'iOS';
    } else {
      return 'Unknown OS';
    }
  }
}

export const sessionManagerWrapper = SessionManagerWrapper.getInstance();
