import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Mail, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface EmailVerificationFormProps {
  email?: string;
  onVerificationComplete: () => void;
  onResendSuccess?: () => void;
}

const EmailVerificationForm: React.FC<EmailVerificationFormProps> = ({ 
  email, 
  onVerificationComplete,
  onResendSuccess 
}) => {
  const [loading, setLoading] = useState(false);
  const [resendLoading, setResendLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [resendCooldown, setResendCooldown] = useState(0);

  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (resendCooldown > 0) {
      interval = setInterval(() => {
        setResendCooldown(prev => prev - 1);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [resendCooldown]);

  useEffect(() => {
    // Listen for auth state changes to detect email verification
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user?.email_confirmed_at) {
          onVerificationComplete();
        }
      }
    );

    return () => subscription.unsubscribe();
  }, [onVerificationComplete]);

  const handleResendVerification = async () => {
    if (!email) {
      setError('Email address is required to resend verification');
      return;
    }

    setResendLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
        options: {
          emailRedirectTo: `${window.location.origin}/verify-email`
        }
      });

      if (error) {
        setError(error.message);
      } else {
        setSuccess('Verification email sent! Please check your inbox.');
        setResendCooldown(60); // 60 second cooldown
        onResendSuccess?.();
      }
    } catch (err) {
      setError('Failed to resend verification email. Please try again.');
    } finally {
      setResendLoading(false);
    }
  };

  const handleCheckVerification = async () => {
    setLoading(true);
    setError(null);

    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      if (error) {
        setError(error.message);
        return;
      }

      if (user?.email_confirmed_at) {
        setSuccess('Email verified successfully!');
        setTimeout(() => {
          onVerificationComplete();
        }, 1500);
      } else {
        setError('Email not yet verified. Please check your email and click the verification link.');
      }
    } catch (err) {
      setError('Failed to check verification status. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <Mail className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle className="text-xl">Verify Your Email</CardTitle>
        <CardDescription>
          {email ? (
            <>We've sent a verification link to <strong>{email}</strong></>
          ) : (
            'Please verify your email address to continue'
          )}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert className="border-red-200 bg-red-50">
            <XCircle className="h-4 w-4" />
            <AlertDescription className="text-red-700">{error}</AlertDescription>
          </Alert>
        )}

        {success && (
          <Alert className="border-green-200 bg-green-50">
            <CheckCircle className="h-4 w-4" />
            <AlertDescription className="text-green-700">{success}</AlertDescription>
          </Alert>
        )}

        <div className="text-sm text-gray-600 text-center space-y-2">
          <p>Click the verification link in your email to activate your account.</p>
          <p>The link will expire in 24 hours for security reasons.</p>
        </div>

        <div className="space-y-3">
          <Button
            onClick={handleCheckVerification}
            className="w-full"
            disabled={loading}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Checking Verification...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                I've Verified My Email
              </>
            )}
          </Button>

          <Button
            variant="outline"
            onClick={handleResendVerification}
            className="w-full"
            disabled={resendLoading || resendCooldown > 0 || !email}
          >
            {resendLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : resendCooldown > 0 ? (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend in {resendCooldown}s
              </>
            ) : (
              <>
                <RefreshCw className="mr-2 h-4 w-4" />
                Resend Verification Email
              </>
            )}
          </Button>
        </div>

        <div className="text-xs text-gray-500 text-center space-y-1">
          <p>Didn't receive the email?</p>
          <ul className="space-y-1">
            <li>• Check your spam or junk folder</li>
            <li>• Make sure {email} is correct</li>
            <li>• Try resending the verification email</li>
          </ul>
        </div>

        <div className="pt-4 border-t">
          <Button
            variant="ghost"
            onClick={() => supabase.auth.signOut()}
            className="w-full text-sm"
          >
            Sign Out and Try Different Email
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default EmailVerificationForm;
